import { AsyncPipe, CommonModule, Location } from '@angular/common';
import { Component, DEFAULT_CURRENCY_CODE, Inject, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, FormsModule, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { MachineSelectorComponent } from '@cms/fe-components';
import { CreateHandpay, HandpayType } from '@cms/machine-handpays';
import { ErrorUtil, TgLoggerFactory, WorkstationTransactionPointService } from '@tronius/frontend-common';
import { TgMainPanelModule } from '@tronius/frontend-ui';
import { ConnectionStatus } from '@tronius/shared-common';
import { MachineCurrent, MachineStatus } from '@tronius/shared-domain';
import { format } from 'date-fns';
import { fromZonedTime } from 'date-fns-tz';
import { Observable, combineLatest, map, tap } from 'rxjs';

import { BackendService } from '../../../core/backend/backend.service';
import { ReportFormsService } from '../../../core/services/report-forms.service';
import { HandpayTypePipe } from '../../handpay-page/handpay/type/handpay-type.pipe';

interface FormGroupType {
    type: FormControl<HandpayType>;
    amount: FormControl<number | null>;
    machineId: FormControl<string | null>;
    gamingDay: FormControl<Date>;
    timestamp: FormControl<string>;
    comment: FormControl<string | null>;
}

@Component({
    selector: 'app-create-handpay',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        MatButtonModule,
        MatDividerModule,
        MatDatepickerModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatSnackBarModule,
        TgMainPanelModule,
        AsyncPipe,
        ReactiveFormsModule,
        HandpayTypePipe,
        MachineSelectorComponent
    ],
    templateUrl: './create-handpay.component.html',
    styleUrl: './create-handpay.component.scss'
})
export class CreateHandpayComponent implements OnInit {
    private static readonly logger = TgLoggerFactory.getLogger(CreateHandpayComponent.name);

    protected readonly ConnectionStatus = ConnectionStatus;
    protected readonly HandpayType = HandpayType;
    protected readonly MachineStatus = MachineStatus;

    protected isCageOpen$: Observable<boolean>;
    protected machineStates$!: Observable<MachineCurrent[]>;
    protected handpayForm!: FormGroup<FormGroupType>;

    constructor(
        @Inject(DEFAULT_CURRENCY_CODE) protected readonly currencyCode: string,
        private readonly route: ActivatedRoute,
        private readonly router: Router,
        private readonly backendService: BackendService,
        private readonly snackBar: MatSnackBar,
        private readonly location: Location,
        private readonly workstationTransactionPointService: WorkstationTransactionPointService,
        private readonly reportFormsService: ReportFormsService
    ) {
        this.isCageOpen$ = this.workstationTransactionPointService.isCageOpenObservableOrRedirect();
    }

    ngOnInit(): void {
        this.machineStates$ = combineLatest([this.backendService.getMachineState(), this.route.queryParamMap]).pipe(
            tap(([_, params]) => {
                const machineId = params.get('machineId') || null;
                this.createHandpayForm(HandpayType.RedeemCredits, machineId);
            }),
            map(([machinesResponse]) => machinesResponse.items)
        );
    }

    protected onSubmit(): void {
        const { timestamp, gamingDay, ...formData } = this.handpayForm.getRawValue();
        const { timezone } = this.reportFormsService.getCurrentGamingDayData();
        const createHandpayRequest = {
            ...formData,
            gamingDay: format(gamingDay, 'yyyy-MM-dd'),
            pendingAt: fromZonedTime(new Date(timestamp), timezone).getTime()
        };
        this.backendService.createHandpay(createHandpayRequest as unknown as CreateHandpay).subscribe({
            next: () => {
                this.snackBar.open($localize`Manual Handpay created.`, undefined, { duration: 5000 });
                void this.router.navigate(['/main/handpays/handpays']);
                this.handpayForm.controls.amount.setValue(null);
            },
            error: (error: unknown) => {
                CreateHandpayComponent.logger.error('Manual Handpay creation failed.', ErrorUtil.extractErrorMessage(error));
                this.snackBar.open($localize`Manual Handpay creation failed`, $localize`Close`);
            }
        });
    }

    protected onCancel(): void {
        if (this.router.navigated) {
            this.location.back();
        }
    }

    private createHandpayForm(handpayType: HandpayType, machineId: string | null): void {
        const gamingDayData = this.reportFormsService.getCurrentGamingDayData();
        const gamingDayDate = new Date(gamingDayData.date);

        const noFutureGamingDayValidator = (control: AbstractControl<Date, never>): ValidationErrors | null => {
            return control.value.getTime() <= gamingDayDate.getTime() ? null : { gamingDay: { value: control.value, gamingDayDate } };
        };

        const timestampFormatValidator = Validators.pattern(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/u);

        const noFutureTimestampValidator = (control: AbstractControl<string, never>): ValidationErrors | null => {
            return new Date(control.value).getTime() <= Date.now() ? null : { future: { timestamp: control.value } };
        };

        const form = new FormGroup({
            type: new FormControl<HandpayType>(handpayType, { nonNullable: true }),
            amount: new FormControl<number | null>(null, [Validators.required, Validators.min(0)]),
            machineId: new FormControl<string | null>(machineId, Validators.required),
            gamingDay: new FormControl<Date>(new Date(gamingDayData.date), {
                validators: [Validators.required, noFutureGamingDayValidator],
                nonNullable: true
            }),
            timestamp: new FormControl<string>(format(new Date(), "yyyy-MM-dd'T'HH:mm:ss"), {
                validators: [Validators.required, timestampFormatValidator, noFutureTimestampValidator],
                nonNullable: true
            }),
            comment: new FormControl<string | null>('')
        });

        this.handpayForm = form;
    }
}
