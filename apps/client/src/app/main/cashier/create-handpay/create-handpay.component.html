<tg-main-panel
    *ngIf="isCageOpen$ | async"
    [contentPadding]="true"
    [fullHeight]="true"
    [scrollable]="true"
    panelTitle="Create Handpay"
    i18n-panelTitle
>
    <form [formGroup]="handpayForm" *ngIf="machineStates$ | async as machineStates" (ngSubmit)="onSubmit()">
        <mat-form-field class="machine">
            <mat-label i18n>Machine</mat-label>
            <cms-machine-selector formControlName="machineId" required></cms-machine-selector>
            @if (handpayForm.controls.machineId.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            }
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Type</mat-label>
            <mat-select formControlName="type" required>
                @for (handpayType of HandpayType | keyvalue; track handpayType) {
                    <mat-option [value]="handpayType.value">
                        {{ handpayType.value | handpayType }}
                    </mat-option>
                }
            </mat-select>
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Amount</mat-label>
            <span matSuffix>{{ currencyCode }} &nbsp;</span>
            <input matInput type="number" formControlName="amount" />
            @if (handpayForm.controls.amount.errors?.['required']) {
                <mat-error i18n>Amount is required!</mat-error>
            } @else if (handpayForm.controls.amount.errors?.['min']) {
                <mat-error i18n>Amount > 0 required!</mat-error>
            }
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Gaming Day</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="gamingDay" placeholder="MM/DD/YYYY" i18n-placeholder required />
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            @if (handpayForm.controls.gamingDay.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            } @else if (handpayForm.controls.gamingDay.errors?.gamingDay) {
                <mat-error i18n>Cannot create handpay for future gaming days</mat-error>
            }
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Timestamp [yyyy-MM-ddTHH:mm:ss]</mat-label>
            <input matInput type="text" formControlName="timestamp" required />
            @if (handpayForm.controls.timestamp.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            } @else if (handpayForm.controls.timestamp.errors?.timestamp) {
                <mat-error i18n>Timestamp should be in yyyy-MM-ddTHH:mm:ss format</mat-error>
            } @else if (handpayForm.controls.timestamp.errors?.future) {
                <mat-error i18n>Cannot create handpay for future timestamps</mat-error>
            }
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Note</mat-label>
            <input matInput type="text" formControlName="comment" />
        </mat-form-field>

        <div><hr /></div>

        <div>
            <button mat-flat-button type="button" (click)="onCancel()">
                <ng-container i18n>Cancel</ng-container>
            </button>
            <button mat-flat-button color="primary" type="submit" [disabled]="!handpayForm.valid">
                <ng-container i18n>Create</ng-container>
            </button>
        </div>
    </form>
</tg-main-panel>
