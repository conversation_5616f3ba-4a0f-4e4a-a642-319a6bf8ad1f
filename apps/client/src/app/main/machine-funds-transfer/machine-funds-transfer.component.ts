import { CommonModule } from '@angular/common';
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { Router, RouterModule } from '@angular/router';
import { MachineFundsTransfer, MachineFundsTransferStatus } from '@cms/machine-funds-transfers';
import { ErrorUtil, TgLoggerFactory } from '@tronius/frontend-common';
import {
    ConfirmationDialogComponent,
    TableRowMenuAction,
    TgFiltersModule,
    TgMainPanelModule,
    TgPagedDataSource,
    TgTableModule
} from '@tronius/frontend-ui';

import { MachineFundsTransferColumnsSource } from './machine-funds-transfer.columns';
import { MachineFundsTransferStatusPipe } from './status/machine-funds-transfer-status.pipe';
import { MachineFundsTransferTypePipe } from './type/machine-funds-transfer-type.pipe';
import { BackendService, MachineFundsTransferActionType } from '../../core/backend/backend.service';

@Component({
    selector: 'app-machine-funds-transfer',
    standalone: true,
    imports: [
        CommonModule,
        TgTableModule,
        TgMainPanelModule,
        TgFiltersModule,
        MatIconModule,
        MatButtonModule,
        MatSnackBarModule,
        MatTableModule,
        RouterModule,
        MachineFundsTransferStatusPipe,
        MachineFundsTransferTypePipe,
        MatDialogModule,
        ConfirmationDialogComponent
    ],
    templateUrl: './machine-funds-transfer.component.html'
})
export class MachineFundsTransferComponent {
    private static readonly logger = TgLoggerFactory.getLogger(MachineFundsTransferComponent.name);

    @ViewChild('dialogTemplate', { static: false }) dialogTemplate?: TemplateRef<ConfirmationDialogComponent>;

    protected readonly MachineFundsTransferStatus = MachineFundsTransferStatus;

    protected columnsSource = new MachineFundsTransferColumnsSource('machine-funds-transfer');
    protected dataSource = new TgPagedDataSource<MachineFundsTransfer>((request) => this.backendService.getMachineFundsTransfers(request));
    protected confirmationDialogTitle?: string;
    protected actions: Array<TableRowMenuAction<MachineFundsTransfer>> = this.createActions();

    constructor(
        private readonly backendService: BackendService,
        private readonly snackBar: MatSnackBar,
        private readonly dialog: MatDialog,
        private readonly router: Router
    ) {}

    protected actOnMachineFundsTransfer(action: MachineFundsTransferActionType, mft: MachineFundsTransfer): void {
        if (action === 'complete') {
            // Navigate to the completion page
            void this.router.navigate(['/main/machine-funds-transfer', mft.id, 'complete']);
            return;
        }

        // For cancel action, show confirmation dialog
        this.confirmationDialogTitle = `${this.dialogTitle(action)} (${mft.amount})`;

        if (this.dialogTemplate) {
            this.dialog
                .open(this.dialogTemplate)
                .afterClosed()
                .subscribe((result) => {
                    if (result) {
                        this.backendService.cancelMachineFundsTransfer(mft.id).subscribe({
                            next: () => {
                                this.snackBar.open($localize`Machine funds transfer canceled`, undefined, { duration: 5000 });
                                void this.dataSource.reload();
                            },
                            error: (error: unknown) => {
                                MachineFundsTransferComponent.logger.error(
                                    'Machine funds transfer cancellation failed',
                                    ErrorUtil.extractErrorMessage(error)
                                );
                                this.snackBar.open($localize`Machine funds transfer cancellation failed`, $localize`Close`);
                            }
                        });
                    }
                });
        }
    }

    private dialogTitle(action: MachineFundsTransferActionType): string {
        switch (action) {
            case 'complete':
                return $localize`Complete Machine Funds Transfer`;
            case 'cancel':
                return $localize`Cancel Machine Funds Transfer`;
            default:
                // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
                throw new Error(`Unknown machine-funds-transfer action: ${action}`);
        }
    }

    private createActions(): Array<TableRowMenuAction<MachineFundsTransfer>> {
        const executeAction = (actionType: MachineFundsTransferActionType) => (transfer: MachineFundsTransfer) =>
            this.actOnMachineFundsTransfer(actionType, transfer);
        const statusMatches = (status: MachineFundsTransferStatus) => (transfer: MachineFundsTransfer) => transfer.status === status;

        return [
            {
                title: $localize`Cancel`,
                icon: 'close',
                action: executeAction('cancel'),
                show: statusMatches(MachineFundsTransferStatus.Pending)
            },
            {
                title: $localize`Manually Complete`,
                icon: 'lock_open',
                action: executeAction('complete'),
                show: statusMatches(MachineFundsTransferStatus.Pending)
            }
        ];
    }
}
