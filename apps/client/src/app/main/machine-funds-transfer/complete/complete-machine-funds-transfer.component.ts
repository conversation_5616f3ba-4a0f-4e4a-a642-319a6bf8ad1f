import { AsyncPipe, CommonModule, Location } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ActivatedRoute, Router } from '@angular/router';
import { CompleteMachineFundsTransfer, MachineFundsTransfer } from '@cms/machine-funds-transfers';
import { ErrorUtil, TgLoggerFactory, WorkstationTransactionPointService } from '@tronius/frontend-common';
import { TgMainPanelModule } from '@tronius/frontend-ui';
import { format } from 'date-fns/format';
import { parse } from 'date-fns/parse';
import { Observable, switchMap, tap } from 'rxjs';

import { BackendService } from '../../../core/backend/backend.service';

// Not accounting for localization, good enough
const accountingDateFormat = 'yyyy-MM-dd HH:mm';

@Component({
    selector: 'app-complete-machine-funds-transfer',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        MatButtonModule,
        MatDatepickerModule,
        MatDividerModule,
        MatFormFieldModule,
        MatInputModule,
        MatSnackBarModule,
        TgMainPanelModule,
        AsyncPipe,
        ReactiveFormsModule
    ],
    templateUrl: './complete-machine-funds-transfer.component.html',
    styleUrl: './complete-machine-funds-transfer.component.scss'
})
export class CompleteMachineFundsTransferComponent implements OnInit {
    private static readonly logger = TgLoggerFactory.getLogger(CompleteMachineFundsTransferComponent.name);

    protected isCageOpen$: Observable<boolean>;
    protected completionForm!: FormGroup<{
        gamingDay: FormControl<Date>;
        accountingDate: FormControl<string>;
        comment: FormControl<string>;
    }>;
    protected machineFundsTransfer?: MachineFundsTransfer;

    constructor(
        private readonly route: ActivatedRoute,
        private readonly router: Router,
        private readonly fb: FormBuilder,
        private readonly backendService: BackendService,
        private readonly snackBar: MatSnackBar,
        private readonly location: Location,
        private readonly workstationTransactionPointService: WorkstationTransactionPointService
    ) {
        this.isCageOpen$ = this.workstationTransactionPointService.isCageOpenObservableOrRedirect();
    }

    ngOnInit(): void {
        this.route.paramMap
            .pipe(
                switchMap((params) => {
                    const id = params.get('id');
                    if (!id) {
                        throw new Error('Machine funds transfer ID is required');
                    }
                    return this.backendService.getMachineFundsTransferById(id);
                }),
                tap((transfer) => {
                    if (!transfer) {
                        throw new Error('Machine funds transfer not found');
                    }
                    this.machineFundsTransfer = transfer;
                    this.createCompletionForm();
                })
            )
            .subscribe({
                error: (error: unknown) => {
                    CompleteMachineFundsTransferComponent.logger.error(
                        'Failed to load machine funds transfer',
                        ErrorUtil.extractErrorMessage(error)
                    );
                    this.snackBar.open($localize`Failed to load machine funds transfer`, $localize`Close`);
                    void this.router.navigate(['/main/machine-funds-transfer']);
                }
            });
    }

    protected onSubmit(): void {
        if (!this.machineFundsTransfer) {
            return;
        }

        const formValue = this.completionForm.getRawValue();

        const completionData: CompleteMachineFundsTransfer = {
            id: this.machineFundsTransfer.id,
            gamingDay: this.formatGamingDay(formValue.gamingDay),
            accountingDate: parse(formValue.accountingDate, accountingDateFormat, new Date()),
            comment: formValue.comment || undefined
        };

        this.backendService.completeMachineFundsTransfer(completionData).subscribe({
            next: () => {
                this.snackBar.open($localize`Machine funds transfer completed successfully`, undefined, { duration: 5000 });
                void this.router.navigate(['/main/machine-funds-transfer']);
            },
            error: (error: unknown) => {
                const errorMessage = ErrorUtil.extractErrorMessage(error) || 'Machine funds transfer completion failed';
                CompleteMachineFundsTransferComponent.logger.error('Machine funds transfer completion failed', errorMessage);
                this.snackBar.open($localize`${errorMessage}`, $localize`Close`);
            }
        });
    }

    protected onCancel(): void {
        if (this.router.navigated) {
            this.location.back();
        }
    }

    private createCompletionForm(): void {
        const now = new Date();

        this.completionForm = this.fb.nonNullable.group({
            // Optimistic but good enough, validations are done on server
            gamingDay: [now, [Validators.required]],
            accountingDate: [
                format(now, accountingDateFormat),
                // Optimistic date format, but good enough, validations are done on server
                [Validators.required, Validators.pattern(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/u)]
            ],
            comment: ['']
        });
    }

    private formatGamingDay(date: Date): string {
        return format(date, 'yyyy-MM-dd');
    }
}
