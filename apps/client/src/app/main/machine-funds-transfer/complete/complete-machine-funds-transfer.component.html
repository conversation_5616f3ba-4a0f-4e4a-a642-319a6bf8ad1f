<tg-main-panel
    *ngIf="isCageOpen$ | async"
    [contentPadding]="true"
    [fullHeight]="true"
    [scrollable]="true"
    panelTitle="Manually Complete Machine Funds Transfer"
    i18n-panelTitle
>
    <form [formGroup]="completionForm" *ngIf="machineFundsTransfer" (ngSubmit)="onSubmit()">
        <!-- Machine Funds Transfer Info -->
        <div class="transfer-info">
            <h3 i18n>Transfer Details</h3>
            <p><strong i18n>ID:</strong> {{ machineFundsTransfer.id }}</p>
            <p><strong i18n>Amount:</strong> {{ machineFundsTransfer.amount | currency }}</p>
            <p><strong i18n>Type:</strong> {{ machineFundsTransfer.type }}</p>
            <p><strong i18n>Status:</strong> {{ machineFundsTransfer.status }}</p>
        </div>

        <div><hr /></div>

        <!-- Gaming Day -->
        <mat-form-field>
            <mat-label i18n>Gaming Day</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="gamingDay" placeholder="YYYY-MM-DD" i18n-placeholder required />
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            @if (completionForm.controls.gamingDay.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            } @else if (completionForm.controls.gamingDay.errors?.gamingDay) {
                <mat-error i18n>Cannot complete transfer for future gaming days</mat-error>
            }
        </mat-form-field>

        <!-- Accounting Date -->
        <mat-form-field>
            <mat-label i18n>Accounting date (yyyy-MM-dd HH:mm)</mat-label>
            <input matInput type="text" formControlName="accountingDate" required placeholder="yyyy-MM-dd HH:mm" i18n-placeholder />
            @if (completionForm.controls.accountingDate.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            }
            @if (completionForm.controls.accountingDate.errors?.pattern) {
                <mat-error i18n>Invalid date format</mat-error>
            }
        </mat-form-field>

        <!-- Comment -->
        <mat-form-field>
            <mat-label i18n>Comment</mat-label>
            <input matInput type="text" formControlName="comment" />
        </mat-form-field>

        <div><hr /></div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button mat-flat-button type="button" (click)="onCancel()">
                <ng-container i18n>Cancel</ng-container>
            </button>
            <button mat-flat-button color="primary" type="submit" [disabled]="!completionForm.valid">
                <ng-container i18n>Complete Transfer</ng-container>
            </button>
        </div>
    </form>
</tg-main-panel>
