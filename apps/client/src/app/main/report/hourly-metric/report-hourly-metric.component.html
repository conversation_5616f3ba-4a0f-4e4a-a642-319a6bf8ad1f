<tg-main-panel [contentPadding]="true" [fullHeight]="true" [scrollable]="true" panelTitle="Hourly Metrics Report" i18n-panelTitle>
    <ng-container mainPanelActions1>
        <button
            mat-flat-button
            printSectionId="print-report-hourly-metrics"
            ngxPrint
            [printTitle]="'Hourly Metrics Report'"
            [openNewTab]="true"
            [closeWindow]="false"
            [printStyle]="{
                th: { 'text-align': 'left' },
                td: { 'text-align': 'left' },
                '.cdk-column-actions': { display: 'none' },
                'mat-paginator': { display: 'none' },
                '.no-rows-state': { display: 'none' }
            }"
        >
            <mat-icon>printer</mat-icon>
            <span i18n>Print</span>
        </button>
    </ng-container>

    <form [formGroup]="form">
        <mat-form-field>
            <mat-label i18n>Gaming Day</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="gamingDay" required />
            <mat-hint i18n>MM/DD/YYYY</mat-hint>
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            @if (form.controls.gamingDay.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            }
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Start Hour</mat-label>
            <mat-select formControlName="startHourIndex" (selectionChange)="form.controls.endHourIndex.updateValueAndValidity()" required>
                @for (hour of startHours; track hour.value) {
                    <mat-option [value]="hour.value">{{ hour.label }}</mat-option>
                }
            </mat-select>
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>End Hour</mat-label>
            <mat-select formControlName="endHourIndex" required>
                @for (hour of endHours; track hour.value) {
                    <mat-option [value]="hour.value" *ngIf="hour.value >= (form.value.startHourIndex ?? startHours[0].value)">
                        {{ hour.label }}
                    </mat-option>
                }
            </mat-select>
            @if (form.controls.endHourIndex.errors?.greaterThanOtherControl?.otherControlName === 'startHourIndex') {
                <mat-error i18n>End hour should be greater than start hour</mat-error>
            }
        </mat-form-field>

        <button mat-flat-button color="primary" [disabled]="!form.valid" (click)="generateReport()" i18n>Generate Report</button>
    </form>

    <div *ngIf="requestData" id="print-report-hourly-metrics">
        <h1 i18n>
            Hourly Machine Performance {{ requestData.gamingDay | tgDate }} - from {{ startHours[requestData.startHourIndex].label }} to
            {{ endHours[requestData.endHourIndex].label }}
        </h1>
        @if (dataSourceTotal && columnsSourceTotal) {
            <h2 i18n>Total</h2>
            <app-report-metric-table [dataSource]="dataSourceTotal" [columnsSource]="columnsSourceTotal"></app-report-metric-table>
        }
        @if (dataSourceMachines && columnsSourceMachines) {
            <h2 i18n>Machines</h2>
            <app-report-metric-table [dataSource]="dataSourceMachines" [columnsSource]="columnsSourceMachines"></app-report-metric-table>
        }
    </div>
</tg-main-panel>
