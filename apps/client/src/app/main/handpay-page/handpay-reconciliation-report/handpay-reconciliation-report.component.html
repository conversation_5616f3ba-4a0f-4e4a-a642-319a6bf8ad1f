<tg-main-panel [contentPadding]="true" [fullHeight]="true" [scrollable]="false">
    <form [formGroup]="form" class="no-print">
        <mat-form-field>
            <mat-label i18n>Gaming Day</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="gamingDay" placeholder="MM/DD/YYYY" i18n-placeholder required />
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            @if (form.controls.gamingDay.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            }
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Handpay Type</mat-label>
            <mat-select formControlName="handpayType" placeholder="All" i18n-placeholder>
                <mat-option [value]="null" i18n>All</mat-option>
                @for (handpayType of handpayTypesArray; track handpayType) {
                    <mat-option [value]="handpayType">{{ handpayType | handpayType }}</mat-option>
                }
            </mat-select>
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Variance &gt; </mat-label>
            <input matInput type="number" formControlName="variance" step="0.01" required />
            @if (form.controls.variance.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            }
        </mat-form-field>

        <button mat-flat-button color="primary" [disabled]="!form.valid" (click)="generateReport()" i18n>
            <mat-icon>assessment</mat-icon>
            Generate Report
        </button>

        <button mat-flat-button color="primary" (click)="print()"><mat-icon>print</mat-icon> <span i18n>Print</span></button>
    </form>

    <div *ngIf="requestData && requestData.gamingDay" id="print-handpay-reconciliation-report">
        <div class="header">
            <div i18n class="operator-row print-only">{{ operatorName }}</div>
            <div class="title-row print-only">
                <div class="title" i18n>Handpay Reconciliation Report</div>
                <div class="generated-meta-info-container">
                    <div i18n class="label">Generated by:</div>
                    <div class="operator-and-time">{{ generatedBy }}, {{ generatedTime | tgDateTime }}</div>
                </div>
            </div>
            <div class="filters-row">
                <div class="filters print-only">
                    <div class="label-value">
                        <div i18n class="label">Gaming Day:</div>
                        <div class="value">
                            {{ requestData.gamingDay | tgDate }}
                        </div>
                    </div>
                    <div class="label-value">
                        <div i18n class="label">Handpay Type:</div>
                        <div class="value">
                            @if (requestData.handpayType) {
                                {{ requestData.handpayType | handpayType }}
                            } @else {
                                <ng-container i18n>All</ng-container>
                            }
                        </div>
                    </div>
                    <div class="label-value">
                        <div i18n class="label">Variance ></div>
                        <div class="value"><tg-primitive-text [value]="requestData.variance" type="currency"></tg-primitive-text></div>
                    </div>
                </div>
            </div>
        </div>

        <tg-table
            *ngIf="dataSource && columnsSource"
            #table
            [enableSelection]="false"
            [columnsSource]="columnsSource"
            [dataSource]="dataSource"
            #sort="matSort"
            [matSort]="sort"
            [showPaginator]="false"
            matSortActive="machineLabel"
            matSortDirection="asc"
            matSortDisableClear
        >
            <ng-template tgColumnCell="meterMetrics.jackpotMeter" let-row="row">{{ row.meterMetrics?.jackpotMeter ?? '-' }}</ng-template>
            <ng-template tgColumnCell="meterMetrics.progressiveMeter" let-row="row">{{
                row.meterMetrics?.progressiveMeter ?? '-'
            }}</ng-template>
            <ng-template tgColumnCell="meterMetrics.redeemCreditsMeter" let-row="row">{{
                row.meterMetrics?.redeemCreditsMeter ?? '-'
            }}</ng-template>
            <ng-template tgColumnCell="meterMetrics.totalMeters" let-row="row">{{ row.meterMetrics?.totalMeters ?? '-' }}</ng-template>
            <ng-template tgColumnCell="variance" let-row="row">{{ row.variance ?? '-' }}</ng-template>
        </tg-table>
        <div *ngIf="noMatchingItems" class="no-matching-items print-only" i18n>No matching items</div>
    </div>
</tg-main-panel>
