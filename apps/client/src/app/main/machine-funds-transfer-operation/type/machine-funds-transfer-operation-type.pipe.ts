import { Pipe, PipeTransform } from '@angular/core';
import { MachineFundsTransferOperationType } from '@cms/machine-funds-transfers';
import { TgLogger, TgLoggerFactory } from '@tronius/frontend-common';

@Pipe({
    name: 'machineFundsTransferOperationType',
    standalone: true
})
export class MachineFundsTransferOperationTypePipe implements PipeTransform {
    private static readonly logger: TgLogger = TgLoggerFactory.getLogger('MachineFundsTransferOperationTypePipe');

    transform(value: string, ..._args: unknown[]): string {
        return MachineFundsTransferOperationTypePipe.stringToDescription(value);
    }

    static stringToDescription(value: string): string {
        switch (value as MachineFundsTransferOperationType) {
            case MachineFundsTransferOperationType.Created:
                return $localize`Created`;
            case MachineFundsTransferOperationType.Cancelled:
                return $localize`Cancelled`;
            case MachineFundsTransferOperationType.Abandoned:
                return $localize`Abandoned`;
            case MachineFundsTransferOperationType.Completed:
                return $localize`Completed`;
            case MachineFundsTransferOperationType.CompletedManual:
                return $localize`Completed (Manual)`;
            case MachineFundsTransferOperationType.Failed:
                return $localize`Failed`;
            default:
                MachineFundsTransferOperationTypePipe.logger.warn(`Unknown machine-funds-transfer operation type: ${value}`);
                return `${$localize`Unknown`} (${value})`;
        }
    }
}
