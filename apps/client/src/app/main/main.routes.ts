import { Data, Route } from '@angular/router';
import { CmsPermissionGuardData, cmsPermissionGuard } from '@cms/client-permissions';
import { canDeactivateGuard } from '@tronius/frontend-ui';
import {
    CmsAction,
    deviceActions,
    eventActions,
    handpayActions,
    jackpotActions,
    machineActions,
    machineEarningRuleActions,
    machineFundsTransferOperationActions,
    machineGameTypeActions,
    machineMetricsActions,
    machineProfileActions,
    manufacturerActions,
    meterActions,
    ticketOperationActions,
    transactionPointActionsWithoutGetWorkstationTransactionPoint
} from '@tronius/shared-domain';

import { GenericTabsPageComponent, TabLink } from './generic-tabs-page/generic-tabs-page.component';
import { MainComponent } from './main.component';

export const mainRoutes: Route[] = [
    {
        path: '',
        component: MainComponent,
        children: [
            {
                path: 'cashier',
                loadComponent: async () => import('./cashier/cashier.component').then((mod) => mod.CashierComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [
                        CmsAction.TicketCreateCash,
                        CmsAction.TicketCreatePromo,
                        CmsAction.TicketRedeem,
                        CmsAction.HandpayList,
                        CmsAction.HandpayCreateManualHandpay
                    ]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'cashier/issue-ticket',
                loadComponent: async () => import('./cashier/issue-ticket/issue-ticket.component').then((mod) => mod.IssueTicketComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.TicketCreateCash, CmsAction.TicketCreatePromo]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'cashier/redeem-ticket',
                loadComponent: async () =>
                    import('./cashier/redeem-ticket/redeem-ticket.component').then((mod) => mod.RedeemTicketComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.TicketRedeem
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'cashier/replace-ticket/:id',
                loadComponent: async () =>
                    import('./cashier/replace-ticket/replace-ticket.component').then((mod) => mod.ReplaceTicketComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.TicketReplace
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'cashier/aft-in',
                loadComponent: async () => import('./cashier/aft-in/aft-in.component').then((mod) => mod.AftInComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.MachineFundsTransferCreateManualTransferIn
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'cashier/create-handpay',
                loadComponent: async () =>
                    import('./cashier/create-handpay/create-handpay.component').then((mod) => mod.CreateHandpayComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.HandpayCreateManualHandpay
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'tickets',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [...ticketOperationActions, CmsAction.TicketList],
                    tabs: [
                        { link: 'tickets', name: $localize`Tickets`, permissions: [CmsAction.TicketList] },
                        { link: 'liability-report', name: $localize`Ticket Liability Report`, permissions: [CmsAction.TicketList] },
                        { link: 'expired-tickets-report', name: $localize`Expired Tickets Report`, permissions: [CmsAction.TicketList] },
                        { link: 'voided-tickets-report', name: $localize`Voided Tickets Report`, permissions: [CmsAction.TicketList] },
                        {
                            link: 'reconciliation-report',
                            name: $localize`Ticket Reconciliation Report`,
                            permissions: [CmsAction.TicketList]
                        },
                        { link: 'operations', name: $localize`Ticket Operations`, permissions: ticketOperationActions },
                        { link: 'operations-report', name: $localize`Ticket Operations Report`, permissions: ticketOperationActions }
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'tickets/:id',
                        loadComponent: async () =>
                            import('./ticket-page/ticket/info/ticket-info.component').then((mod) => mod.TicketInfoComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.TicketList]
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'tickets',
                        loadComponent: async () => import('./ticket-page/ticket/ticket.component').then((mod) => mod.TicketComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.TicketList]
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'operations',
                        loadComponent: async () =>
                            import('./ticket-page/ticket-operation/ticket-operation.component').then((mod) => mod.TicketOperationComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: ticketOperationActions
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'operations-report',
                        loadComponent: async () =>
                            import('./ticket-page/ticket-operations-report/ticket-operations-report.component').then(
                                (mod) => mod.TicketOperationsReportComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: ticketOperationActions
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'liability-report',
                        loadComponent: async () =>
                            import('./ticket-page/ticket-liability-report/ticket-liability-report.component').then(
                                (mod) => mod.TicketLiabilityReportComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.TicketList]
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'expired-tickets-report',
                        loadComponent: async () =>
                            import('./ticket-page/expired-tickets-report/expired-tickets-report.component').then(
                                (mod) => mod.ExpiredTicketsReportComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.TicketList]
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'voided-tickets-report',
                        loadComponent: async () =>
                            import('./ticket-page/voided-tickets-report/voided-tickets-report.component').then(
                                (mod) => mod.VoidedTicketsReportComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.TicketList]
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'reconciliation-report',
                        loadComponent: async () =>
                            import('./ticket-page/ticket-reconciliation-report/ticket-reconciliation-report.component').then(
                                (mod) => mod.TicketReconciliationReportComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.TicketList]
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'tickets', pathMatch: 'full' }
                ]
            },

            {
                path: 'handpays',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [...handpayActions, CmsAction.HandpayOperationList],
                    tabs: [
                        { link: 'handpays', name: $localize`Handpays`, permissions: handpayActions } as TabLink,
                        { link: 'operations', name: $localize`Operations`, permissions: [CmsAction.HandpayOperationList] } as TabLink,
                        { link: 'reconciliation-report', name: $localize`Reconciliation Report`, permissions: handpayActions } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'handpays/:id',
                        loadComponent: async () => import('./handpay-page/handpay/handpay.component').then((mod) => mod.HandpayComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.HandpayList]
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'handpays',
                        loadComponent: async () => import('./handpay-page/handpay/handpay.component').then((mod) => mod.HandpayComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: handpayActions
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'operations',
                        loadComponent: async () =>
                            import('./handpay-page/handpay-operation/handpay-operation.component').then(
                                (mod) => mod.HandpayOperationComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.HandpayOperationList]
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'reconciliation-report',
                        loadComponent: async () =>
                            import('./handpay-page/handpay-reconciliation-report/handpay-reconciliation-report.component').then(
                                (mod) => mod.HandpayReconciliationReportComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.HandpayList]
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'handpays', pathMatch: 'full' }
                ]
            },

            {
                path: 'cash',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.CashOperationList],
                    tabs: [
                        {
                            link: 'reconciliation-report',
                            name: $localize`Reconciliation Report`,
                            permissions: [CmsAction.CashOperationList]
                        } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'reconciliation-report',
                        loadComponent: async () =>
                            import('./report/cash-reconciliation-report/cash-reconciliation-report.component').then(
                                (mod) => mod.CashReconciliationReportComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.CashOperationList
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'reconciliation-report', pathMatch: 'full' }
                ]
            },
            {
                path: 'machine-reports',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.MachineMetricsList],
                    tabs: [
                        {
                            link: 'hourly-win-reconciliation-report',
                            name: $localize`Hourly Win Reconciliation Report`,
                            permissions: [CmsAction.MachineMetricsList]
                        } as TabLink,
                        {
                            link: 'daily-win-reconciliation-report',
                            name: $localize`Daily Win Reconciliation Report`,
                            permissions: [CmsAction.MachineMetricsList]
                        } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'hourly-win-reconciliation-report',
                        loadComponent: async () =>
                            import(
                                './machine-reports-page/hourly-win-reconciliation-report/hourly-win-reconciliation-report.component'
                            ).then((mod) => mod.HourlyWinReconciliationReportComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.MachineMetricsList
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'daily-win-reconciliation-report',
                        loadComponent: async () =>
                            import('./machine-reports-page/daily-win-reconciliation-report/daily-win-reconciliation-report.component').then(
                                (mod) => mod.DailyWinReconciliationReportComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.MachineMetricsList
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'hourly-win-reconciliation-report', pathMatch: 'full' }
                ]
            },
            {
                path: 'machine-funds-transfer/:id/complete',
                loadComponent: async () =>
                    import('./machine-funds-transfer/complete/complete-machine-funds-transfer.component').then(
                        (mod) => mod.CompleteMachineFundsTransferComponent
                    ),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.MachineFundsTransferComplete]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'machine-funds-transfer/:id',
                loadComponent: async () =>
                    import('./machine-funds-transfer/info/machine-funds-transfer-info.component').then(
                        (mod) => mod.MachineFundsTransferInfoComponent
                    ),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: machineFundsTransferOperationActions
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'machine-funds-transfer',
                loadComponent: async () =>
                    import('./machine-funds-transfer/machine-funds-transfer.component').then((mod) => mod.MachineFundsTransferComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: machineFundsTransferOperationActions
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'machine-funds-transfer-operation',
                loadComponent: async () =>
                    import('./machine-funds-transfer-operation/machine-funds-transfer-operation.component').then(
                        (mod) => mod.MachineFundsTransferOperationComponent
                    ),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: machineFundsTransferOperationActions
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'events',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: eventActions,
                    tabs: [
                        { link: 'events', name: $localize`Events`, permissions: eventActions } as TabLink,
                        { link: 'machine-event-report', name: $localize`Machine Event Report`, permissions: eventActions } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'events',
                        loadComponent: async () => import('./event-page/event/event.component').then((mod) => mod.EventComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: eventActions
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'machine-event-report',
                        loadComponent: async () =>
                            import('./event-page/machine-event-report/machine-event-report.component').then(
                                (mod) => mod.MachineEventReportComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: eventActions
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'events', pathMatch: 'full' }
                ]
            },
            {
                path: 'transaction-point',
                loadComponent: async () =>
                    import('./casino/transaction-point/transaction-points.component').then((mod) => mod.TransactionPointsComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: transactionPointActionsWithoutGetWorkstationTransactionPoint
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'transaction-point/add',
                loadComponent: async () =>
                    import('./casino/transaction-point/edit/transaction-point-edit.component').then(
                        (mod) => mod.TransactionPointEditComponent
                    ),
                canDeactivate: [canDeactivateGuard],
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.TransactionPointCreate]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'transaction-point/:id/edit',
                loadComponent: async () =>
                    import('./casino/transaction-point/edit/transaction-point-edit.component').then(
                        (mod) => mod.TransactionPointEditComponent
                    ),
                canDeactivate: [canDeactivateGuard],
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.TransactionPointUpdate]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'machines',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [...machineActions, ...manufacturerActions, ...machineProfileActions],
                    tabs: [
                        { link: 'live-view-tiles', name: $localize`Live View Tiles`, permissions: machineActions } as TabLink,
                        { link: 'live-view', name: $localize`Live View`, permissions: machineActions } as TabLink,
                        { link: 'profile', name: $localize`Profiles`, permissions: machineProfileActions } as TabLink,
                        { link: 'manufacturer', name: $localize`Manufacturers`, permissions: manufacturerActions } as TabLink,
                        { link: 'game-type', name: $localize`Game Types`, permissions: machineGameTypeActions } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'live-view-tiles',
                        loadComponent: async () =>
                            import('./machine/live-view/tiles/machines-live-view-tiles.component').then(
                                (mod) => mod.MachinesLiveViewTilesComponent
                            ),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: machineActions
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'live-view',
                        loadComponent: async () =>
                            import('./machine/live-view/machines-live-view.component').then((mod) => mod.MachinesLiveViewComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: machineActions
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'profile',
                        loadComponent: async () =>
                            import('./machine/profile/machine-profiles.component').then((mod) => mod.MachineProfilesComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: machineProfileActions
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'manufacturer',
                        loadComponent: async () =>
                            import('./machine/manufacturer/manufacturers.component').then((mod) => mod.ManufacturersComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: manufacturerActions
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'game-type',
                        loadComponent: async () =>
                            import('./machine/game-type/machine-game-types.component').then((mod) => mod.MachineGameTypesComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: machineGameTypeActions
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'live-view-tiles', pathMatch: 'full' }
                ]
            },
            {
                path: 'machines/profile/add',
                loadComponent: async () =>
                    import('./machine/profile/edit/machine-profile-edit.component').then((mod) => mod.MachineProfileEditComponent),

                canDeactivate: [canDeactivateGuard],
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.MachineProfileCreate
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'machines/profile/:id/edit',
                loadComponent: async () =>
                    import('./machine/profile/edit/machine-profile-edit.component').then((mod) => mod.MachineProfileEditComponent),
                canDeactivate: [canDeactivateGuard],
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.MachineProfileUpdate
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'machines/manufacturer/add',
                loadComponent: async () =>
                    import('./machine/manufacturer/edit/manufacturer-edit.component').then((mod) => mod.ManufacturerEditComponent),
                canDeactivate: [canDeactivateGuard],
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.ManufacturerCreate]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'machines/manufacturer/:id/edit',
                loadComponent: async () =>
                    import('./machine/manufacturer/edit/manufacturer-edit.component').then((mod) => mod.ManufacturerEditComponent),
                canDeactivate: [canDeactivateGuard],
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.ManufacturerUpdate]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'machines/game-type/add',
                loadComponent: async () =>
                    import('./machine/game-type/edit/machine-game-type-edit.component').then((mod) => mod.MachineGameTypeEditComponent),
                canDeactivate: [canDeactivateGuard],
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.MachineGameTypeCreate]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'machines/game-type/:id/edit',
                loadComponent: async () =>
                    import('./machine/game-type/edit/machine-game-type-edit.component').then((mod) => mod.MachineGameTypeEditComponent),
                canDeactivate: [canDeactivateGuard],
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.MachineGameTypeUpdate]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'meter',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: meterActions,
                    tabs: [
                        {
                            link: 'raw-current',
                            name: $localize`Current Raw Meters`,
                            permissions: [CmsAction.MeterListRawCurrent]
                        } as TabLink,
                        {
                            link: 'raw-snapshot',
                            name: $localize`Raw Meter Snapshots`,
                            permissions: [CmsAction.MeterListRawSnapshot]
                        } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'raw-current',
                        loadComponent: async () =>
                            import('./meter/current-raw/raw-current-meter.component').then((mod) => mod.RawCurrentMeterComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.MeterListRawCurrent
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'raw-snapshot',
                        loadComponent: async () =>
                            import('./meter/snapshot-raw/raw-snapshot-meter.component').then((mod) => mod.RawSnapshotMeterComponent)
                    },
                    { path: '', redirectTo: 'raw-current', pathMatch: 'full' }
                ]
            },
            {
                path: 'metric',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: machineMetricsActions,
                    tabs: [
                        { link: 'hourly', name: $localize`Hourly Metrics`, permissions: [CmsAction.MachineMetricsList] } as TabLink,
                        { link: 'daily', name: $localize`Daily Metrics`, permissions: [CmsAction.MachineMetricsList] } as TabLink,
                        { link: 'clearance', name: $localize`Clearance Metrics`, permissions: [CmsAction.MachineMetricsList] } as TabLink,
                        { link: 'all', name: $localize`All Metrics`, permissions: [CmsAction.MachineMetricsList] } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'hourly',
                        loadComponent: async () =>
                            import('./metric/hourly/hourly-metric.component').then((mod) => mod.HourlyMetricComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.MachineMetricsList
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'daily',
                        loadComponent: async () => import('./metric/daily/daily-metric.component').then((mod) => mod.DailyMetricComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.MachineMetricsList
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'clearance',
                        loadComponent: async () =>
                            import('./metric/clearance/clearance-metric.component').then((mod) => mod.ClearanceMetricComponent),
                        data: {
                            actions: CmsAction.MachineMetricsList
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'all',
                        loadComponent: async () => import('./metric/all/all-metric.component').then((mod) => mod.AllMetricComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.MachineMetricsList
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'hourly', pathMatch: 'full' }
                ]
            },
            {
                path: 'report',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: machineMetricsActions,
                    tabs: [
                        { link: 'hourly', name: $localize`Hourly Report`, permissions: [CmsAction.MachineMetricsList] } as TabLink,
                        { link: 'daily', name: $localize`Daily Report`, permissions: [CmsAction.MachineMetricsList] } as TabLink,
                        { link: 'clearance', name: $localize`Clearance Report`, permissions: [CmsAction.MachineMetricsList] } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'hourly',
                        loadComponent: async () =>
                            import('./report/hourly-metric/report-hourly-metric.component').then((mod) => mod.ReportHourlyMetricComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.MachineMetricsList
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'daily',
                        loadComponent: async () =>
                            import('./report/daily-metric/report-daily-metric.component').then((mod) => mod.ReportDailyMetricComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.MachineMetricsList
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'clearance',
                        loadComponent: async () =>
                            import('./report/clearance/clearance-report.component').then((mod) => mod.ClearanceReportComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.MachineMetricsList
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'hourly', pathMatch: 'full' }
                ]
            },
            {
                path: 'device',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: deviceActions,
                    tabs: [
                        { link: 'all', name: $localize`Devices`, permissions: [CmsAction.DeviceList] } as TabLink,
                        { link: 'pending', name: $localize`Pending Devices`, permissions: [CmsAction.DeviceList] } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'all',
                        loadComponent: async () => import('./device/device/device.component').then((mod) => mod.DeviceComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.DeviceList
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'pending',
                        loadComponent: async () =>
                            import('./device/pending/pending-device.component').then((mod) => mod.PendingDeviceComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.DeviceList
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'create',
                        loadComponent: async () =>
                            import('./device/create-device/create-device.component').then((mod) => mod.CreateDeviceComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.DeviceCreate
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: ':id/edit',
                        loadComponent: async () =>
                            import('./device/edit-device/edit-device.component').then((mod) => mod.EditDeviceComponent),
                        canActivate: [cmsPermissionGuard],
                        canDeactivate: [canDeactivateGuard],
                        data: {
                            actions: CmsAction.DeviceUpdate
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'all', pathMatch: 'full' }
                ]
            },
            {
                path: 'device-firmware',
                loadComponent: async () =>
                    import('./device-firmware/device-firmware-page.component').then((mod) => mod.DeviceFirmwarePageComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.DeviceFirmware
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'printer',
                loadComponent: async () => import('./printer/printer.component').then((mod) => mod.PrinterComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.PrinterList
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'printer/add',
                loadComponent: async () => import('./printer/edit/printer-edit.component').then((mod) => mod.PrinterEditComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.PrinterCreate
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'printer/:id/edit',
                loadComponent: async () => import('./printer/edit/printer-edit.component').then((mod) => mod.PrinterEditComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.PrinterUpdate
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'sandbox',
                loadComponent: async () => import('./sandbox/sandbox.component').then((mod) => mod.SandboxComponent)
            },
            {
                path: 'jackpot',
                component: GenericTabsPageComponent,
                data: {
                    actions: jackpotActions,
                    tabs: [
                        { link: 'level', name: $localize`Jackpot Levels`, permissions: [CmsAction.JackpotListLevels] } as TabLink,
                        { link: 'client', name: $localize`Jackpot Clients`, permissions: [CmsAction.JackpotListClients] } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'level',
                        loadComponent: async () =>
                            import('./jackpot/level/jackpot-level.component').then((mod) => mod.JackpotLevelComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.JackpotListLevels
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'client',
                        loadComponent: async () =>
                            import('./jackpot/client/jackpot-client.component').then((mod) => mod.JackpotClientComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.JackpotListClients
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'level', pathMatch: 'full' }
                ],
                canActivate: [cmsPermissionGuard]
            },
            {
                path: 'jackpot/level/add',
                loadComponent: async () =>
                    import('./jackpot/level/edit/jackpot-level-edit.component').then((mod) => mod.JackpotLevelEditComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.JackpotCreateLevel
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'jackpot/level/:id/edit',
                loadComponent: async () =>
                    import('./jackpot/level/edit/jackpot-level-edit.component').then((mod) => mod.JackpotLevelEditComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.JackpotUpdateLevel
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'jackpot/level/:id/view',
                loadComponent: async () =>
                    import('./jackpot/level/view/jackpot-level-view.component').then((mod) => mod.JackpotLevelViewComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.JackpotListLevels, CmsAction.JackpotListLevelHistory]
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'jackpot/client/add',
                loadComponent: async () =>
                    import('./jackpot/client/edit/jackpot-client-edit.component').then((mod) => mod.JackpotClientEditComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.JackpotAddLevelsForClient
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'jackpot/client/:id/edit',
                loadComponent: async () =>
                    import('./jackpot/client/edit/jackpot-client-edit.component').then((mod) => mod.JackpotClientEditComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.JackpotAddLevelsForClient
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'jackpot/client/:id/view',
                loadComponent: async () =>
                    import('./jackpot/client/edit/jackpot-client-edit.component').then((mod) => mod.JackpotClientEditComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.JackpotListClients
                } as CmsPermissionGuardData as Data
            },
            {
                path: 'permissions',
                loadComponent: async () => import('./permission/permission.component').then((mod) => mod.PermissionComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.PermissionList
                } as CmsPermissionGuardData
            },
            {
                path: 'users',
                loadComponent: async () => import('./cms-users/cms-users.component').then((mod) => mod.CmsUsersComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.UserList
                } as CmsPermissionGuardData
            },
            {
                path: 'users/create',
                loadComponent: async () => import('./cms-users/create-user/create-user.component').then((mod) => mod.CreateUserComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.UserManage
                } as CmsPermissionGuardData
            },
            {
                path: 'users/:username/reset-password',
                loadComponent: async () =>
                    import('./cms-users/reset-password/reset-password.component').then((mod) => mod.ResetPasswordComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.UserManage
                } as CmsPermissionGuardData
            },
            {
                path: 'users/:username/edit',
                loadComponent: async () => import('./cms-users/update-user/update-user.component').then((mod) => mod.UpdateUserComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.UserManage
                } as CmsPermissionGuardData
            },
            {
                path: 'platform-overview',
                loadComponent: async () =>
                    import('../platform-overview/platform-overview.component').then((mod) => mod.PlatformOverviewComponent)
            },
            {
                path: 'finance',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: CmsAction.DenominationList,
                    tabs: [{ link: 'denomination', name: $localize`Denominations`, permissions: [CmsAction.DenominationList] } as TabLink]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'denomination',
                        loadComponent: async () =>
                            import('./finance/denomination/denominations.component').then((mod) => mod.DenominationsComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.DenominationList
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'denomination/add',
                        loadComponent: async () =>
                            import('./finance/denomination/edit/denomination-edit.component').then((mod) => mod.DenominationEditComponent),
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.DenominationCreate
                        } as CmsPermissionGuardData as Data
                    },
                    {
                        path: 'denomination/:id/edit',
                        loadComponent: async () =>
                            import('./finance/denomination/edit/denomination-edit.component').then((mod) => mod.DenominationEditComponent),
                        canDeactivate: [canDeactivateGuard],
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: CmsAction.DenominationUpdate
                        } as CmsPermissionGuardData as Data
                    },
                    { path: '', redirectTo: 'denomination', pathMatch: 'full' }
                ]
            },
            {
                path: 'loyalty',
                component: GenericTabsPageComponent,
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: machineEarningRuleActions,
                    tabs: [
                        {
                            link: 'machine-earning-rules',
                            name: $localize`Machine Earning Rules`,
                            permissions: machineEarningRuleActions
                        } as TabLink
                    ]
                } as CmsPermissionGuardData as Data,
                children: [
                    {
                        path: 'machine-earning-rules',
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: machineEarningRuleActions
                        } as CmsPermissionGuardData as Data,
                        loadComponent: async () =>
                            import('./loyalty/machine-earning-rules/machine-earning-rules.component').then(
                                (mod) => mod.MachineEarningRulesComponent
                            )
                    },
                    {
                        path: 'machine-earning-rules/add',
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.MachineEarningRulesEdit]
                        } as CmsPermissionGuardData as Data,
                        loadComponent: async () =>
                            import('./loyalty/machine-earning-rules/edit/machine-earning-rule-edit.component').then(
                                (mod) => mod.MachineEarningRuleEditComponent
                            )
                    },
                    {
                        path: 'machine-earning-rules/:id/edit',
                        canActivate: [cmsPermissionGuard],
                        data: {
                            actions: [CmsAction.MachineEarningRulesEdit]
                        } as CmsPermissionGuardData as Data,
                        loadComponent: async () =>
                            import('./loyalty/machine-earning-rules/edit/machine-earning-rule-edit.component').then(
                                (mod) => mod.MachineEarningRuleEditComponent
                            )
                    },
                    { path: '', redirectTo: 'machine-earning-rules', pathMatch: 'full' }
                ]
            },
            {
                path: 'audit',
                loadComponent: async () => import('./audit/audit-page.component').then((mod) => mod.AuditPageComponent),
                canActivate: [cmsPermissionGuard],
                data: {
                    actions: [CmsAction.AuditLogList]
                } as CmsPermissionGuardData as Data
            }
        ]
    }
];
