<tg-main-panel [contentPadding]="true" [fullHeight]="true" [scrollable]="false">
    <form [formGroup]="form" class="no-print">
        <mat-form-field>
            <mat-label i18n>Gaming Day</mat-label>
            <mat-date-range-input [formGroup]="form" [rangePicker]="picker">
                <input matStartDate formControlName="start" placeholder="Start date" i18n-placeholder />
                <input matEndDate formControlName="end" placeholder="End date" i18n-placeholder />
            </mat-date-range-input>
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-date-range-picker #picker></mat-date-range-picker>
            @if (form.controls.end.errors?.required) {
                <mat-error i18n>End date is required</mat-error>
            } @else if (form.controls.end.errors?.maxDaysGreaterThanOtherControl?.otherControlName === 'start') {
                <mat-error i18n> Dates should be at most {{ maxIntervalInDays }} days apart </mat-error>
            }
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Ticket Type</mat-label>
            <mat-select formControlName="ticketType">
                <mat-option [value]="null" i18n>All</mat-option>
                @for (ticketType of ticketTypes; track ticketType) {
                    <mat-option [value]="ticketType">
                        {{ ticketType | ticketType }}
                    </mat-option>
                }
            </mat-select>
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Variance &gt; </mat-label>
            <input matInput type="number" formControlName="variance" step="0.01" required />
            @if (form.controls.variance.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            }
        </mat-form-field>

        <button mat-flat-button color="primary" [disabled]="!form.valid" (click)="generateReport()" i18n>
            <mat-icon>assessment</mat-icon>
            Generate Report
        </button>

        <button mat-flat-button color="primary" (click)="print()"><mat-icon>print</mat-icon> <span i18n>Print</span></button>
    </form>

    <div *ngIf="requestData && requestData.start && requestData.end" id="print-ticket-reconciliation-report">
        <div class="header">
            <div i18n class="operator-row print-only">{{ operatorName }}</div>
            <div class="title-row print-only">
                <div class="title" i18n>Ticket Reconciliation Report</div>
                <div class="generated-meta-info-container">
                    <div i18n class="label">Generated by:</div>
                    <div class="operator-and-time">{{ generatedBy }}, {{ generatedTime | tgDateTime }}</div>
                </div>
            </div>
            <div class="filters-row">
                <div class="filters print-only">
                    <div class="label-value">
                        <div i18n class="label">Gaming Day:</div>
                        <div class="value">
                            {{ requestData.start | tgDate }}
                            <ng-container *ngIf="requestData.start !== requestData.end"> - {{ requestData.end | tgDate }}</ng-container>
                        </div>
                    </div>
                    <div i18n class="label">Ticket Type:</div>
                    <div class="value">
                        @if (requestData.ticketType) {
                            {{ requestData.ticketType | ticketType }}
                        } @else {
                            <ng-container i18n>All</ng-container>
                        }
                    </div>
                    <div class="label-value">
                        <div i18n class="label">Variance ></div>
                        <div class="value"><tg-primitive-text [value]="requestData.variance" type="currency"></tg-primitive-text></div>
                    </div>
                </div>
            </div>

            <div *ngIf="totalDataSource" class="totals-table">
                <table mat-table [dataSource]="totalDataSource" class="mat-elevation-z8" style="min-width: auto">
                    <ng-container matColumnDef="total">
                        <th i18n mat-header-cell *matHeaderCellDef>Total</th>
                        <td mat-cell *matCellDef="let element">{{ element.rowLabel }}</td>
                    </ng-container>

                    <ng-container matColumnDef="accounting">
                        <th i18n mat-header-cell *matHeaderCellDef>Accounting</th>
                        <td mat-cell *matCellDef="let element">
                            <tg-primitive-text [value]="element.accounting" type="currency"></tg-primitive-text>
                        </td>
                    </ng-container>

                    <ng-container matColumnDef="meter">
                        <th i18n mat-header-cell *matHeaderCellDef>Meter</th>
                        <td mat-cell *matCellDef="let element">
                            <tg-primitive-text [value]="element.meter" type="currency"></tg-primitive-text>
                        </td>
                    </ng-container>

                    <ng-container matColumnDef="variance">
                        <th i18n mat-header-cell *matHeaderCellDef>Variance</th>
                        <td mat-cell *matCellDef="let element">
                            <tg-primitive-text [value]="element.variance" type="currency"></tg-primitive-text>
                        </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="totalTableColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: totalTableColumns"></tr>
                </table>
            </div>
        </div>

        <tg-table
            *ngIf="dataSource && columnsSource"
            #table
            [enableSelection]="false"
            [columnsSource]="columnsSource"
            [dataSource]="dataSource"
            #sort="matSort"
            [matSort]="sort"
            [showPaginator]="false"
            matSortActive="machineLabel"
            matSortDirection="asc"
            matSortDisableClear
        >
            <ng-template tgColumnCell="machineId" let-row="row">
                <ng-container *ngIf="row.machine">{{ row.machine.location }} ({{ row.machine.assetNumber }})</ng-container>
            </ng-template>
        </tg-table>
        <div *ngIf="noMatchingItems" class="no-matching-items print-only" i18n>No matching items</div>
    </div>
</tg-main-panel>
