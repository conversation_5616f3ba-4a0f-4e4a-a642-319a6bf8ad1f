import { CommonModule } from '@angular/common';
import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { Title } from '@angular/platform-browser';
import { getGamingDay } from '@cms/casino-operator';
import { MachineLabelPipe } from '@cms/fe-components';
import { WinReconciliationReportType } from '@cms/machine-metrics';
import { AuthenticationService } from '@cms/ng-client-authentication';
import { TicketReconciliationReportItem } from '@cms/tickets';
import { SettingsService, TgLoggerFactory } from '@tronius/frontend-common';
import { TgArrayDataSource, TgDateModule, TgMainPanelModule, TgPrimitiveTextComponent, TgTableModule } from '@tronius/frontend-ui';
import { CmsUser, TgTicketType } from '@tronius/shared-domain';
import { formatDate } from 'date-fns';
import { firstValueFrom } from 'rxjs';

import { ticketReconciliationReportColumnsSource } from './ticket-reconciliation-report.columns';
import { BackendService } from '../../../core/backend/backend.service';
import { TicketTypePipe } from '../ticket/type/ticket-type.pipe';

interface FormGroupType {
    start: FormControl<Date | null>;
    end: FormControl<Date | null>;
    ticketType: FormControl<TgTicketType | null>;
    variance: FormControl<number | null>;
}

export interface TicketReconciliationReportRow extends TicketReconciliationReportItem {
    machineLabel: string;
}

interface ReportTotalRow {
    accountingTicketsIn: number;
    meterTicketsIn: number;
    accountingTicketsOut: number;
    meterTicketsOut: number;
    ticketsInVariance: number | null;
    ticketsOutVariance: number | null;
}

@Component({
    selector: 'app-ticket-reconciliation-report',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule,
        MatSelectModule,
        MatSnackBarModule,
        MatTableModule,
        TgMainPanelModule,
        TgDateModule,
        TgTableModule,
        TgPrimitiveTextComponent,
        TicketTypePipe
    ],
    templateUrl: './ticket-reconciliation-report.component.html',
    styleUrls: ['./ticket-reconciliation-report.component.scss']
})
export class TicketReconciliationReportComponent implements OnInit, OnDestroy {
    private static readonly logger = TgLoggerFactory.getLogger(TicketReconciliationReportComponent.name);

    protected readonly ticketTypes = [TgTicketType.Cash, TgTicketType.PromoRestricted];
    protected readonly maxIntervalInDays = 31;
    protected readonly defaultVarianceValue = 0.01;

    protected form: FormGroup<FormGroupType> = this.initForm();
    protected requestData?: typeof this.form.value;
    protected columnsSource = ticketReconciliationReportColumnsSource;
    protected dataSource!: TgArrayDataSource<TicketReconciliationReportRow>;
    protected noMatchingItems = true;

    protected operatorName: string;
    protected generatedBy: string = this.determineGeneratedBy();
    protected generatedTime = new Date();

    protected totalTableColumns = ['total', 'accounting', 'meter', 'variance'];
    protected totalDataSource!: Array<{ rowLabel: string; accounting: number; meter: number; variance: number }>;
    protected totalRow?: ReportTotalRow;

    constructor(
        private readonly authenticationService: AuthenticationService,
        private readonly backendService: BackendService,
        private readonly settingsService: SettingsService,
        private readonly titleService: Title
    ) {
        this.operatorName = this.settingsService.getSettings().propertyName;
        const title = $localize`Ticket Reconciliation Report`;
        this.titleService.setTitle(`${title} (${this.generatedBy})`);
    }

    ngOnInit(): void {
        this.generateReport();
    }

    ngOnDestroy(): void {
        this.titleService.setTitle('CMS');
    }

    protected generateReport(): void {
        this.dataSource = new TgArrayDataSource<TicketReconciliationReportRow>(async () => this.getReport());
    }

    protected print(): void {
        window.print();
    }

    private initForm(): FormGroup<FormGroupType> {
        const gamingDay = this.determineCurrentGamingDay();
        const today = new Date(gamingDay);

        const form = new FormGroup({
            start: new FormControl<Date | null>(today, Validators.required),
            end: new FormControl<Date | null>(today, Validators.required),
            ticketType: new FormControl<TgTicketType | null>(null),
            variance: new FormControl<number | null>(this.defaultVarianceValue, Validators.required)
        });

        return form;
    }

    private determineCurrentGamingDay(): string {
        const { endOfDayHour, timezone } = this.settingsService.getSettings();
        return getGamingDay({ timestamp: Date.now(), endOfDayHour, timezone }).date;
    }

    private async getReport(): Promise<TicketReconciliationReportRow[]> {
        const { start, end, ticketType, variance } = (this.requestData = this.form.getRawValue());

        if (!start || !end || !variance) {
            return [];
        }

        const request = {
            fromGamingDay: formatDate(start, 'yyyy-MM-dd'),
            toGamingDay: formatDate(end, 'yyyy-MM-dd'),
            type: this.getReportTypeByTicketType(ticketType)
        };

        const rows = await firstValueFrom(this.backendService.getTicketReconciliationReport(request));

        this.calculateTotals(rows);
        this.noMatchingItems = !rows.length;

        return rows.map((row) => ({ ...row, machineLabel: MachineLabelPipe.machineToLabel(row.machine) }));
    }

    private calculateTotals(rows: TicketReconciliationReportItem[]): void {
        const total: ReportTotalRow = {
            accountingTicketsIn: 0,
            meterTicketsIn: 0,
            ticketsInVariance: 0,
            accountingTicketsOut: 0,
            meterTicketsOut: 0,
            ticketsOutVariance: 0
        };

        rows.forEach(({ metrics }) => {
            total.accountingTicketsIn += metrics.accountingTicketsIn ?? 0;
            total.meterTicketsIn += metrics.meterTicketsIn ?? 0;
            total.accountingTicketsOut += metrics.accountingTicketsOut ?? 0;
            total.meterTicketsOut += metrics.meterTicketsOut ?? 0;
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            total.ticketsInVariance! += metrics.varianceIn ?? 0;
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            total.ticketsOutVariance! += metrics.varianceOut ?? 0;
        });

        this.totalRow = total;

        this.totalDataSource = [
            {
                rowLabel: $localize`:Row label for tickets in:In`,
                accounting: total.accountingTicketsIn,
                meter: total.meterTicketsIn,
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                variance: total.ticketsInVariance!
            },
            {
                rowLabel: $localize`:Row label for tickets out:Out`,
                accounting: total.accountingTicketsOut,
                meter: total.meterTicketsOut,
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                variance: total.ticketsOutVariance!
            }
        ];
    }

    private determineGeneratedBy(): string {
        const user: CmsUser | null = this.authenticationService.authenticatedUser;

        if (!user) {
            return '';
        }

        return user.firstName || user.lastName ? `${user.firstName} ${user.lastName}` : user.username;
    }

    private getReportTypeByTicketType(ticketType: TgTicketType | null): WinReconciliationReportType {
        if (!ticketType) {
            return WinReconciliationReportType.IncludePromo;
        }
        switch (ticketType) {
            case TgTicketType.Cash:
                return WinReconciliationReportType.ExcludePromo;
            case TgTicketType.PromoRestricted:
            case TgTicketType.PromoUnrestricted: // Just in case
                return WinReconciliationReportType.OnlyPromo;
            default:
                return WinReconciliationReportType.IncludePromo;
        }
    }
}
