import { TgBrowserStorageColumnsSource } from '@tronius/frontend-ui';

export const ticketReconciliationReportColumnsSource = new TgBrowserStorageColumnsSource(
    [
        {
            id: 'machineLabel',
            name: $localize`Machine`,
            columnHeaderAlign: 'left',
            columnAlign: 'left'
        },
        {
            id: 'metrics.accountingTicketsIn',
            name: $localize`Accounting Tickets In`,
            type: 'currency',
            columnHeaderAlign: 'right',
            columnAlign: 'right'
        },
        {
            id: 'metrics.meterTicketsIn',
            name: $localize`Meter Tickets In`,
            type: 'currency',
            columnHeaderAlign: 'right',
            columnAlign: 'right'
        },
        {
            id: 'metrics.varianceIn',
            name: $localize`Tickets In Variance`,
            type: 'currency',
            columnHeaderAlign: 'right',
            columnAlign: 'right'
        },
        {
            id: 'metrics.accountingTicketsOut',
            name: $localize`Accounting Tickets Out`,
            type: 'currency',
            columnHeaderAlign: 'right',
            columnAlign: 'right'
        },
        {
            id: 'metrics.meterTicketsOut',
            name: $localize`Meter Tickets Out`,
            type: 'currency',
            columnHeaderAlign: 'right',
            columnAlign: 'right'
        },
        {
            id: 'metrics.varianceOut',
            name: $localize`Tickets Out Variance`,
            type: 'currency',
            columnHeaderAlign: 'right',
            columnAlign: 'right'
        }
    ],
    'ticket-reconciliation-report'
);
