import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { Title } from '@angular/platform-browser';
import { getGamingDay } from '@cms/casino-operator';
import { MachineLabelPipe } from '@cms/fe-components';
import { AuthenticationService } from '@cms/ng-client-authentication';
import { TgTicket, TgTicketOperationType, TgTicketType } from '@cms/tickets';
import { SettingsService } from '@tronius/frontend-common';
import {
    TgArrayDataSource,
    TgBrowserStorageColumnsSource,
    TgDateModule,
    TgMainPanelModule,
    TgPrimitiveTextComponent,
    TgTableModule,
    createColumnSort
} from '@tronius/frontend-ui';
import { TgFilter, TgFilterOperator, TgListRequest, TriggerType } from '@tronius/shared-common';
import { CmsUser, MachineProfile, MachineProfileListItem, TransactionPoint } from '@tronius/shared-domain';
import { formatDate, subDays } from 'date-fns';
import { Observable, combineLatest, firstValueFrom, from, map, of, switchMap, tap } from 'rxjs';

import { getTicketColumnSourceColumns } from './abstract-tickets-report.columns';
import { TriggerTypePipe } from '../../../common/trigger-type/trigger-type.pipe';
import { BackendService } from '../../../core/backend/backend.service';
import { TicketTypePipe } from '../ticket/type/ticket-type.pipe';

interface FormGroupType {
    gamingDay: FormControl<Date | null>;
    ticketType: FormControl<TgTicketType | null>;
}

interface TicketReportRow extends TgTicket {
    printLocationType?: string;
    printLocation?: string;
    voidLocationType?: string;
    voidLocation?: string;
    voidUser?: string;
}

@Component({
    selector: 'app-ticket-liability-report',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule,
        MatSelectModule,
        MatSnackBarModule,
        TgMainPanelModule,
        TgDateModule,
        TgTableModule,
        TicketTypePipe,
        TgPrimitiveTextComponent
    ],
    template: ''
})
export abstract class AbstractTicketsReportComponent implements OnInit, OnDestroy {
    protected readonly TgTicketType = TgTicketType;
    protected readonly TriggerType = TriggerType;

    protected readonly ticketTypes = [TgTicketType.Cash, TgTicketType.PromoRestricted];
    protected form: FormGroup<FormGroupType> = this.initForm();
    protected requestData?: typeof this.form.value;
    protected columnsSource: TgBrowserStorageColumnsSource = this.createColumnsSource();
    protected dataSource!: TgArrayDataSource<TicketReportRow>;
    protected noMatchingItems = true;

    protected operatorName: string;
    protected generatedBy: string = this.determineGeneratedBy();
    protected generatedTime = new Date();
    protected totalAmount = 0;

    private readonly machinesById: Record<string, MachineProfile | MachineProfileListItem | undefined> = {};
    private readonly txPointsById: Record<string, TransactionPoint | undefined> = {};

    protected abstract reportName: string;

    protected constructor(
        readonly authenticationService: AuthenticationService,
        readonly backendService: BackendService,
        readonly settingsService: SettingsService,
        readonly titleService: Title
    ) {
        this.operatorName = this.settingsService.getSettings().propertyName;
    }

    ngOnInit(): void {
        this.titleService.setTitle(`${this.reportName} (${this.generatedBy})`);
        this.generateReport();
    }

    ngOnDestroy(): void {
        this.titleService.setTitle('CMS');
    }

    protected generateReport(): void {
        this.dataSource = new TgArrayDataSource<TicketReportRow>(async () => this.getReport(), createColumnSort(this.columnsSource));
    }

    protected print(): void {
        window.print();
    }

    private createColumnsSource(): TgBrowserStorageColumnsSource {
        const columns = getTicketColumnSourceColumns().filter((column) => this.getColumnIds().includes(column.id));
        return new TgBrowserStorageColumnsSource(columns, this.getColumnSourceTableName());
    }

    private initForm(): FormGroup<FormGroupType> {
        const gamingDay = this.determinePreviousGamingDay();

        return new FormGroup({
            gamingDay: new FormControl<Date | null>(new Date(gamingDay), Validators.required),
            ticketType: new FormControl<TgTicketType | null>(null)
        });
    }

    private determinePreviousGamingDay(): string {
        const { endOfDayHour, timezone } = this.settingsService.getSettings();
        return getGamingDay({ timestamp: subDays(Date.now(), 1).getTime(), endOfDayHour, timezone }).date;
    }

    private determineCurrentGamingDay(): string {
        const { endOfDayHour, timezone } = this.settingsService.getSettings();
        return getGamingDay({ timestamp: Date.now(), endOfDayHour, timezone }).date;
    }

    private async getReport(): Promise<TicketReportRow[]> {
        const { gamingDay, ticketType } = (this.requestData = this.form.getRawValue());
        const gamingDayString = gamingDay ? formatDate(gamingDay, 'yyyy-MM-dd') : this.determinePreviousGamingDay();

        const filters = this.createRequestFiltersFromForm(gamingDayString, ticketType ?? undefined);
        const request: TgListRequest<TgTicket> = this.createRequest(filters, gamingDayString);
        const request$ = from(this.backendService.getAllTickets(request)).pipe(
            switchMap((items) => {
                this.noMatchingItems = !items.length;
                const [machineIds, txPointIds] = this.extractPrintedAndResolvedTriggerIds(items);

                return combineLatest([of(items), this.loadMachines(machineIds), this.loadTxPoints(txPointIds)]);
            }),
            map(([items]) => items.map((item) => this.transformTicket(item))),
            tap((rows) => {
                this.totalAmount = rows.reduce((sum, row) => sum + row.amount, 0);
            })
        );

        return firstValueFrom(request$);
    }

    private extractTriggeredIds(items: TgTicket[], field: 'printed' | 'resolved'): [string[], string[]] {
        const [machineIds, txPointIds] = [TriggerType.Machine, TriggerType.TransactionPoint].map((triggerType) => {
            const ids = items
                .filter((item) => (field === 'printed' ? item.printedByType : item.resolvedByType) === triggerType)
                .map((item): string | null => (field === 'printed' ? item.printedById : item.resolvedById))
                .filter((id): id is string => !!id);
            return Array.from(new Set(ids));
        });

        return [machineIds, txPointIds];
    }

    private extractPrintedAndResolvedTriggerIds(items: TgTicket[]): [string[], string[]] {
        const [printedMachineIds, printedTxPointIds] = this.extractTriggeredIds(items, 'printed');
        const [resolvedMachineIds, resolvedTxPointIds] = this.extractTriggeredIds(items, 'resolved');

        return [
            Array.from(new Set([...printedMachineIds, ...resolvedMachineIds])),
            Array.from(new Set([...printedTxPointIds, ...resolvedTxPointIds]))
        ];
    }

    private loadMachines(ids: string[]): Observable<MachineProfile[]> {
        if (ids.length) {
            return this.backendService.getMachineProfilesById(ids).pipe(
                tap((machines) => {
                    machines.forEach((machine) => (this.machinesById[machine.id] = machine));
                })
            );
        } else {
            return of([]);
        }
    }

    private loadTxPoints(ids: string[]): Observable<TransactionPoint[]> {
        if (ids.length) {
            return this.backendService.getTransactionPointsById(ids).pipe(
                tap((txPoints) => {
                    txPoints.forEach((txPoint) => (this.txPointsById[txPoint.id] = txPoint));
                })
            );
        } else {
            return of([]);
        }
    }

    private transformTicket(ticket: TgTicket): TicketReportRow {
        let printLocationType: string | undefined;
        let printLocation: string | undefined;
        let voidLocationType: string | undefined;
        let voidLocation: string | undefined;

        if (ticket.printedByType && ticket.printedById) {
            switch (ticket.printedByType) {
                case TriggerType.Machine:
                    printLocationType = TriggerTypePipe.stringToDescription(ticket.printedByType);
                    printLocation = MachineLabelPipe.machineToLabel(this.machinesById[ticket.printedById]);
                    break;
                case TriggerType.TransactionPoint:
                    printLocationType = $localize`Cage`;
                    printLocation = this.txPointsById[ticket.printedById]?.name;
                    break;
            }
        }

        if (ticket.resolvedByType && ticket.resolvedById) {
            switch (ticket.resolvedByType) {
                case TriggerType.Machine:
                    voidLocationType = TriggerTypePipe.stringToDescription(ticket.resolvedByType);
                    voidLocation = MachineLabelPipe.machineToLabel(this.machinesById[ticket.resolvedById]);
                    break;
                case TriggerType.TransactionPoint:
                    voidLocationType = $localize`Cage`;
                    voidLocation = this.txPointsById[ticket.resolvedById]?.name;
                    break;
            }
        }

        const voidUser = (ticket.operations ?? []).find((operation) => operation.type === TgTicketOperationType.Void)?.createdBy;

        return { ...ticket, printLocationType, printLocation, voidLocationType, voidLocation, voidUser };
    }

    private determineGeneratedBy(): string {
        const user: CmsUser | null = this.authenticationService.authenticatedUser;

        if (!user) {
            return '';
        }

        return user.firstName || user.lastName ? `${user.firstName} ${user.lastName}` : user.username;
    }

    private createRequestFiltersFromForm(_gamingDay: string, ticketType?: TgTicketType): Array<TgFilter<TgTicket>> {
        const filters: Array<TgFilter<TgTicket>> = [];

        if (ticketType) {
            filters.push({
                field: 'type',
                operator: TgFilterOperator.Eq,
                value: ticketType
            });
        }

        return filters;
    }

    protected abstract getColumnSourceTableName(): string;

    protected abstract getColumnIds(): string[];

    protected abstract createRequest(filters: Array<TgFilter<TgTicket>>, gamingDay: string): TgListRequest<TgTicket>;
}
