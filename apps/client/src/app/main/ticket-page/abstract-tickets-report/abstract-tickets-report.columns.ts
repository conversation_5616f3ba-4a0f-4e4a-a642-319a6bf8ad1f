import { Column } from '@tronius/frontend-ui';

export const liabilityReportColumnIds = [
    'ticketSuffix',
    'customerId',
    'printedAt',
    'printedByType',
    'printedById',
    'expirationDay',
    'amount'
];

export const expiredReportColumnIds = liabilityReportColumnIds.filter((id) => id !== 'expirationDay');

export const voidedReportColumnIds = [...expiredReportColumnIds, 'resolvedAt', 'resolvedByType', 'resolvedById', 'resolvedUser'];

export const getTicketColumnSourceColumns = (): Column[] => {
    return [
        {
            id: 'ticketSuffix',
            name: $localize`Number`,
            columnHeaderAlign: 'left',
            columnAlign: 'left',
            sortable: true
        },
        {
            id: 'customerId',
            name: $localize`Customer`,
            isHidden: true,
            columnHeaderAlign: 'left',
            columnAlign: 'left'
        },
        {
            id: 'printedAt',
            name: $localize`Ticket Printed`,
            type: 'datetime',
            columnHeaderAlign: 'left',
            columnAlign: 'left',
            sortable: true
        },
        {
            id: 'printLocationType',
            name: $localize`Print Location Type`,
            columnHeaderAlign: 'left',
            columnAlign: 'left',
            sortable: true
        },
        {
            id: 'printLocation',
            name: $localize`Print Location`,
            columnHeaderAlign: 'left',
            columnAlign: 'left',
            sortable: true
        },
        {
            id: 'expirationDay',
            name: $localize`Expires`,
            type: 'date',
            columnHeaderAlign: 'left',
            columnAlign: 'left',
            sortable: true
        },
        {
            id: 'resolvedAt',
            name: $localize`Ticket Voided`,
            type: 'datetime',
            columnHeaderAlign: 'left',
            columnAlign: 'left',
            sortable: true
        },
        {
            id: 'resolvedByType',
            name: $localize`Void Location Type`,
            columnHeaderAlign: 'left',
            columnAlign: 'left',
            sortable: true
        },
        {
            id: 'resolvedById',
            name: $localize`Void Location`,
            columnHeaderAlign: 'left',
            columnAlign: 'left'
        },
        {
            id: 'resolvedUser',
            name: $localize`Void User`,
            columnHeaderAlign: 'left',
            columnAlign: 'left',
            sortable: true
        },
        {
            id: 'amount',
            name: $localize`Amount`,
            type: 'currency',
            columnHeaderAlign: 'right',
            columnAlign: 'right',
            sortable: true
        }
    ];
};
