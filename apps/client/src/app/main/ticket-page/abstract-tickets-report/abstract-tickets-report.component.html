<tg-main-panel [contentPadding]="true" [fullHeight]="true" [scrollable]="false">
    <form [formGroup]="form" class="no-print">
        <mat-form-field>
            <mat-label i18n>Gaming Day</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="gamingDay" placeholder="MM/DD/YYYY" i18n-placeholder required />
            <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            @if (form.controls.gamingDay.errors?.required) {
                <mat-error i18n>Field is required</mat-error>
            }
        </mat-form-field>

        <mat-form-field>
            <mat-label i18n>Ticket Type</mat-label>
            <mat-select formControlName="ticketType">
                <mat-option [value]="null" i18n>All</mat-option>
                @for (ticketType of ticketTypes; track ticketType) {
                    <mat-option [value]="ticketType">{{ ticketType | ticketType }}</mat-option>
                }
            </mat-select>
        </mat-form-field>

        <button mat-flat-button color="primary" [disabled]="!form.valid" (click)="generateReport()" i18n>
            <mat-icon>assessment</mat-icon>
            Generate Report
        </button>

        <button mat-flat-button color="primary" (click)="print()"><mat-icon>print</mat-icon> <span i18n>Print</span></button>
    </form>

    <div *ngIf="requestData && requestData.gamingDay" id="print-ticket-liability-report">
        <div class="header">
            <div i18n class="operator-row print-only">{{ operatorName }}</div>
            <div class="title-row print-only">
                <div class="title" i18n>{{ reportName }}</div>
                <div class="generated-meta-info-container">
                    <div i18n class="label">Generated by:</div>
                    <div class="operator-and-time">{{ generatedBy }}, {{ generatedTime | tgDateTime }}</div>
                </div>
            </div>
            <div class="filters-row">
                <div class="filters print-only">
                    <div class="label-value">
                        <div i18n class="label">Gaming Day:</div>
                        <div class="value">
                            {{ requestData.gamingDay | tgDate }}
                        </div>
                    </div>
                    <div class="label-value">
                        <div i18n class="label">Ticket Type:</div>
                        <div class="value">
                            @if (requestData.ticketType) {
                                {{ requestData.ticketType | ticketType }}
                            } @else {
                                <ng-container i18n>All</ng-container>
                            }
                        </div>
                    </div>
                </div>
                <div class="total-amount label-value">
                    <div i18n class="label">Total Amount:</div>
                    <div class="value"><tg-primitive-text [value]="totalAmount" type="currency"></tg-primitive-text></div>
                </div>
            </div>
        </div>

        <tg-table
            *ngIf="dataSource && columnsSource"
            #table
            [enableSelection]="false"
            [columnsSource]="columnsSource"
            [dataSource]="dataSource"
            #sort="matSort"
            [matSort]="sort"
            [showPaginator]="false"
            matSortActive="printedAt"
            matSortDirection="asc"
            matSortDisableClear
        >
            <ng-template tgColumnCell="printLocationType" let-row="row">{{ row.printLocationType }}</ng-template>
            <ng-template tgColumnCell="printLocation" let-row="row">{{ row.printLocation }}</ng-template>
            <ng-template tgColumnCell="resolvedByType" let-row="row">{{ row.voidLocationType }}</ng-template>
            <ng-template tgColumnCell="resolvedById" let-row="row">{{ row.voidLocation }}</ng-template>
            <ng-template tgColumnCell="resolvedUser" let-row="row">{{ row.voidUser }}</ng-template>
        </tg-table>
        <div *ngIf="noMatchingItems" class="no-matching-items print-only" i18n>No matching items</div>
    </div>
</tg-main-panel>
