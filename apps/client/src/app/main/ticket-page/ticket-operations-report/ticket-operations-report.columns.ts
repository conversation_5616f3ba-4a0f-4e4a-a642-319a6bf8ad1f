import { Column, TgBrowserStorageColumnsSource } from '@tronius/frontend-ui';

const ticketOperationsReportColumns: Column[] = [
    {
        id: 'ticket.ticketSuffix',
        name: $localize`Ticket Number`,
        columnHeaderAlign: 'left',
        columnAlign: 'left',
        sortable: true
    },
    {
        id: 'ticket.type',
        name: $localize`Ticket Type`,
        columnHeaderAlign: 'left',
        columnAlign: 'left',
        sortable: true
    },
    {
        id: 'type',
        name: $localize`Operation Type`,
        columnHeaderAlign: 'left',
        columnAlign: 'left',
        sortable: true
    },
    {
        id: 'gamingDay',
        name: $localize`Gaming Day`,
        type: 'date',
        columnHeaderAlign: 'left',
        columnAlign: 'left',
        sortable: true
    },
    {
        id: 'triggeredAt',
        name: $localize`Time`,
        type: 'datetime',
        columnHeaderAlign: 'left',
        columnAlign: 'left',
        sortable: true
    },

    {
        id: 'triggeredByType',
        name: $localize`Location Type`,
        columnHeaderAlign: 'left',
        columnAlign: 'left',
        sortable: true
    },
    {
        id: 'triggeredById',
        name: $localize`Location`,
        columnHeaderAlign: 'left',
        columnAlign: 'left'
    },
    {
        id: 'debit',
        name: $localize`Debit`,
        type: 'currency',
        columnHeaderAlign: 'right',
        columnAlign: 'right',
        sortable: true
    },
    {
        id: 'credit',
        name: $localize`Credit`,
        type: 'currency',
        columnHeaderAlign: 'right',
        columnAlign: 'right',
        sortable: true
    }
];

export const ticketOperationsReportColumnsSource = new TgBrowserStorageColumnsSource(
    ticketOperationsReportColumns,
    'ticket-operations-report'
);
