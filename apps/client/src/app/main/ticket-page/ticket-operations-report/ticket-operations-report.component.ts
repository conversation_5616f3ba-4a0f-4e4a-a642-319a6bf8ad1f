import { CommonModule } from '@angular/common';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { Title } from '@angular/platform-browser';
import { getGamingDay } from '@cms/casino-operator';
import { MachineLabelPipe, MachineSelectorComponent } from '@cms/fe-components';
import { AuthenticationService } from '@cms/ng-client-authentication';
import { TgTicketOperation, TgTicketOperationType, TgTicketType } from '@cms/tickets';
import { SettingsService, TgLoggerFactory } from '@tronius/frontend-common';
import {
    TgArrayDataSource,
    TgBrowserStorageColumnsSource,
    TgDateModule,
    TgMainPanelModule,
    TgPrimitiveTextComponent,
    TgTableModule,
    createColumnSort
} from '@tronius/frontend-ui';
import { TgFilter, TgFilterOperator, TgListRequest, TgSortDirection, TriggerType } from '@tronius/shared-common';
import { CmsUser, MachineProfile, MachineProfileListItem, TransactionPoint } from '@tronius/shared-domain';
import { formatDate, subDays } from 'date-fns';
import { NgxPrintDirective } from 'ngx-print';
import { Observable, combineLatest, firstValueFrom, from, map, of, switchMap, tap } from 'rxjs';

import { ticketOperationsReportColumnsSource } from './ticket-operations-report.columns';
import { TriggerTypePipe } from '../../../common/trigger-type/trigger-type.pipe';
import { BackendService } from '../../../core/backend/backend.service';
import { TicketTypePipe } from '../ticket/type/ticket-type.pipe';
import { TicketOperationTypePipe } from '../ticket-operation/type/ticket-operation-type.pipe';

interface TicketOperationReportRow extends TgTicketOperation {
    locationType?: string;
    location?: string;
    credit?: number;
    debit?: number;
}

interface FormGroupType {
    start: FormControl<Date | null>;
    end: FormControl<Date | null>;
    ticketType: FormControl<TgTicketType | null>;
    ticketOperationType: FormControl<TgTicketOperationType | null>;
    triggerType: FormControl<TriggerType.Machine | TriggerType.TransactionPoint | null>;
    machineId: FormControl<string | null>;
}

@Component({
    selector: 'app-ticket-operations-report',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatButtonModule,
        MatIconModule,
        MatDatepickerModule,
        MatSelectModule,
        MatSnackBarModule,
        TgMainPanelModule,
        MachineSelectorComponent,
        NgxPrintDirective,
        TgDateModule,
        TgTableModule,
        TicketTypePipe,
        TicketOperationTypePipe,
        TriggerTypePipe,
        TgPrimitiveTextComponent,
        MachineLabelPipe
    ],
    templateUrl: './ticket-operations-report.component.html',
    styleUrls: ['./ticket-operations-report.component.scss']
})
export class TicketOperationsReportComponent implements OnInit, OnDestroy {
    private static readonly logger = TgLoggerFactory.getLogger(TicketOperationsReportComponent.name);

    protected readonly TgTicketType = TgTicketType;
    protected readonly TgTicketOperationType = TgTicketOperationType;
    protected readonly TriggerType = TriggerType;

    protected readonly maxIntervalInDays = 31;

    protected readonly ticketTypes = [TgTicketType.Cash, TgTicketType.PromoRestricted];
    protected readonly ticketOperationTypes = [
        TgTicketOperationType.Print,
        TgTicketOperationType.Redeem,
        TgTicketOperationType.Void,
        TgTicketOperationType.ReplaceValid,
        TgTicketOperationType.ReplaceExpired
    ];
    protected form: FormGroup<FormGroupType> = this.initForm();
    protected requestData?: typeof this.form.value;
    protected columnsSource: TgBrowserStorageColumnsSource = ticketOperationsReportColumnsSource;
    protected dataSource!: TgArrayDataSource<TgTicketOperation>;
    protected noMatchingItems = true;

    protected operatorName: string;
    protected generatedBy: string = this.determineGeneratedBy();
    protected generatedTime = new Date();
    protected selectedMachine?: MachineProfile | MachineProfileListItem;
    protected totalDebit = 0;
    protected totalCredit = 0;

    private readonly machinesById: Record<string, MachineProfile | MachineProfileListItem | undefined> = {};
    private readonly txPointsById: Record<string, TransactionPoint | undefined> = {};

    constructor(
        private readonly authenticationService: AuthenticationService,
        private readonly backendService: BackendService,
        private readonly settingsService: SettingsService,
        private readonly titleService: Title
    ) {
        const title = $localize`Ticket Operations Report`;
        this.titleService.setTitle(`${title} (${this.generatedBy})`);
        this.operatorName = this.settingsService.getSettings().propertyName;
    }

    ngOnInit(): void {
        this.generateReport();
    }

    ngOnDestroy(): void {
        this.titleService.setTitle('CMS');
    }

    protected generateReport(): void {
        this.dataSource = new TgArrayDataSource<TgTicketOperation>(async () => this.getReport(), createColumnSort(this.columnsSource));
        this.hideRedundantColumns();
    }

    protected print(): void {
        window.print();
    }

    private initForm(): FormGroup<FormGroupType> {
        const gamingDay = this.determinePreviousGamingDay();
        const start = new Date(gamingDay);

        return new FormGroup({
            start: new FormControl<Date | null>(start, Validators.required),
            end: new FormControl<Date | null>(start, Validators.required),
            ticketType: new FormControl<TgTicketType | null>(null),
            ticketOperationType: new FormControl<TgTicketOperationType | null>(null),
            triggerType: new FormControl<TriggerType.Machine | TriggerType.TransactionPoint | null>(null),
            machineId: new FormControl<string | null>(null)
        });
    }

    private determinePreviousGamingDay(): string {
        const { endOfDayHour, timezone } = this.settingsService.getSettings();
        return getGamingDay({ timestamp: subDays(Date.now(), 1).getTime(), endOfDayHour, timezone }).date;
    }

    private createRequest(): TgListRequest<TgTicketOperation> {
        const { start, end, ticketType, ticketOperationType, triggerType, machineId } = this.form.getRawValue();

        const filters: Array<TgFilter<TgTicketOperation>> = [];

        if (start && end) {
            filters.push({
                field: 'gamingDay',
                operator: TgFilterOperator.Gte,
                value: formatDate(start, 'yyyy-MM-dd')
            });

            filters.push({
                field: 'gamingDay',
                operator: TgFilterOperator.Lte,
                value: formatDate(end, 'yyyy-MM-dd')
            });
        }

        if (ticketType) {
            filters.push({
                field: 'ticket.type',
                operator: TgFilterOperator.Eq,
                value: ticketType
            });
        }

        if (ticketOperationType) {
            filters.push({
                field: 'type',
                operator: TgFilterOperator.Eq,
                value: ticketOperationType
            });
        }

        if (triggerType) {
            filters.push({
                field: 'triggeredByType',
                operator: TgFilterOperator.Eq,
                value: triggerType
            });
        }

        if (machineId) {
            filters.push({
                field: 'triggeredById',
                operator: TgFilterOperator.Eq,
                value: machineId
            });
        }

        filters.push({
            field: 'triggeredAt',
            operator: TgFilterOperator.Lte,
            value: Date.now()
        });

        const sorts = [{ field: 'triggeredAt', direction: TgSortDirection.ASC }];

        return { filters, relations: ['ticket'], sorts };
    }

    private async getReport(): Promise<TgTicketOperation[]> {
        this.requestData = this.form.getRawValue();

        const request: TgListRequest<TgTicketOperation> = this.createRequest();
        const request$ = from(this.backendService.getAllTicketOperations(request)).pipe(
            switchMap((items) => {
                this.noMatchingItems = !items.length;
                const machineIds = this.extractMachineIds(items);
                if (this.requestData?.machineId && !machineIds.includes(this.requestData.machineId)) {
                    machineIds.push(this.requestData.machineId);
                }

                return combineLatest([of(items), this.loadMachines(machineIds), this.loadTxPoints(this.extractTxPointIds(items))]);
            }),
            map(([items]) => items.map((item) => this.transformTicketOperation(item))),
            tap((rows) => {
                this.selectedMachine = this.requestData?.machineId ? this.machinesById[this.requestData.machineId] : undefined;
                this.totalDebit = rows.reduce((acc, row) => acc + (row.debit ?? 0), 0);
                this.totalCredit = rows.reduce((acc, row) => acc + (row.credit ?? 0), 0);
            })
        );

        return firstValueFrom(request$);
    }

    private extractMachineIds(items: TgTicketOperation[]): string[] {
        const ids = items.filter((item) => item.triggeredByType === TriggerType.Machine).map((item) => item.triggeredById);

        return Array.from(new Set(ids));
    }

    private extractTxPointIds(items: TgTicketOperation[]): string[] {
        const ids = items.filter((item) => item.triggeredByType === TriggerType.TransactionPoint).map((item) => item.triggeredById);

        return Array.from(new Set(ids));
    }

    private loadMachines(ids: string[]): Observable<MachineProfile[]> {
        if (ids.length) {
            return this.backendService.getMachineProfilesById(ids).pipe(
                tap((machines) => {
                    machines.forEach((machine) => (this.machinesById[machine.id] = machine));
                })
            );
        } else {
            return of([]);
        }
    }

    private loadTxPoints(ids: string[]): Observable<TransactionPoint[]> {
        if (ids.length) {
            return this.backendService.getTransactionPointsById(ids).pipe(
                tap((txPoints) => {
                    txPoints.forEach((txPoint) => (this.txPointsById[txPoint.id] = txPoint));
                })
            );
        } else {
            return of([]);
        }
    }

    private transformTicketOperation(ticketOperation: TgTicketOperation): TicketOperationReportRow {
        let locationType: string | undefined;
        let location: string | undefined;

        switch (ticketOperation.triggeredByType) {
            case TriggerType.Machine:
                locationType = TriggerTypePipe.stringToDescription(ticketOperation.triggeredByType);
                location = MachineLabelPipe.machineToLabel(this.machinesById[ticketOperation.triggeredById]);
                break;
            case TriggerType.TransactionPoint:
                locationType = $localize`Cage`;
                location = this.txPointsById[ticketOperation.triggeredById]?.name;
                break;
        }

        const isCredit = ticketOperation.type === TgTicketOperationType.Print;
        const amount = ticketOperation.ticket?.amount ?? 0;
        const credit = isCredit ? amount : undefined;
        const debit = isCredit ? undefined : amount;

        return { ...ticketOperation, locationType, location, credit, debit };
    }

    private determineGeneratedBy(): string {
        const user: CmsUser | null = this.authenticationService.authenticatedUser;

        if (!user) {
            return '';
        }

        return user.firstName || user.lastName ? `${user.firstName} ${user.lastName}` : user.username;
    }

    private hideRedundantColumns(): void {
        const { start, end, ticketType, ticketOperationType, triggerType, machineId } = this.form.getRawValue();

        this.hideColumn('gamingDay', start === end);
        this.hideColumn('ticket.type', !!ticketType);
        this.hideColumn('type', !!ticketOperationType);
        this.hideColumn('triggeredByType', !!triggerType);
        this.hideColumn('triggeredById', !!machineId);

        this.columnsSource.updateColumns();
    }

    private hideColumn(columnId: string, hide: boolean): void {
        const column = this.columnsSource.getColumnById(columnId);

        if (column) {
            column.isHidden = hide;
        }
    }
}
