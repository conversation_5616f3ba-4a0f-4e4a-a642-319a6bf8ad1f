@mixin print-styles {
    tg-main-panel {
        position: relative;
        max-height: 100%;
        background-color: white;

        .print-only {
            display: flex !important;
        }

        ::ng-deep .main-panel-content-wrapper {
            overflow: visible !important;
            padding: 0 !important;
        }

        .header {
            display: flex;
            flex-direction: column;
            margin-bottom: 0 !important;
            gap: 8px;

            .filters-row {
                padding: 8px 0;
                border-top: 1px solid #ebebeb;
                border-bottom: 1px solid #ebebeb;
            }
        }

        .totals-section {
            max-height: 100px;
        }

        h4,
        h5 {
            font-style: normal;
            font-weight: 500;
            letter-spacing: 0.15px;
            line-height: 24px; /* 150% */
        }

        h4 {
            font-size: 16px;
        }

        h5 {
            font-size: 14px;
        }

        tg-table {
            box-shadow: none !important;

            ::ng-deep {
                .mat-mdc-header-row {
                    height: auto !important;

                    .mat-mdc-header-cell {
                        padding: 8px;
                        border: 1px solid #ebebeb;
                        background: #fff;
                        font-size: 8px !important;
                    }
                }

                .mat-mdc-row {
                    height: auto;

                    .mat-mdc-cell {
                        padding: 4px 8px;
                        border: 1px solid #ebebeb;
                        background: #fff;
                        font-size: 8px !important;
                    }
                }
            }
        }
    }
}

:host {
    tg-main-panel {
        ::ng-deep {
            .main-panel-header .header-container {
                min-height: auto !important;
            }

            .main-panel-content-wrapper {
                display: flex;
                flex-direction: column;

                #print-hourly-win-reconciliation-report {
                    display: contents;
                    flex: 1;
                }
            }
        }

        form {
            display: flex;
            align-items: center;

            mat-form-field {
                width: 280px;
                margin-top: 20px;
                margin-right: 20px;
            }

            button {
                margin-right: 20px;
            }
        }

        #print-hourly-win-reconciliation-report {
            .header {
                margin-bottom: 20px;

                .label-value {
                    display: flex;
                    align-items: center;
                    gap: 2px;

                    .label {
                        color: #5a5a5a;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 12px; /* 120% */
                    }

                    .value {
                        color: #000;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 500;
                        letter-spacing: 0.4px;
                        line-height: 12px; /* 120% */
                    }
                }

                .operator-row {
                    display: flex;
                    justify-content: flex-end;
                    padding: 8px 0;
                    border-bottom: 1px solid #ebebeb;
                }

                .title-row {
                    display: flex;
                    align-items: baseline;
                    align-self: stretch;
                    gap: 8px;

                    .title {
                        display: flex;
                        flex: 1 0 0;
                        align-items: center;
                        color: #000;
                        font-size: 16px;
                        font-style: normal;
                        font-weight: 500;
                        letter-spacing: 0.15px;
                        line-height: 24px; /* 150% */
                    }

                    .generated-meta-info-container {
                        display: flex;
                        flex: 1 0 0;
                        align-items: center;
                        justify-content: flex-end;
                        gap: 2px;

                        .label {
                            color: #5a5a5a;
                            font-size: 12px;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 12px; /* 120% */
                        }

                        .operator-and-time {
                            color: #000;
                            font-size: 12px;
                            font-style: normal;
                            font-weight: 500;
                            letter-spacing: 0.15px;
                            line-height: 12px; /* 120% */
                            text-align: right;
                        }
                    }
                }

                .filters-row {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: flex-start;
                    justify-content: flex-end;
                    gap: 50px;

                    .filters {
                        display: flex;
                        flex: 1;
                        flex-wrap: wrap;
                        gap: 8px 20px;
                    }

                    .total-amount {
                        color: #000;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 500;
                        letter-spacing: 0.4px;
                        line-height: 12px; /* 120% */
                    }
                }
            }

            tg-table {
                min-height: 100px;
            }
        }
    }
}

@media print {
    ::ng-deep body {
        --mat-app-background-color: white;
    }

    :host {
        @include print-styles;
    }
}

::ng-deep {
    .print-mode body {
        --mat-app-background-color: white;

        app-hourly-win-reconciliation-report {
            @include print-styles;
        }
    }

    :not(.print-mode) app-hourly-win-reconciliation-report {
        .print-only {
            display: none !important;
        }
    }
}
