:host {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;

    mat-icon {
        width: 16px;
        height: 16px;
        font-size: 16px;

        &.check {
            color: #29c740;
        }

        &.error {
            color: #ff6057;
        }
    }
}

@media print {
    :host mat-icon {
        width: 12px !important;
        height: 12px !important;
        font-size: 12px !important;
    }
}

::ng-deep {
    .print-mode body app-reconciliation-amount-icon mat-icon {
        width: 12px !important;
        height: 12px !important;
        font-size: 12px !important;
    }
}
