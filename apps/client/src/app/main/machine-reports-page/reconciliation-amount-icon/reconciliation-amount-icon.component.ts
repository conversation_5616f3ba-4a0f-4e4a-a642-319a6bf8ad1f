import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MachineWinReconciliationReportMetrics, ReconciliationReportMetrics } from '@cms/machine-metrics';
import { TgPrimitiveTextComponent } from '@tronius/frontend-ui';

@Component({
    selector: 'app-reconciliation-amount-icon',
    standalone: true,
    imports: [CommonModule, MatIconModule, TgPrimitiveTextComponent],
    templateUrl: './reconciliation-amount-icon.component.html',
    styleUrl: './reconciliation-amount-icon.component.scss'
})
export class ReconciliationAmountIconComponent implements OnInit {
    private static readonly metricGroupMapping: Partial<Record<ReconciliationReportMetrics, ReconciliationReportMetrics[] | undefined>> =
        ReconciliationAmountIconComponent.assembleMetricGroupMapping();

    @Input()
    metricKey!: ReconciliationReportMetrics;

    @Input()
    metricValues!: MachineWinReconciliationReportMetrics;

    protected metricIsMatching = false;

    ngOnInit(): void {
        this.metricIsMatching = this.isMetricMatching();
    }

    /**
     * Determines if the current metric matches its corresponding group metrics
     */
    isMetricMatching(): boolean {
        const group = ReconciliationAmountIconComponent.metricGroupMapping[this.metricKey];
        const value = this.metricValues[this.metricKey];

        if (group?.length === 0 || typeof value !== 'number') {
            return false;
        } else if (!group?.length) {
            return true;
        }

        return group.some((groupMetricKey) => {
            return this.metricValues[groupMetricKey] === value;
        });
    }

    /**
     * Returns the metric group mapping for reconciliation validation.
     * This method defines which metrics should match for validation purposes.
     */
    private static assembleMetricGroupMapping(): Partial<Record<ReconciliationReportMetrics, ReconciliationReportMetrics[] | undefined>> {
        return {
            [ReconciliationReportMetrics.CashRevenue]: [ReconciliationReportMetrics.FinanceCashIn],
            [ReconciliationReportMetrics.TicketRevenue]: [ReconciliationReportMetrics.FinanceTicketsIn],
            [ReconciliationReportMetrics.TransferRevenue]: [ReconciliationReportMetrics.FinanceTransfersIn],
            [ReconciliationReportMetrics.TotalRevenue]: [ReconciliationReportMetrics.FinanceTotalIn],
            [ReconciliationReportMetrics.TicketExpenses]: [ReconciliationReportMetrics.FinanceTicketsOut],
            [ReconciliationReportMetrics.TransferExpenses]: [ReconciliationReportMetrics.FinanceTransfersOut],
            [ReconciliationReportMetrics.AccountingHandpays]: [ReconciliationReportMetrics.FinanceHandpays],
            [ReconciliationReportMetrics.AccountingTotalOut]: [ReconciliationReportMetrics.FinanceTotalOut],

            [ReconciliationReportMetrics.FinanceCashIn]: [ReconciliationReportMetrics.CashRevenue],
            [ReconciliationReportMetrics.FinanceTicketsIn]: [ReconciliationReportMetrics.TicketRevenue],
            [ReconciliationReportMetrics.FinanceTransfersIn]: [ReconciliationReportMetrics.TransferRevenue],
            [ReconciliationReportMetrics.FinanceTotalIn]: [ReconciliationReportMetrics.TotalRevenue],
            [ReconciliationReportMetrics.FinanceTicketsOut]: [ReconciliationReportMetrics.TicketExpenses],
            [ReconciliationReportMetrics.FinanceTransfersOut]: [ReconciliationReportMetrics.TransferExpenses],
            [ReconciliationReportMetrics.FinanceHandpays]: [ReconciliationReportMetrics.AccountingHandpays],
            [ReconciliationReportMetrics.FinanceTotalOut]: [ReconciliationReportMetrics.AccountingTotalOut],

            [ReconciliationReportMetrics.AccountingTotalWin]: [
                ReconciliationReportMetrics.FinanceTotalWin,
                ReconciliationReportMetrics.PlayTotalWin
            ],
            [ReconciliationReportMetrics.PlayTotalWin]: [
                ReconciliationReportMetrics.FinanceTotalWin,
                ReconciliationReportMetrics.AccountingTotalWin
            ],
            [ReconciliationReportMetrics.FinanceTotalWin]: [
                ReconciliationReportMetrics.AccountingTotalWin,
                ReconciliationReportMetrics.PlayTotalWin
            ]
        };
    }
}
