import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatIconModule } from '@angular/material/icon';
import { ReconciliationReportMetrics } from '@cms/machine-metrics';
import { TgPrimitiveTextComponent } from '@tronius/frontend-ui';

import { ReconciliationAmountIconComponent } from './reconciliation-amount-icon.component';

describe('ReconciliationAmountIconComponent', () => {
    let component: ReconciliationAmountIconComponent;
    let fixture: ComponentFixture<ReconciliationAmountIconComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ReconciliationAmountIconComponent, MatIconModule, TgPrimitiveTextComponent]
        }).compileComponents();

        fixture = TestBed.createComponent(ReconciliationAmountIconComponent);
        component = fixture.componentInstance;
    });

    const queryElement = <T extends HTMLElement>(selector: string): T | null => {
        return (fixture.nativeElement as HTMLElement).querySelector(selector);
    };

    describe('isMetricMatching', () => {
        it('should return true when metric matches its group values', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1000
            };

            fixture.detectChanges();
            expect(component.isMetricMatching()).toBe(true);
        });

        it('should return false when metric does not match its group values', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1500
            };

            fixture.detectChanges();
            expect(component.isMetricMatching()).toBe(false);
        });

        it('should return false when current metric value is null', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: null,
                [ReconciliationReportMetrics.FinanceCashIn]: 1000
            };

            fixture.detectChanges();
            expect(component.isMetricMatching()).toBe(false);
        });

        it('should return false when group metric value is null', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: null
            };

            fixture.detectChanges();
            expect(component.isMetricMatching()).toBe(false);
        });

        it('should handle multiple group metrics correctly', () => {
            component.metricKey = ReconciliationReportMetrics.PlayTotalWin;
            component.metricValues = {
                [ReconciliationReportMetrics.PlayTotalWin]: 500,
                [ReconciliationReportMetrics.FinanceTotalWin]: 500
            };

            fixture.detectChanges();
            expect(component.isMetricMatching()).toBe(true);
        });
    });

    describe('template rendering', () => {
        it('should display green check icon when metric is matching', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1000
            };

            fixture.detectChanges();

            const icon = queryElement<HTMLSpanElement>('mat-icon');
            expect(icon?.textContent?.trim()).toBe('check_circle');
            expect(icon?.classList.contains('check')).toBe(true);
        });

        it('should display red error icon when metric is not matching', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1500
            };

            fixture.detectChanges();

            const icon = queryElement<HTMLSpanElement>('mat-icon');
            expect(icon?.textContent?.trim()).toBe('error');
            expect(icon?.classList.contains('error')).toBe(true);
        });

        it('should display currency value using tg-primitive-text', () => {
            component.metricKey = ReconciliationReportMetrics.CashRevenue;
            component.metricValues = {
                [ReconciliationReportMetrics.CashRevenue]: 1000,
                [ReconciliationReportMetrics.FinanceCashIn]: 1000
            };

            fixture.detectChanges();

            const primitiveText = queryElement<HTMLSpanElement>('tg-primitive-text');
            expect(primitiveText).toBeTruthy();
        });
    });
});
