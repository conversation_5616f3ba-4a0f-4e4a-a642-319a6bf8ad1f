import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, TemplateRef, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ClientPermissionsModule } from '@cms/client-permissions';
import { ErrorUtil, TgLoggerFactory } from '@tronius/frontend-common';
import {
    ConfirmationDialogComponent,
    DistanceToNowComponent,
    DistanceToNowPipe,
    TableRowMenuAction,
    TgFiltersModule,
    TgMainPanelModule,
    TgPagedDataSource,
    TgPrimitiveTextComponent,
    TgTableModule,
    TilesComponent
} from '@tronius/frontend-ui';
import {
    ConnectionStatus,
    TgFilter,
    TgFilterOperator,
    TgPaginationRequest,
    TgPaginationResponse,
    TgSortDirection,
    TriggerType
} from '@tronius/shared-common';
import {
    CmsAction,
    MachineCurrent,
    MachineLockState,
    MachineStatus,
    OperationalStatus,
    SessionState,
    TgEvent
} from '@tronius/shared-domain';
import { addMilliseconds } from 'date-fns';
import { Observable, firstValueFrom, from, switchMap, tap } from 'rxjs';
import { map } from 'rxjs/operators';

import { BackendService } from '../../../../core/backend/backend.service';
import { MachineCurrentService } from '../../../../core/machine-current/machine-current.service';
import { EventIdPipe } from '../../../event-page/id/event-id.pipe';
import { defaultMachineSort } from '../machine.util';
import { MachineLiveViewColumnsSource } from '../machines-live-view.columns';
import { SelectOperationalStatusComponent } from '../select-operational-status-dialog/select-operational-status-dialog.component';

/* eslint-disable @typescript-eslint/sort-type-constituents */
type MachineStatusIconType =
    | 'disconnected'
    | 'machine-disconnected'
    | 'machine-connected'
    | 'machine-handpay'
    | 'no-active-session'
    | 'unknown-customer-session'
    | 'known-customer-session'
    | 'authenticated-session';
/* eslint-enable @typescript-eslint/sort-type-constituents */

@Component({
    selector: 'app-machines-live-view-tiles',
    standalone: true,
    imports: [
        CommonModule,
        MatButtonModule,
        MatDialogModule,
        MatIconModule,
        MatMenuModule,
        MatSlideToggleModule,
        MatSnackBarModule,
        MatTableModule,
        MatTooltipModule,
        RouterModule,
        TgTableModule,
        TgMainPanelModule,
        TgFiltersModule,
        TilesComponent,
        TgPrimitiveTextComponent,
        EventIdPipe,
        DistanceToNowPipe,
        FormsModule,
        DistanceToNowComponent,
        ConfirmationDialogComponent,
        ClientPermissionsModule
    ],
    templateUrl: './machines-live-view-tiles.component.html',
    styleUrls: ['./machines-live-view-tiles.component.scss']
})
export class MachinesLiveViewTilesComponent implements OnDestroy {
    private static readonly logger = TgLoggerFactory.getLogger(MachinesLiveViewTilesComponent.name);

    private static readonly eventsMaxPageCount = 5;
    private static readonly eventsMaxPageSize = 100;
    private static readonly eventsPerMachineCount = 2;
    // eslint-disable-next-line @typescript-eslint/no-magic-numbers
    private static readonly eventsMaxAgeInMs = 4 * 60 * 60 * 1000; // 4 hours
    private static readonly reloadDataIntervalInMs = 10000;

    @ViewChild('dialogTemplate', { static: false }) dialogTemplate?: TemplateRef<ConfirmationDialogComponent>;

    protected readonly TriggerType = TriggerType;
    protected readonly ConnectionStatus = ConnectionStatus;
    protected readonly MachineStatus = MachineStatus;

    protected columnsSource = new MachineLiveViewColumnsSource('machine-live-view-tiles', 'machine.location');
    protected dataSource = new TgPagedDataSource<MachineCurrent>((request) => this.loadData(request), defaultMachineSort);
    protected actions: Array<TableRowMenuAction<MachineCurrent>> = this.createActions();
    protected autoRefresh = false;
    protected statusIconByMachine: Record<string, MachineStatusIconType> = {};
    protected eventsByMachine: Record<string, TgEvent[]> = {};
    protected eventsRequestDate?: Date;
    protected machineWithoutEventSinceDate?: Date;

    protected readonly CmsAction = CmsAction;

    private reloadDataIntervalId: ReturnType<typeof setTimeout> | undefined;
    private machines: MachineCurrent[] = [];

    constructor(
        private readonly route: ActivatedRoute,
        private readonly router: Router,
        private readonly backendService: BackendService,
        private readonly machineCurrentService: MachineCurrentService,
        private readonly snackBar: MatSnackBar,
        private readonly matDialog: MatDialog,
        private readonly dialog: MatDialog
    ) {}

    ngOnDestroy(): void {
        if (this.reloadDataIntervalId) {
            clearInterval(this.reloadDataIntervalId);
        }
    }

    protected onRefresh(): void {
        this.eventsByMachine = {};
        this.eventsRequestDate = undefined;
        this.machineWithoutEventSinceDate = undefined;

        void this.dataSource.reload();
    }

    protected onAutoRefreshChange(autoRefresh: boolean): void {
        this.autoRefresh = autoRefresh;

        if (autoRefresh) {
            this.reloadDataIntervalId = setInterval(
                // eslint-disable-next-line @typescript-eslint/no-misused-promises
                async () => this.dataSource.reload(),
                MachinesLiveViewTilesComponent.reloadDataIntervalInMs
            );
        } else if (this.reloadDataIntervalId) {
            clearInterval(this.reloadDataIntervalId);
            this.reloadDataIntervalId = undefined;
        }
    }

    protected async resetHandpay(id: string): Promise<void> {
        try {
            await this.backendService.resetHandpay(id);
            this.snackBar.open($localize`Reset handpay request send to the machine.`, undefined, { duration: 5000 });
            // TODO maybe we should wait for an event indicating handpay reset successfully (status change or event).
        } catch (error) {
            MachinesLiveViewTilesComponent.logger.error('Handpay action failed', ErrorUtil.extractErrorMessage(error));
            this.snackBar.open($localize`Failed to send reset handpay request to the machine.`, $localize`Close`);
        }
    }

    private loadData(request: TgPaginationRequest<MachineCurrent>): Observable<TgPaginationResponse<MachineCurrent>> {
        return this.backendService.getMachineState(request).pipe(
            tap((response) => {
                this.machines = response.items;
                response.items.forEach((machine) => {
                    this.statusIconByMachine[machine.id] = this.determineStatusIcon(machine);
                });
            }),
            switchMap((state) => {
                return from(this.loadEvents()).pipe(map(() => state));
            })
        );
    }

    private async loadEvents(): Promise<void> {
        let eventsByMachine: Record<string, TgEvent[]> = {};

        let start = 0;
        const count = MachinesLiveViewTilesComponent.eventsMaxPageSize;

        const toDate = new Date();
        const fromDate: Date = addMilliseconds(toDate, -MachinesLiveViewTilesComponent.eventsMaxAgeInMs);

        let oldestEventDate: Date | undefined;
        let done = false;

        do {
            const response = await firstValueFrom(this.loadEventsPage(fromDate, toDate, start, count));

            if (!response.items.length) {
                break;
            }

            eventsByMachine = this.groupEventsByMachine(response.items, eventsByMachine);

            start += count;
            oldestEventDate = new Date(response.items[response.items.length - 1].triggeredAt);
            done =
                start + 1 >= response.total ||
                (start + 1) / count >= MachinesLiveViewTilesComponent.eventsMaxPageCount ||
                !this.someMachinesNeedMoreEvents(this.eventsByMachine);
        } while (!done);

        this.eventsByMachine = this.mergeEvents(eventsByMachine, this.eventsByMachine);
        this.eventsRequestDate = toDate;
        this.machineWithoutEventSinceDate = oldestEventDate ?? fromDate;
    }

    private loadEventsPage(fromDate: Date, toDate: Date, start: number, count: number): Observable<TgPaginationResponse<TgEvent>> {
        const machineIds = this.machines.map((machine) => machine.id);
        const machineIdFilter =
            // eslint-disable-next-line @typescript-eslint/no-magic-numbers
            machineIds.length > 20
                ? { field: 'machineId', operator: TgFilterOperator.IsNotNull }
                : { field: 'machineId', value: machineIds, operator: TgFilterOperator.In };

        const fromFilter = { field: 'triggeredAt', value: fromDate.getTime(), operator: TgFilterOperator.Gt };
        const toFilter = { field: 'triggeredAt', value: toDate.getTime(), operator: TgFilterOperator.Lte };
        const filters: Array<TgFilter<TgEvent>> = [fromFilter, toFilter, machineIdFilter];

        const sorts = [{ field: 'triggeredAt', direction: TgSortDirection.DESC }];

        const request = { start, count, filters, sorts };
        return this.backendService.getEvents(request);
    }

    private groupEventsByMachine(events: TgEvent[], initialEventsByMachine: Record<string, TgEvent[]>): Record<string, TgEvent[]> {
        return events.reduce<Record<string, TgEvent[]>>((eventByMachine, event) => {
            const machineEvents = event.machineId ? (eventByMachine[event.machineId] ?? []) : [];
            if (event.machineId && machineEvents.length < MachinesLiveViewTilesComponent.eventsPerMachineCount) {
                machineEvents.push(event);
                // eslint-disable-next-line no-param-reassign
                eventByMachine[event.machineId] = machineEvents;
            }

            return eventByMachine;
        }, initialEventsByMachine);
    }

    private someMachinesNeedMoreEvents(eventsByMachine: Record<string, TgEvent[]>): boolean {
        return Object.values(eventsByMachine).some((events) => events.length < MachinesLiveViewTilesComponent.eventsPerMachineCount);
    }

    private mergeEvents(newEvents: Record<string, TgEvent[]>, oldEvents: Record<string, TgEvent[]>): Record<string, TgEvent[]> {
        return Object.entries(newEvents).reduce<Record<string, TgEvent[]>>((eventsByMachine, [key, events]) => {
            // eslint-disable-next-line no-param-reassign
            eventsByMachine[key] = [...events, ...(oldEvents[key] ?? [])].slice(0, MachinesLiveViewTilesComponent.eventsPerMachineCount);
            return eventsByMachine;
        }, {});
    }

    // eslint-disable-next-line consistent-return
    private determineStatusIcon(machine: MachineCurrent): MachineStatusIconType {
        if (machine.device?.connectionStatus !== ConnectionStatus.Connected) {
            return 'disconnected';
        }

        if (machine.connectionStatus !== ConnectionStatus.Connected) {
            return 'machine-disconnected';
        }

        if (machine.status === MachineStatus.Handpay) {
            return 'machine-handpay';
        }

        switch (machine.sessionState) {
            case SessionState.Active:
                return 'known-customer-session';
            case SessionState.Inactive:
            default:
                return 'no-active-session';
        }
    }

    private createActions(): Array<TableRowMenuAction<MachineCurrent>> {
        return [
            {
                title: $localize`Manual funds transfer to machine`,
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'cashier', 'aft-in'], {
                        queryParams: { machineId: row.id },
                        relativeTo: this.route
                    }),
                show: (row: MachineCurrent) =>
                    [MachineStatus.Available, MachineStatus.Busy].includes(row.status) &&
                    row.connectionStatus === ConnectionStatus.Connected
            },
            {
                title: $localize`Reset handpay`,
                icon: 'lock_reset',
                action: async (row: MachineCurrent) => this.resetHandpay(row.id),
                show: (row: MachineCurrent) => row.status === MachineStatus.Handpay && row.connectionStatus === ConnectionStatus.Connected
            },
            {
                title: $localize`Machine meters`,
                icon: 'analytics',
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'meter', 'raw-current'], {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        queryParams: { 'filters-machine': `in;${row.id}` },
                        relativeTo: this.route
                    }),
                cmsPermissions: [CmsAction.MeterListRawCurrent]
            },
            {
                title: $localize`Machine events`,
                icon: 'notifications',
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'events', 'events'], {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        queryParams: { 'filters-machineId': `in;${row.id}` },
                        relativeTo: this.route
                    }),
                cmsPermissions: [CmsAction.EventsList]
            },
            {
                title: $localize`Set Operational Status`,
                icon: 'flag',
                action: (row: MachineCurrent) => {
                    this.openSelectOperationalStatusDialog(row);
                },
                cmsPermissions: [CmsAction.MachineUpdateOperationalStatus]
            },
            {
                title: $localize`Lock Machine`,
                icon: 'lock',
                action: (row: MachineCurrent) => this.displayLockStateConfirmationDialog(row, MachineLockState.Locked),
                show: (row: MachineCurrent) => row.lockState === MachineLockState.Unlocked,
                cmsPermissions: [CmsAction.MachineUpdateLockState]
            },
            {
                title: $localize`Unlock Machine`,
                icon: 'lock_reset',
                action: (row: MachineCurrent) => this.displayLockStateConfirmationDialog(row, MachineLockState.Unlocked),
                show: (row: MachineCurrent) => row.lockState === MachineLockState.Locked,
                cmsPermissions: [CmsAction.MachineUpdateLockState]
            },
            {
                title: $localize`Reboot MED`,
                icon: 'restart_alt',
                fontSet: 'material-symbols-rounded-fill',
                show: (row: MachineCurrent) =>
                    !!(row.deviceId && row.device?.ipAddress) && row.device.connectionStatus === ConnectionStatus.Connected,
                action: (row: MachineCurrent) => this.confirmRebootMed(row),
                cmsPermissions: [CmsAction.MachineRebootMed]
            },
            {
                title: $localize`Restart MED App`,
                icon: 'refresh',
                fontSet: 'material-symbols-rounded-fill',
                show: (row: MachineCurrent) =>
                    !!(row.deviceId && row.device?.ipAddress) && row.device.connectionStatus === ConnectionStatus.Connected,
                action: (row: MachineCurrent) => this.confirmRestartMedApp(row),
                cmsPermissions: [CmsAction.MachineRestartMedApp]
            },
            {
                title: $localize`Ticket Operations`,
                icon: 'barcode',
                fontSet: 'material-symbols-rounded',
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'tickets', 'operations'], {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        queryParams: { 'filters-triggeredById': `in;${row.id}` },
                        relativeTo: this.route
                    }),
                cmsPermissions: [CmsAction.TicketOperationList]
            },
            {
                title: $localize`Handpay Operations`,
                icon: 'attribution',
                fontSet: 'material-symbols-rounded-fill',
                action: (row: MachineCurrent) =>
                    // eslint-disable-next-line no-void
                    void this.router.navigate(['../..', 'handpays', 'operations'], {
                        // eslint-disable-next-line @typescript-eslint/naming-convention
                        queryParams: { 'filters-triggeredById': `in;${row.id}` },
                        relativeTo: this.route
                    }),
                cmsPermissions: [CmsAction.HandpayOperationList]
            }
        ];
    }

    private openSelectOperationalStatusDialog(machineCurrent: MachineCurrent): void {
        const dialogRef = this.matDialog.open<SelectOperationalStatusComponent, MachineCurrent, OperationalStatus | undefined>(
            SelectOperationalStatusComponent,
            {
                data: machineCurrent
            }
        );

        dialogRef.afterClosed().subscribe((result: OperationalStatus | undefined) => {
            if (result) {
                this.updateMachineCurrentOperationalStatus(result, machineCurrent);
            }
        });
    }

    private updateMachineCurrentOperationalStatus(operationalStatus: OperationalStatus, rowMachineCurrent: MachineCurrent): void {
        this.backendService.updateMachineCurrentOperationalStatus(rowMachineCurrent.id, operationalStatus).subscribe({
            next: () => {
                void this.dataSource.reload();
                this.snackBar.open($localize`Machine Operational Status updated`, undefined, { duration: 5000 });
            },
            error: (error: unknown) => {
                MachinesLiveViewTilesComponent.logger.error(
                    'Machine Operational Status update failed',
                    ErrorUtil.extractErrorMessage(error)
                );
                this.snackBar.open($localize`Machine Operational Status update failed`, $localize`Close`);
            }
        });
    }

    private displayLockStateConfirmationDialog(machineCurrent: MachineCurrent, lockState: MachineLockState): void {
        if (this.dialogTemplate) {
            this.dialog
                .open(this.dialogTemplate)
                .afterClosed()
                .subscribe((result) => {
                    if (result) {
                        this.updateMachineCurrentLockState(machineCurrent, lockState);
                    }
                });
        }
    }

    private updateMachineCurrentLockState(machineCurrent: MachineCurrent, lockState: MachineLockState): void {
        this.backendService.updateMachineCurrentLockState(machineCurrent.id, lockState).subscribe({
            next: () => {
                void this.dataSource.reload();
                this.snackBar.open($localize`Machine Lock State updated`, undefined, { duration: 5000 });
            },
            error: (error: unknown) => {
                MachinesLiveViewTilesComponent.logger.error('Machine Lock State update failed', ErrorUtil.extractErrorMessage(error));
                this.snackBar.open($localize`Machine Lock State update failed`, $localize`Close`);
            }
        });
    }

    private confirmAction(title: string, content: string, action: () => void): void {
        this.dialog
            .open(ConfirmationDialogComponent, {
                data: { title, content }
            })
            .afterClosed()
            .subscribe((result) => {
                if (result) {
                    action();
                }
            });
    }

    private confirmRebootMed(machineCurrent: MachineCurrent): void {
        this.confirmAction(
            $localize`Confirm Reboot MED`,
            $localize`Do you want to reboot the MED for machine "${machineCurrent.machine?.location}"?`,
            () => this.rebootMed(machineCurrent)
        );
    }

    private confirmRestartMedApp(machineCurrent: MachineCurrent): void {
        this.confirmAction(
            $localize`Confirm Restart MED App`,
            $localize`Do you want to restart the MED application for machine "${machineCurrent.machine?.location}"?`,
            () => this.restartMedApp(machineCurrent)
        );
    }

    private rebootMed(machineCurrent: MachineCurrent): void {
        this.machineCurrentService.rebootMed(machineCurrent.id).subscribe({
            next: () => {
                this.snackBar.open($localize`MED reboot command sent successfully.`, undefined, {
                    duration: 5000
                });
                void this.dataSource.reload();
            },
            error: (error) => {
                MachinesLiveViewTilesComponent.logger.error('Reboot MED action failed', ErrorUtil.extractErrorMessage(error));
                this.snackBar.open($localize`Failed to send reboot MED command.`, $localize`Close`);
            }
        });
    }

    private restartMedApp(machineCurrent: MachineCurrent): void {
        this.machineCurrentService.restartMedApp(machineCurrent.id).subscribe({
            next: () => {
                this.snackBar.open($localize`MED app restart command sent successfully.`, undefined, {
                    duration: 5000
                });
                void this.dataSource.reload();
            },
            error: (error) => {
                MachinesLiveViewTilesComponent.logger.error('Restart MED app action failed', ErrorUtil.extractErrorMessage(error));
                this.snackBar.open($localize`Failed to send restart MED app command.`, $localize`Close`);
            }
        });
    }
}
