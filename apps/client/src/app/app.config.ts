import { registerLocaleData } from '@angular/common';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import localeSl from '@angular/common/locales/sl';
import { APP_INITIALIZER, ApplicationConfig, DEFAULT_CURRENCY_CODE, importProvidersFrom } from '@angular/core';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MAT_RIPPLE_GLOBAL_OPTIONS, MatDateFormats } from '@angular/material/core';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';
import { MAT_ICON_DEFAULT_OPTIONS } from '@angular/material/icon';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import { AuthenticationService, NgClientAuthenticationModule } from '@cms/ng-client-authentication';
import {
    CountryService,
    EnvironmentResolver,
    SettingsService,
    TgLogLevel,
    TgLoggerFactory,
    WorkstationTransactionPointService
} from '@tronius/frontend-common';
import { CmsDateFnsAdapter, DEFAULT_TIMEZONE, cmsDateFnsFormats, dateFnsLocales } from '@tronius/frontend-ui';
import { CmsPermissionRepository, CmsPermissionService } from '@tronius/shared-domain';
import { Locale } from 'date-fns';
import { enUS } from 'date-fns/locale';

import { appRoutes } from './app.routes';
import { BackendService } from './core/backend/backend.service';
import { LoyaltyService } from './core/loyalty/loyalty.service';
import { MachineCurrentService } from './core/machine-current/machine-current.service';
import { MachineGameTypeService } from './core/machine-game-type/machine-game-type.service';
import { AuditService } from './core/services/audit.service';

const logger = TgLoggerFactory.getLogger('ApplicationConfig');

const initializeLogger = (environmentResolver: EnvironmentResolver): void => {
    const resolvedEnvironment = environmentResolver.getEnvironment();
    TgLoggerFactory.initialize(resolvedEnvironment.logAppender, resolvedEnvironment.logLevel);
    logger.log('Using log level: ', TgLogLevel[TgLoggerFactory.getCurrentLogLevel()]);
};

const initialize =
    (
        authenticationService: AuthenticationService,
        environmentResolver: EnvironmentResolver,
        backendService: BackendService,
        loyaltyService: LoyaltyService,
        machineCurrentService: MachineCurrentService,
        machineGameTypeService: MachineGameTypeService,
        auditService: AuditService,
        countryService: CountryService,
        settingsService: SettingsService,
        workstationTransactionPointService: WorkstationTransactionPointService
    ) =>
    async (): Promise<void> => {
        initializeLogger(environmentResolver);
        registerLocaleData(localeSl, 'sl-SI');

        await authenticationService.initialize();

        // TODO: fix this
        if (authenticationService.isAuthenticated) {
            backendService.init(environmentResolver);
            loyaltyService.init(environmentResolver);
            machineCurrentService.init(environmentResolver);
            machineGameTypeService.init(environmentResolver);
            auditService.init(environmentResolver);
            await Promise.all([countryService.init(), settingsService.init(), workstationTransactionPointService.init()]);
        }
    };

const defaultCurrencyCodeFactory = (settingsService: SettingsService): string => {
    return settingsService.getSettings().currency;
};

const defaultTimezoneFactory = (settingsService: SettingsService): string | undefined => {
    return settingsService.getSettings().timezone;
};

/**
 * `MAT_DATE_LOCALE` factory that uses date-fns. Affects date picker.
 */
const matLocaleFactory = (settingsService: SettingsService): Locale => {
    return dateFnsLocales[settingsService.getSettings().locale] ?? enUS;
};

/**
 * `MAT_DATE_FORMATS` factory. Affects date picker.
 */
const matDateFormatsFactory = (_settingsService: SettingsService): MatDateFormats => {
    // TODO Get date format from settings and return value with updated cmsDateFnsFormats.display.dateInput
    return cmsDateFnsFormats;
};

export const appConfig: ApplicationConfig = {
    providers: [
        importProvidersFrom([NgClientAuthenticationModule]),
        // For debug tracing instead use
        // provideRouter(appRoutes, withDebugTracing()),
        provideRouter(appRoutes),
        provideAnimations(),
        provideHttpClient(withInterceptorsFromDi()),
        {
            provide: APP_INITIALIZER,
            useFactory: initialize,
            deps: [
                AuthenticationService,
                EnvironmentResolver,
                BackendService,
                LoyaltyService,
                MachineCurrentService,
                MachineGameTypeService,
                AuditService,
                CountryService,
                SettingsService,
                WorkstationTransactionPointService
            ],
            multi: true
        },
        { provide: DEFAULT_CURRENCY_CODE, useFactory: defaultCurrencyCodeFactory, deps: [SettingsService] },
        { provide: DEFAULT_TIMEZONE, useFactory: defaultTimezoneFactory, deps: [SettingsService] },
        // Default icons should be round
        { provide: MAT_ICON_DEFAULT_OPTIONS, useValue: { fontSet: 'material-symbols-rounded' } },
        { provide: MAT_FORM_FIELD_DEFAULT_OPTIONS, useValue: { appearance: 'outline', floatLabel: 'always' } },
        { provide: MAT_DATE_LOCALE, useFactory: matLocaleFactory, deps: [SettingsService] },
        { provide: MAT_DATE_FORMATS, useFactory: matDateFormatsFactory, deps: [SettingsService] },
        {
            provide: MAT_RIPPLE_GLOBAL_OPTIONS,
            useValue: {
                disabled: true,
                animation: {
                    enterDuration: 0,
                    exitDuration: 0
                }
            }
        },
        { provide: DateAdapter, useClass: CmsDateFnsAdapter, deps: [MAT_DATE_LOCALE] },
        { provide: CmsPermissionService, useValue: new CmsPermissionService(new CmsPermissionRepository()) }
    ]
};
