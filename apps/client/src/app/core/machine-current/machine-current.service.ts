import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EnvironmentResolver } from '@tronius/frontend-common';
import { Observable } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class MachineCurrentService {
    private baseUrl!: string;

    constructor(private readonly http: HttpClient) {}

    init(environmentResolver: EnvironmentResolver): void {
        this.baseUrl = environmentResolver.getEnvironment().apiUrl;
    }

    /**
     * Reboot MED for a machine
     * @param machineId - The machine ID
     * @returns Observable<void>
     */
    rebootMed(machineId: string): Observable<void> {
        return this.http.post<undefined>(`${this.baseUrl}/machine-current/${machineId}/reboot-med`, {});
    }

    /**
     * Restart MED application for a machine
     * @param machineId - The machine ID
     * @returns Observable<void>
     */
    restartMedApp(machineId: string): Observable<void> {
        return this.http.post<undefined>(`${this.baseUrl}/machine-current/${machineId}/restart-med-app`, {});
    }
}
