import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
    CashReconciliationReportRequest,
    MachineCashOperationsMetrics,
    MachineCashReconciliationReport,
    MachineCashReconciliationReportProps
} from '@cms/cash-operations';
import { CasinoSettings } from '@cms/casino-operator';
import { CreateDenomination, Denomination, UpdateDenomination } from '@cms/denominations';
import {
    DeviceFirmwareListingItem,
    DeviceFirmwareType,
    ListDeviceFirmwareRequestProps,
    ListDeviceFirmwareResponse,
    PrepareDeviceFirmwareUploadResponse,
    PrepareDeviceFirmwareUploadResponseProps,
    PublishDeviceFirmwareRequestProps
} from '@cms/device-firmware';
import {
    CompleteMachineFundsTransfer,
    CreateManualFundsTransferIn,
    MachineFundsTransfer,
    MachineFundsTransferOperation
} from '@cms/machine-funds-transfers';
import { MachineGameType, MachineGameTypeProps } from '@cms/machine-game-types';
import {
    CreateHandpay,
    Handpay,
    HandpayOperation,
    HandpayReconciliationReportRequest,
    MachineHandpayMetrics,
    MachineHandpayReconciliationReport,
    MachineHandpayReconciliationReportProps
} from '@cms/machine-handpays';
import {
    MachineWinReconciliationReportItem,
    MachineWinReconciliationReportItemProps,
    WinReconciliationDailyRequest,
    WinReconciliationHourlyRequest
} from '@cms/machine-metrics';
import { deserialize } from '@cms/serialization';
import {
    TgCreateTicketRequest,
    TgRedeemManualTicketRequest,
    TgRedeemTicketRequest,
    TgTicket,
    TgTicketOperation,
    TgTicketType,
    TgVoidTicketRequest,
    TicketReconciliationReportItem,
    TicketReconciliationReportItemProps,
    TicketReconciliationReportRequest
} from '@cms/tickets';
import { EnvironmentResolver } from '@tronius/frontend-common';
import {
    ContentTypeEnum,
    InternalServerError,
    TgFilterOperator,
    TgListRequest,
    TgListResponse,
    TgPaginationRequest,
    TgPaginationResponse,
    TgUtils
} from '@tronius/shared-common';
import {
    ClearanceReportRequest,
    CmsUser,
    CreateCmsUser,
    CreateDevice,
    CreateJackpotLevelRequest,
    CreateMachineProfile,
    CreateManufacturer,
    CreateTransactionPoint,
    Device,
    DeviceType,
    JackpotClientInfo,
    JackpotLevel,
    JackpotLevelHistory,
    JackpotLevelHistoryData,
    ListJackpotClientInfo,
    ListJackpotLevels,
    MachineCurrent,
    MachineLockState,
    MachineProfile,
    MachineProfileListItem,
    MachineProfileListItemProps,
    Manufacturer,
    MeterRawCurrent,
    MeterRawSnapshot,
    Metric,
    MetricClearance,
    MetricDaily,
    MetricDailyGroupedByMachineResponse,
    MetricDailyGroupedRequest,
    MetricDailyTotalResponse,
    MetricHourly,
    MetricHourlyGroupedByMachineResponse,
    MetricHourlyGroupedRequest,
    MetricHourlyTotalResponse,
    OperationalStatus,
    PendingDevice,
    ResetCmsUserPassword,
    SaveJackpotLevelResponse,
    SetJackpotLevelValueRequest,
    SetJackpotLevelValueResponse,
    TgCreatePrinter,
    TgEvent,
    TgPrinter,
    TgUpdatePrinter,
    TransactionPoint,
    UpdateCmsUser,
    UpdateDevice,
    UpdateJackpotLevelRequest,
    UpdateMachineLockState,
    UpdateMachineOperationalStatus,
    UpdateMachineProfile,
    UpdateManufacturer,
    UpdateTransactionPoint
} from '@tronius/shared-domain';
import { addDays, addMinutes, startOfDay } from 'date-fns';
import { Observable, firstValueFrom, lastValueFrom, take } from 'rxjs';
import { map } from 'rxjs/operators';

export type HandpayActionType = 'authorize' | 'pay-in-property' | 'reverse' | 'transfer-to-wallet' | 'void';

export type MachineFundsTransferActionType = 'cancel' | 'complete';

@Injectable({
    providedIn: 'root'
})
export class BackendService {
    private static readonly gamingDayOffset = '5:30';
    private static readonly cashTicketValidityDays = 7;
    private static readonly promoTicketValidityDays = 1;

    private baseUrl!: string;

    constructor(private readonly http: HttpClient) {}

    init(environmentResolver: EnvironmentResolver): void {
        this.baseUrl = environmentResolver.getEnvironment().apiUrl;
    }

    getCashReconciliationReport(
        request: CashReconciliationReportRequest
    ): Observable<Array<{ metrics: MachineCashOperationsMetrics; machine: MachineProfileListItem; meterMetrics: null }>> {
        return this.http
            .get<MachineCashReconciliationReportProps[]>(`${this.baseUrl}/cash-operations/reconciliation-report`, {
                params: { ...request }
            })
            .pipe(
                map((response) =>
                    response
                        .map((props) => MachineCashReconciliationReport.create(props))
                        // This line is temporary until a unified endpoint for handpay and handpay meters is implemented
                        // eslint-disable-next-line spellcheck/spell-checker
                        .map((mhr) => ({ metrics: mhr.metrics, machine: mhr.machine, meterMetrics: null }))
                )
            );
    }

    getHourlyMachineWinReconciliationReport(request: WinReconciliationHourlyRequest): Observable<MachineWinReconciliationReportItem[]> {
        return this.http
            .get<MachineWinReconciliationReportItemProps[]>(`${this.baseUrl}/machine-hourly-metrics/reconciliation-report`, {
                params: { ...request, from: request.from.toISOString(), to: request.to.toISOString() }
            })
            .pipe(map((response) => response.map((props) => MachineWinReconciliationReportItem.create(props))));
    }

    getDailyMachineWinReconciliationReport(request: WinReconciliationDailyRequest): Observable<MachineWinReconciliationReportItem[]> {
        return this.http
            .get<MachineWinReconciliationReportItemProps[]>(`${this.baseUrl}/machine-daily-metrics/reconciliation-report`, {
                params: { ...request }
            })
            .pipe(map((response) => response.map((props) => MachineWinReconciliationReportItem.create(props))));
    }

    async getPropertySettings(): Promise<CasinoSettings> {
        return firstValueFrom(this.http.get<CasinoSettings>(`${this.baseUrl}/casino/property/settings`));
    }

    getTicketOperations(request: TgPaginationRequest<TgTicketOperation>): Observable<TgPaginationResponse<TgTicketOperation>> {
        return this.http.get<TgPaginationResponse<TgTicketOperation>>(`${this.baseUrl}/payment/ticket-operation`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    async getAllTicketOperations(request: TgListRequest<TgTicketOperation>): Promise<TgTicketOperation[]> {
        const pageSize = 100; // Reasonable page size to avoid overwhelming the server
        const maxItems = 10000; // Safety limit to prevent infinite loops

        let offset = 0;
        let limit = maxItems;

        let allItems: TgTicketOperation[] = [];
        do {
            const { items, total } = await firstValueFrom(this.getTicketOperations({ ...request, start: offset, count: pageSize }));
            allItems = [...allItems, ...items];

            offset += items.length;
            limit = Math.min(total, maxItems);
        } while (offset < limit);

        return allItems;
    }

    async getAll<T>(
        request: TgListRequest<T>,
        fetchPageFn: (request: TgPaginationRequest<T>) => Observable<TgPaginationResponse<T>>
    ): Promise<T[]> {
        const pageSize = 100; // Reasonable page size to avoid overwhelming the server
        const maxItems = 10000; // Safety limit to prevent infinite loops

        let offset = 0;
        let limit = maxItems;

        let allItems: T[] = [];
        do {
            const { items, total } = await firstValueFrom(fetchPageFn({ ...request, start: offset, count: pageSize }));
            allItems = [...allItems, ...items];

            offset += items.length;
            limit = Math.min(total, maxItems);
        } while (offset < limit);

        return allItems;
    }

    getTickets(request: TgPaginationRequest<TgTicket>): Observable<TgPaginationResponse<TgTicket>> {
        return this.http
            .get<TgPaginationResponse<TgTicket>>(`${this.baseUrl}/payment/ticket`, {
                params: BackendService.paginationRequestToHttpParams(request)
            })
            .pipe(
                map((response) => {
                    const items = response.items.map((item) => {
                        const printedAt = item.printedAt ? new Date(item.printedAt) : null;
                        const resolvedAt = item.resolvedAt ? new Date(item.resolvedAt) : null;
                        const expiredAt = item.expiredAt ? new Date(item.expiredAt) : null;

                        return { ...item, printedAt, resolvedAt, expiredAt };
                    });

                    return { ...response, items };
                })
            );
    }

    async getAllTickets(request: TgListRequest<TgTicket>): Promise<TgTicket[]> {
        return this.getAll<TgTicket>(request, (aRequest) => this.getTickets(aRequest));
    }

    getTicketById(id: string, relations?: string[]): Observable<TgTicket | undefined> {
        const filters = [{ field: 'id', operator: TgFilterOperator.Eq, value: id }];
        const request: TgPaginationRequest<TgTicket> = { start: 0, count: 1, filters, relations };

        return this.getTickets(request).pipe(
            take(1),
            map((response) => response.items[0])
        );
    }

    getTicketByTicketNumber(ticketNumber: string): Observable<TgTicket | undefined> {
        return this.http.get<TgTicket | undefined>(`${this.baseUrl}/payment/ticket/ticket-number/${encodeURIComponent(ticketNumber)}`);
    }

    createTicket(amount: number, type: TgTicketType, printerId: string): Observable<TgTicket> {
        const body: TgCreateTicketRequest = { ticket: { amount, type }, printerId };

        return this.http.post<TgTicket>(`${this.baseUrl}/payment/ticket/create`, body, { withCredentials: true });
    }

    redeemTicket(ticketNumber: string): Observable<void> {
        const request: TgRedeemTicketRequest = { ticketNumber };
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        return this.http.put<void>(`${this.baseUrl}/payment/ticket/redeem`, request, { withCredentials: true });
    }

    redeemManualTicket(id: string): Observable<void> {
        const request: TgRedeemManualTicketRequest = { id };
        return this.http.put<undefined>(`${this.baseUrl}/payment/ticket/redeem/manual`, request, { withCredentials: true });
    }

    replaceTicket(id: string, comment: string): Observable<TgTicket> {
        return this.http.put<TgTicket>(`${this.baseUrl}/payment/ticket/replace`, { id, comment }, { withCredentials: true });
    }

    voidTicket(id: string): Observable<void> {
        const request: TgVoidTicketRequest = { id };
        return this.http.put<undefined>(`${this.baseUrl}/payment/ticket/void`, request, { withCredentials: true });
    }

    getTicketReconciliationReport(request: TicketReconciliationReportRequest): Observable<TicketReconciliationReportItem[]> {
        return this.http
            .get<TicketReconciliationReportItemProps[]>(`${this.baseUrl}/payment/ticket/reconciliation-report/`, { params: { ...request } })
            .pipe(map((propsList) => TicketReconciliationReportItem.mapList(propsList)));
    }

    getHandpayOperations(request: TgPaginationRequest<HandpayOperation>): Observable<TgPaginationResponse<HandpayOperation>> {
        return this.http.get<TgPaginationResponse<HandpayOperation>>(`${this.baseUrl}/payment/handpay-operation`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    getHandpays(request: TgPaginationRequest<Handpay>): Observable<TgPaginationResponse<Handpay>> {
        return this.http.get<TgPaginationResponse<Handpay>>(`${this.baseUrl}/payment/handpay`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    getHandpayReconciliationReport(
        request: HandpayReconciliationReportRequest
    ): Observable<Array<{ metrics: MachineHandpayMetrics; machine: MachineProfileListItem; meterMetrics: null }>> {
        return this.http
            .get<MachineHandpayReconciliationReportProps[]>(`${this.baseUrl}/payment/handpay-operation/reconciliation-report`, {
                params: { ...request }
            })
            .pipe(
                map((response) =>
                    response
                        .map((props) => MachineHandpayReconciliationReport.create(props))
                        // This line is temporary until a unified endpoint for handpay and handpay meters is implemented
                        // eslint-disable-next-line spellcheck/spell-checker
                        .map((mhr) => ({ metrics: mhr.metrics, machine: mhr.machine, meterMetrics: null }))
                )
            );
    }

    getHandpayById(id: string, relations?: string[]): Observable<Handpay | undefined> {
        const filters = [{ field: 'id', operator: TgFilterOperator.Eq, value: id }];
        const request: TgPaginationRequest<Handpay> = { start: 0, count: 1, filters, relations };

        return this.getHandpays(request).pipe(
            take(1),
            map((response) => response.items[0])
        );
    }

    actOnHandpay(action: HandpayActionType, id: string): Observable<void> {
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        return this.http.put<void>(`${this.baseUrl}/payment/handpay/${action}`, { id }, { withCredentials: true });
    }

    async resetHandpay(machineId: string): Promise<void> {
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        return firstValueFrom(this.http.post<void>(`${this.baseUrl}/payment/handpay/reset/${machineId}`, null, { withCredentials: true }));
    }

    createHandpay(createHandpayDto: CreateHandpay): Observable<Handpay> {
        return this.http.post<Handpay>(`${this.baseUrl}/payment/handpay/create`, createHandpayDto, { withCredentials: true });
    }

    getMachineFundsTransferOperations(
        request: TgPaginationRequest<MachineFundsTransferOperation>
    ): Observable<TgPaginationResponse<MachineFundsTransferOperation>> {
        return this.http.get<TgPaginationResponse<MachineFundsTransferOperation>>(`${this.baseUrl}/machine-funds-transfers/operations`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    getMachineFundsTransfers(request: TgPaginationRequest<MachineFundsTransfer>): Observable<TgPaginationResponse<MachineFundsTransfer>> {
        return this.http.get<TgPaginationResponse<MachineFundsTransfer>>(`${this.baseUrl}/machine-funds-transfers`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    getMachineFundsTransferById(id: string, relations?: string[]): Observable<MachineFundsTransfer | undefined> {
        const filters = [{ field: 'id', operator: TgFilterOperator.Eq, value: id }];
        const request: TgPaginationRequest<MachineFundsTransfer> = { start: 0, count: 1, filters, relations };

        return this.getMachineFundsTransfers(request).pipe(
            take(1),
            map((response) => response.items[0])
        );
    }

    completeMachineFundsTransfer(data: CompleteMachineFundsTransfer): Observable<void> {
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        return this.http.put<void>(`${this.baseUrl}/machine-funds-transfers/complete`, data, { withCredentials: true });
    }

    cancelMachineFundsTransfer(id: string): Observable<void> {
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        return this.http.put<void>(`${this.baseUrl}/machine-funds-transfers/cancel`, { id }, { withCredentials: true });
    }

    createManualTransferIn(data: CreateManualFundsTransferIn): Observable<MachineFundsTransfer> {
        return this.http.post<MachineFundsTransfer>(`${this.baseUrl}/machine-funds-transfers/create/manual-transfer-in`, data, {
            withCredentials: true
        });
    }

    getAllMachineGameTypes(): Observable<MachineGameType[]> {
        return this.http.get<MachineGameTypeProps[]>(`${this.baseUrl}/machine-game-types`).pipe(
            map((machineGameTypeProps) => {
                return machineGameTypeProps.map((props) => MachineGameType.map(props));
            })
        );
    }

    createMachineProfile(
        location: string,
        games: string | undefined,
        assetNumber: number,
        serialNumber: string | undefined,
        denomination: number,
        manufacturerId: string,
        machineGameTypeId: string,
        rtp?: number | null
    ): Observable<MachineProfile> {
        const body: CreateMachineProfile = {
            location,
            games,
            assetNumber,
            serialNumber,
            denomination,
            manufacturerId,
            rtp,
            machineGameTypeId
        };
        return this.http.post<MachineProfile>(`${this.baseUrl}/machine-profile/create`, body);
    }

    updateMachineProfile(
        id: string,
        location: string,
        games: string | undefined,
        assetNumber: number,
        serialNumber: string | undefined,
        denomination: number,
        manufacturerId: string,
        machineGameTypeId: string,
        rtp?: number | null
    ): Observable<MachineProfile> {
        const body: UpdateMachineProfile = {
            id,
            location,
            games,
            assetNumber,
            serialNumber,
            denomination,
            manufacturerId,
            rtp,
            machineGameTypeId
        };
        return this.http.put<MachineProfile>(`${this.baseUrl}/machine-profile/update`, body);
    }

    getMachineProfileById(id: string): Observable<MachineProfile | undefined> {
        const filters = [{ field: 'id', operator: TgFilterOperator.Eq, value: id }];
        const request: TgPaginationRequest<MachineProfile> = { start: 0, count: 1, filters };

        return this.getMachineProfilesWithManufacturer(request).pipe(
            take(1),
            map((response) => response.items[0])
        );
    }

    getMachineProfilesById(ids: string[]): Observable<MachineProfile[]> {
        const filters = [{ field: 'id', operator: TgFilterOperator.In, value: ids }];
        const request: TgPaginationRequest<MachineProfile> = { start: 0, count: ids.length, filters };

        return this.getMachineProfilesWithManufacturer(request).pipe(
            take(1),
            map((response) => response.items)
        );
    }

    getMachineProfilesWithManufacturer(
        request: TgPaginationRequest<MachineProfile> = { start: 0, count: 500 }
    ): Observable<TgPaginationResponse<MachineProfile>> {
        return this.http.get<TgPaginationResponse<MachineProfile>>(`${this.baseUrl}/machine-profile`, {
            params: BackendService.paginationRequestToHttpParams({ ...request })
        });
    }

    getMachineProfilesListing(): Observable<MachineProfileListItem[]> {
        return this.http
            .get<MachineProfileListItemProps[]>(`${this.baseUrl}/machine-profile/listing`)
            .pipe(map((propsList) => MachineProfileListItem.createList(propsList)));
    }

    getAllMachineProfiles(): Observable<MachineProfile[]> {
        return this.http.get<MachineProfile[]>(`${this.baseUrl}/machine-profile/all`);
    }

    getMachineState(
        request: TgPaginationRequest<MachineCurrent> = { start: 0, count: 100, relations: [] }
    ): Observable<TgPaginationResponse<MachineCurrent>> {
        const relations = Array.from(new Set((request.relations || []).concat(['machine', 'device'])));

        return this.http.get<TgPaginationResponse<MachineCurrent>>(`${this.baseUrl}/machine-current`, {
            params: BackendService.paginationRequestToHttpParams({ ...request, relations })
        });
    }

    getMachineStateById(id: string): Observable<MachineCurrent | undefined> {
        const filters = [{ field: 'id', operator: TgFilterOperator.Eq, value: id }];
        const request: TgPaginationRequest<MachineCurrent> = { start: 0, count: 1, filters };

        return this.getMachineState(request).pipe(
            take(1),
            map((response) => response.items[0])
        );
    }

    updateMachineCurrentOperationalStatus(machineId: string, operationalStatus: OperationalStatus): Observable<void> {
        const body: UpdateMachineOperationalStatus = { operationalStatus };
        return this.http.patch<undefined>(`${this.baseUrl}/machine-current/${machineId}/operational-status`, body);
    }

    updateMachineCurrentLockState(machineId: string, lockState: MachineLockState): Observable<void> {
        const body: UpdateMachineLockState = { lockState };
        return this.http.patch<undefined>(`${this.baseUrl}/machine-current/${machineId}/lock-state`, body);
    }

    getRawCurrentMeters(
        request: TgPaginationRequest<MeterRawCurrent>,
        machines: MachineProfile[] = []
    ): Observable<TgPaginationResponse<MeterRawCurrent>> {
        return BackendService.paginationResponseWithMachines(
            this.http.get<TgPaginationResponse<MeterRawCurrent>>(`${this.baseUrl}/meter/raw/current`, {
                params: BackendService.paginationRequestToHttpParams(request)
            }),
            machines
        );
    }

    getRawSnapshotMeters(
        request: TgPaginationRequest<MeterRawSnapshot>,
        machines: MachineProfile[] = []
    ): Observable<TgPaginationResponse<MeterRawSnapshot>> {
        return BackendService.paginationResponseWithMachines(
            this.http.get<TgPaginationResponse<MeterRawSnapshot>>(`${this.baseUrl}/meter/raw/snapshot`, {
                params: BackendService.paginationRequestToHttpParams(request)
            }),
            machines
        );
    }

    getMetrics(request: TgPaginationRequest<Metric>, machines: MachineProfile[] = []): Observable<TgPaginationResponse<Metric>> {
        return BackendService.paginationResponseWithMachines(
            this.http.get<TgPaginationResponse<Metric>>(`${this.baseUrl}/metric`, {
                params: BackendService.paginationRequestToHttpParams(request)
            }),
            machines
        );
    }

    getHourlyMetrics(
        request: TgPaginationRequest<MetricHourly>,
        machines: MachineProfile[] = []
    ): Observable<TgPaginationResponse<MetricHourly>> {
        return BackendService.paginationResponseWithMachines(
            this.http.get<TgPaginationResponse<MetricHourly>>(`${this.baseUrl}/metric/hourly`, {
                params: BackendService.paginationRequestToHttpParams(request)
            }),
            machines
        );
    }

    getDailyMetrics(
        request: TgPaginationRequest<MetricDaily>,
        machines: MachineProfile[] = []
    ): Observable<TgPaginationResponse<MetricDaily>> {
        return BackendService.paginationResponseWithMachines(
            this.http.get<TgPaginationResponse<MetricDaily>>(`${this.baseUrl}/metric/daily`, {
                params: BackendService.paginationRequestToHttpParams(request)
            }),
            machines
        );
    }

    getClearanceMetrics(
        request: TgPaginationRequest<MetricClearance>,
        machines: MachineProfile[] = []
    ): Observable<TgPaginationResponse<MetricClearance>> {
        return BackendService.paginationResponseWithMachines(
            this.http.get<TgPaginationResponse<MetricClearance>>(`${this.baseUrl}/metric/clearance`, {
                params: BackendService.paginationRequestToHttpParams(request)
            }),
            machines
        );
    }

    getHourlyMetricsTotal(request: MetricHourlyGroupedRequest): Observable<MetricHourlyTotalResponse> {
        return this.http.get<MetricHourlyTotalResponse>(`${this.baseUrl}/metric/hourly/total`, {
            params: {
                gamingDay: request.gamingDay,
                startHourIndex: request.startHourIndex.toString(),
                endHourIndex: request.endHourIndex.toString()
            }
        });
    }

    getHourlyMetricsGroupedByMachine(request: MetricHourlyGroupedRequest): Observable<MetricHourlyGroupedByMachineResponse> {
        return this.http.get<MetricHourlyGroupedByMachineResponse>(`${this.baseUrl}/metric/hourly/grouped-by-machine`, {
            params: {
                gamingDay: request.gamingDay,
                startHourIndex: request.startHourIndex.toString(),
                endHourIndex: request.endHourIndex.toString()
            }
        });
    }

    getDailyMetricsTotal(request: MetricDailyGroupedRequest): Observable<MetricDailyTotalResponse> {
        return this.http.get<MetricDailyTotalResponse>(`${this.baseUrl}/metric/daily/total`, {
            params: {
                startGamingDay: request.startGamingDay,
                endGamingDay: request.endGamingDay
            }
        });
    }

    getDailyMetricsGroupedByMachine(request: MetricDailyGroupedRequest): Observable<MetricDailyGroupedByMachineResponse> {
        return this.http.get<MetricDailyGroupedByMachineResponse>(`${this.baseUrl}/metric/daily/grouped-by-machine`, {
            params: {
                startGamingDay: request.startGamingDay,
                endGamingDay: request.endGamingDay
            }
        });
    }

    getClearanceReport(request: ClearanceReportRequest): Observable<TgListResponse<MetricClearance>> {
        return this.http.get<TgListResponse<MetricClearance>>(`${this.baseUrl}/metric/clearance/all`, {
            params: {
                startGamingDay: request.startGamingDay,
                endGamingDay: request.endGamingDay
            }
        });
    }

    getEvents(request: TgPaginationRequest<TgEvent>): Observable<TgPaginationResponse<TgEvent>> {
        return this.http.get<TgPaginationResponse<TgEvent>>(`${this.baseUrl}/event`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    async getAllEvents(request: TgListRequest<TgEvent>): Promise<TgEvent[]> {
        return this.getAll<TgEvent>(request, (aRequest) => this.getEvents(aRequest));
    }

    getPrinters(request: TgPaginationRequest<TgPrinter> = { start: 0, count: 100 }): Observable<TgPaginationResponse<TgPrinter>> {
        return this.http.get<TgPaginationResponse<TgPrinter>>(`${this.baseUrl}/printer`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    getPrinterById(id: string): Observable<TgPrinter | undefined> {
        return this.getPrinterByField('id', id);
    }

    getPrinterByDeviceId(deviceId: string): Observable<TgPrinter | undefined> {
        return this.getPrinterByField('deviceId', deviceId);
    }

    getPrinterByField(field: string, value: string): Observable<TgPrinter | undefined> {
        const filters = [{ field, operator: TgFilterOperator.Eq, value }];
        const request: TgPaginationRequest<TgPrinter> = { start: 0, count: 1, filters };

        return this.getPrinters(request).pipe(
            take(1),
            map((response) => response.items[0])
        );
    }

    createPrinter(name: string, deviceId: string, assetId?: string): Observable<TgPrinter> {
        const body: TgCreatePrinter = { name, deviceId, assetId };
        return this.http.post<TgPrinter>(`${this.baseUrl}/printer/create`, body);
    }

    updatePrinter(id: string, name?: string, deviceId?: string, assetId?: string): Observable<TgPrinter> {
        const body: TgUpdatePrinter = { id, name, deviceId, assetId };
        return this.http.put<TgPrinter>(`${this.baseUrl}/printer/update`, body);
    }

    deletePrinter(id: string): Observable<boolean> {
        return this.http.delete<{ success: boolean }>(`${this.baseUrl}/printer/delete/${id}`).pipe(map(({ success }) => success));
    }

    getJackpotClients(params: ListJackpotClientInfo = {}): Observable<TgPaginationResponse<JackpotClientInfo>> {
        return this.http.get<TgPaginationResponse<JackpotClientInfo>>(`${this.baseUrl}/jackpot/client`, {
            params: params as Record<string, string>
        });
    }

    async getAllJackpotClients(): Promise<JackpotClientInfo[]> {
        const response = await firstValueFrom(this.getJackpotClients());
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        return response.items || [];
    }

    async getJackpotClientById(id: string): Promise<JackpotClientInfo | undefined> {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const response = await firstValueFrom(this.getJackpotClients({ 'client-ids': id, 'only-connected': 'false' }));

        return response.items[0];
    }

    async createJackpotClient(clientId: string, clientGroupIds: string[], levelIds?: string[]): Promise<void> {
        await this.saveJackpotClientClientGroupIds(clientId, clientGroupIds);

        if (levelIds?.length) {
            await this.addLevelsToJackpotClient(clientId, levelIds);
        }
    }

    async updateJackpotClient(
        clientId: string,
        clientGroupIds: string[],
        levelIdsToRemove?: string[],
        levelIdsToAdd?: string[]
    ): Promise<void> {
        await this.saveJackpotClientClientGroupIds(clientId, clientGroupIds);

        if (levelIdsToRemove?.length) {
            await this.removeLevelsFromJackpotClient(clientId, levelIdsToRemove);
        }

        if (levelIdsToAdd?.length) {
            await this.addLevelsToJackpotClient(clientId, levelIdsToAdd);
        }
    }

    async saveJackpotClientClientGroupIds(clientId: string, clientGroupIds: string[]): Promise<void> {
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        return firstValueFrom(this.http.put<void>(`${this.baseUrl}/jackpot/client/save-client-group-ids`, { clientId, clientGroupIds }));
    }

    async addLevelsToJackpotClient(clientId: string, levelIds: string[]): Promise<void> {
        return firstValueFrom(
            // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
            this.http.post<void>(`${this.baseUrl}/jackpot/client/add-client-enabled-levels`, { clientId, levelIds })
        );
    }

    async removeLevelsFromJackpotClient(clientId: string, levelIds: string[]): Promise<void> {
        return firstValueFrom(
            // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
            this.http.post<void>(`${this.baseUrl}/jackpot/client/remove-client-enabled-levels`, { clientId, levelIds })
        );
    }

    getJackpotLevels(params: ListJackpotLevels = {}): Observable<TgPaginationResponse<JackpotLevel>> {
        return this.http.get<TgPaginationResponse<JackpotLevel>>(`${this.baseUrl}/jackpot/level`, {
            params: params as Record<string, string>
        });
    }

    async getAllJackpotLevels(): Promise<JackpotLevel[]> {
        // eslint-disable-next-line @typescript-eslint/naming-convention
        const response = await firstValueFrom(this.getJackpotLevels({ 'include-disabled': 'true' }));
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        return response.items || [];
    }

    async getJackpotClientLevels(clientGroups: string[], clientId?: string): Promise<JackpotLevel[]> {
        const response = await firstValueFrom(
            // eslint-disable-next-line @typescript-eslint/naming-convention
            this.getJackpotLevels({ 'include-disabled': 'true', 'client-id': clientId, 'client-group-ids': clientGroups.join(',') })
        );
        return response.items;
    }

    async getJackpotLevelById(id: string): Promise<JackpotLevel | undefined> {
        const response = await firstValueFrom(
            this.http.get<TgListResponse<JackpotLevel>>(`${this.baseUrl}/jackpot/level`, {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                params: { 'include-disabled': true, 'level-ids': [id] }
            })
        );

        return response.items[0];
    }

    // eslint-disable-next-line @typescript-eslint/no-magic-numbers
    async getJackpotLevelHistory(id: string, onlyTop = false, count = 10): Promise<JackpotLevelHistoryData[]> {
        const response = await firstValueFrom(
            this.http.get<TgListResponse<JackpotLevelHistory>>(`${this.baseUrl}/jackpot/level/history`, {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                params: { 'level-ids': id, 'include-disabled': true, limit: count.toString(), 'only-top': onlyTop.toString() }
            })
        );

        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        return response.items[0]?.history || [];
    }

    async createJackpotLevel(dto: CreateJackpotLevelRequest): Promise<SaveJackpotLevelResponse> {
        return firstValueFrom(this.http.post<SaveJackpotLevelResponse>(`${this.baseUrl}/jackpot/level/create`, dto));
    }

    async updateJackpotLevel(dto: UpdateJackpotLevelRequest): Promise<SaveJackpotLevelResponse> {
        return firstValueFrom(this.http.put<SaveJackpotLevelResponse>(`${this.baseUrl}/jackpot/level/update`, dto));
    }

    async deleteJackpotLevel(id: string): Promise<void> {
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        return firstValueFrom(this.http.delete<void>(`${this.baseUrl}/jackpot/level/delete/${id}`));
    }

    async setJackpotLevelValue(dto: SetJackpotLevelValueRequest): Promise<SetJackpotLevelValueResponse> {
        return firstValueFrom(this.http.post<SetJackpotLevelValueResponse>(`${this.baseUrl}/jackpot/level/set-value`, dto));
    }

    async setJackpotLevelEnabled(levelId: string, enabled: boolean): Promise<void> {
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        return firstValueFrom(this.http.put<void>(`${this.baseUrl}/jackpot/level/toggle`, { levelId, enabled }));
    }

    async resetJackpotLevel(levelId: string): Promise<void> {
        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        return firstValueFrom(this.http.put<void>(`${this.baseUrl}/jackpot/level/reset/${levelId}`, null));
    }

    getManufacturerById(id: string): Observable<Manufacturer | undefined> {
        const filters = [{ field: 'id', operator: TgFilterOperator.Eq, value: id }];
        const request: TgPaginationRequest<Manufacturer> = { start: 0, count: 1, filters };

        return this.getManufacturers(request).pipe(
            take(1),
            map((response) => response.items[0])
        );
    }

    getManufacturers(
        request: TgPaginationRequest<Manufacturer> = { start: 0, count: 100 }
    ): Observable<TgPaginationResponse<Manufacturer>> {
        return this.http.get<TgPaginationResponse<Manufacturer>>(`${this.baseUrl}/manufacturer`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    createManufacturer(createManufacturer: CreateManufacturer): Observable<Manufacturer> {
        return this.http.post<Manufacturer>(`${this.baseUrl}/manufacturer/create`, createManufacturer);
    }

    findManufacturerByName(name: string): Observable<Manufacturer | null> {
        return this.http.get<Manufacturer | null>(`${this.baseUrl}/manufacturer/name/${encodeURIComponent(name)}`);
    }

    updateManufacturer(updateManufacturer: UpdateManufacturer): Observable<Manufacturer> {
        return this.http.put<Manufacturer>(`${this.baseUrl}/manufacturer/update`, updateManufacturer);
    }

    deleteManufacturer(id: string): Observable<boolean> {
        return this.http.delete<{ success: boolean }>(`${this.baseUrl}/manufacturer/delete/${id}`).pipe(map(({ success }) => success));
    }

    getTransactionPoints(
        request: TgPaginationRequest<TransactionPoint> = { start: 0, count: 100 }
    ): Observable<TgPaginationResponse<TransactionPoint>> {
        return this.http.get<TgPaginationResponse<TransactionPoint>>(`${this.baseUrl}/transaction-point`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    getTransactionPointById(id: string): Observable<TransactionPoint | undefined> {
        const filters = [{ field: 'id', operator: TgFilterOperator.Eq, value: id }];
        const request: TgPaginationRequest<TransactionPoint> = { start: 0, count: 1, filters };

        return this.getTransactionPoints(request).pipe(
            take(1),
            map((response) => response.items[0])
        );
    }

    createTransactionPoint(createTransactionPoint: CreateTransactionPoint): Observable<TransactionPoint> {
        return this.http.post<TransactionPoint>(`${this.baseUrl}/transaction-point/create`, createTransactionPoint);
    }

    updateTransactionPoint(updateTransactionPoint: UpdateTransactionPoint): Observable<TransactionPoint> {
        return this.http.put<TransactionPoint>(`${this.baseUrl}/transaction-point/update`, updateTransactionPoint);
    }

    getTransactionPointsById(ids: string[]): Observable<TransactionPoint[]> {
        const filters = [{ field: 'id', operator: TgFilterOperator.In, value: ids }];
        const request: TgPaginationRequest<TransactionPoint> = { start: 0, count: ids.length, filters };

        return this.getTransactionPoints(request).pipe(
            take(1),
            map((response) => response.items)
        );
    }

    getDevices(request: TgPaginationRequest<Device> = { start: 0, count: 100 }): Observable<TgPaginationResponse<Device>> {
        return this.http.get<TgPaginationResponse<Device>>(`${this.baseUrl}/device`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    createDevice(request: CreateDevice): Observable<Device> {
        return this.http.post<Device>(`${this.baseUrl}/device/create`, request);
    }

    getDeviceById(id: string): Observable<Device | undefined> {
        return this.http.get<Device | undefined>(`${this.baseUrl}/device/${id}`);
    }

    updateDevice(request: UpdateDevice): Observable<void> {
        return this.http.put<undefined>(`${this.baseUrl}/device/update`, request);
    }

    getAvailableVersions(deviceType: DeviceType): Observable<string[]> {
        let deviceFirmwareType: DeviceFirmwareType;
        switch (deviceType) {
            case DeviceType.Med:
                deviceFirmwareType = DeviceFirmwareType.Med;
                break;
            case DeviceType.TicketPrinter:
                deviceFirmwareType = DeviceFirmwareType.TicketPrinter;
                break;
        }

        return this.http.get<{ versions: string[] }>(`${this.baseUrl}/device/get-available-versions/${deviceFirmwareType}`).pipe(
            map((response) => {
                return response.versions;
            })
        );
    }

    macAddressTaken(macAddress: string): Observable<boolean> {
        return this.http.get<boolean>(`${this.baseUrl}/device/mac-address-taken/${macAddress}`);
    }

    rebootDevice(id: string): Observable<void> {
        return this.http.put<undefined>(`${this.baseUrl}/device/reboot/${id}`, null);
    }

    deleteDevice(id: string): Observable<{ success: boolean }> {
        return this.http.delete<{ success: boolean }>(`${this.baseUrl}/device/delete/${id}`);
    }

    getPendingDevices(
        request: TgPaginationRequest<PendingDevice> = { start: 0, count: 100 }
    ): Observable<TgPaginationResponse<PendingDevice>> {
        return this.http.get<TgPaginationResponse<PendingDevice>>(`${this.baseUrl}/device/pending`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    getPendingDeviceById(id: string): Observable<PendingDevice | null> {
        const filters = [{ field: 'id', operator: TgFilterOperator.Eq, value: id }];
        const request: TgPaginationRequest<PendingDevice> = { start: 0, count: 1, filters };

        return this.getPendingDevices(request).pipe(
            take(1),
            map((response) => response.items[0] || null)
        );
    }

    getCmsUsers(): Observable<CmsUser[]> {
        return this.http
            .get<CmsUser[]>(`${this.baseUrl}/cms-user`)
            .pipe(map((cmsUsersJsonArray) => cmsUsersJsonArray.map((cmsUserJson) => deserialize(CmsUser, cmsUserJson))));
    }

    getCmsUserByUsername(username: string): Observable<CmsUser> {
        return this.http.get<CmsUser>(`${this.baseUrl}/cms-user/${username}`).pipe(map((cmsUserJson) => deserialize(CmsUser, cmsUserJson)));
    }

    createCmsUser(user: CreateCmsUser): Observable<CmsUser> {
        return this.http.post<CmsUser>(`${this.baseUrl}/cms-user`, user).pipe(map((cmsUserJson) => deserialize(CmsUser, cmsUserJson)));
    }

    updateCmsUser(username: string, updateCmsUserDto: UpdateCmsUser): Observable<null> {
        return this.http.post<null>(`${this.baseUrl}/cms-user/update/${username}`, updateCmsUserDto);
    }

    isUsernameTaken(username: string): Observable<boolean> {
        return this.http.get<boolean>(`${this.baseUrl}/cms-user/username-taken/${username}`);
    }

    resetCmsUserPassword(username: string, resetPassword: ResetCmsUserPassword): Observable<undefined> {
        return this.http.post<undefined>(`${this.baseUrl}/cms-user/reset-password/${encodeURIComponent(username)}`, resetPassword);
    }

    toggleCmsUserEnabledFlag(username: string, enabled: boolean): Observable<CmsUser> {
        const toggleRoute = enabled ? 'enabled' : 'disabled';
        return this.http
            .post<CmsUser>(`${this.baseUrl}/cms-user/toggle-${toggleRoute}/${encodeURIComponent(username)}`, null)
            .pipe(map((cmsUserJson) => deserialize(CmsUser, cmsUserJson)));
    }

    getDenominations(
        request: TgPaginationRequest<Denomination> = { start: 0, count: 100 }
    ): Observable<TgPaginationResponse<Denomination>> {
        return this.http.get<TgPaginationResponse<Denomination>>(`${this.baseUrl}/finance/denomination`, {
            params: BackendService.paginationRequestToHttpParams(request)
        });
    }

    getDenominationById(id: string): Observable<Denomination | undefined> {
        const filters = [{ field: 'id', operator: TgFilterOperator.Eq, value: id }];
        const request: TgPaginationRequest<Denomination> = { start: 0, count: 1, filters };

        return this.getDenominations(request).pipe(
            take(1),
            map((response) => response.items[0])
        );
    }

    createDenomination(createTransactionPoint: CreateDenomination): Observable<Denomination> {
        return this.http.post<Denomination>(`${this.baseUrl}/finance/denomination/create`, createTransactionPoint);
    }

    updateDenomination(updateTransactionPoint: UpdateDenomination): Observable<Denomination> {
        return this.http.put<Denomination>(`${this.baseUrl}/finance/denomination/update`, updateTransactionPoint);
    }

    getDeviceFirmwareList(): Observable<DeviceFirmwareListingItem[]> {
        return this.http.get<ListDeviceFirmwareRequestProps>(`${this.baseUrl}/device-firmware`).pipe(
            map((firmwareListResponseProps) => deserialize(ListDeviceFirmwareResponse, firmwareListResponseProps)),
            map((firmwareListResponse) => firmwareListResponse.items)
        );
    }

    async uploadDeviceFirmware(firmwareFile: File): Promise<string> {
        const { presignedUrl, uploadId } = await lastValueFrom(
            this.http
                .post<PrepareDeviceFirmwareUploadResponseProps>(`${this.baseUrl}/device-firmware/prepare-upload`, {})
                .pipe(map((prepareUploadResponseProps) => PrepareDeviceFirmwareUploadResponse.map(prepareUploadResponseProps)))
        );

        const uploadFirmwareResponse = await fetch(presignedUrl, {
            method: 'PUT',
            body: firmwareFile,
            headers: {
                // eslint-disable-next-line @typescript-eslint/naming-convention
                'Content-Type': ContentTypeEnum.ApplicationOctetStream
            }
        });

        if (!uploadFirmwareResponse.ok) {
            throw new InternalServerError({ message: 'Failed to upload firmware' });
        }

        return uploadId;
    }

    publishDeviceFirmware(publishDeviceFirmware: PublishDeviceFirmwareRequestProps): Observable<null> {
        return this.http.post<null>(`${this.baseUrl}/device-firmware/publish`, publishDeviceFirmware);
    }

    private static paginationRequestToHttpParams<T>(request: TgPaginationRequest<T>): HttpParams {
        let params = new HttpParams().set('start', request.start.toString()).set('count', request.count.toString());

        request.filters?.forEach((value) => (params = params.append('filters', JSON.stringify(value))));
        request.sorts?.forEach((value) => (params = params.append('sorts', JSON.stringify(value))));
        request.relations?.forEach((value) => (params = params.append('relations', value)));

        if (request.query) {
            params = params.append('query', JSON.stringify(request.query));
        }

        return params;
    }

    private static determineTicketExpiration(ticketType: TgTicketType): number {
        const validityDays =
            ticketType === TgTicketType.Cash ? BackendService.cashTicketValidityDays : BackendService.promoTicketValidityDays;

        const gamingDayOffsetMinutes = BackendService.getGamingDayOffsetInMinutes(BackendService.gamingDayOffset);
        const startOfGamingDay = addMinutes(startOfDay(new Date()), gamingDayOffsetMinutes).getTime();
        return addDays(startOfGamingDay, validityDays + (Date.now() > startOfGamingDay ? 1 : 0)).getTime();
    }

    /**
     * @param offset Offset in format HH:mm
     */
    private static getGamingDayOffsetInMinutes(offset: string): number {
        const [hours, minutes] = offset.split(':');

        const minutesInHour = 60;
        return Number(hours) * minutesInHour + Number(minutes);
    }

    private static listWithMachine<T extends { machineId: string; machine?: MachineProfile }>(
        list: T[],
        machinesMap?: Map<string, MachineProfile>
    ): T[] {
        return list.map((item) => ({ ...item, machine: machinesMap?.get(item.machineId) }));
    }

    private static paginationResponseWithMachines<T extends { machineId: string; machine?: MachineProfile }>(
        observable: Observable<TgPaginationResponse<T>>,
        machines: MachineProfile[] = []
    ): Observable<TgPaginationResponse<T>> {
        return observable.pipe(
            map((response) => {
                return { ...response, items: BackendService.listWithMachine(response.items, TgUtils.toMap(machines)) };
            })
        );
    }
}
