import { MessageBroker } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import {
    ConflictError,
    ConnectionStatus,
    CurrencyAmountUtils,
    NotFoundError,
    TgErrorType,
    TgPaginationRequest,
    TgPaginationResponse
} from '@tronius/shared-common';
import {
    AssignMachineToMedDevice,
    FailedToAcquireMetersEvent,
    GetMachineByAssetNumberQuery,
    GetMachineByAssetNumberQueryResult,
    MachineConnectionStatus,
    MachineCurrent,
    MachineCurrentSession,
    MachineLockState,
    MachineLockStateChangedEvent,
    MachineLockStateStatus,
    MachineOperationalStatusChangedEvent,
    MachineRebootMedCommand,
    MachineRestartMedAppCommand,
    MeterAcquisitionStatus,
    MeterSnapshotContext,
    OperationalStatus,
    SessionState,
    SetMachinesCurrentOperationalStatus,
    SetMachinesLastBet,
    UpdateMachineLockStateStatus,
    UpdateMachineStatus
} from '@tronius/shared-domain';
import { EntityManager } from 'typeorm';

import { MachineCurrentDao } from './dao/machine-current.dao';

@Injectable()
export class MachineCurrentService {
    private readonly logger = new Logger(MachineCurrentService.name);

    constructor(
        private readonly dao: MachineCurrentDao,
        private readonly messageBroker: MessageBroker
    ) {}

    async setSession(machineId: string, session: MachineCurrentSession): Promise<void> {
        await this.dao.updateMachineSession({ machineId, session, sessionState: SessionState.Active });
    }

    async clearSession(machineId: string): Promise<void> {
        await this.dao.updateMachineSession({ machineId, session: null, sessionState: SessionState.Inactive });
    }

    async updateMeterAcquisitionStatus(
        machineId: string,
        status: MeterAcquisitionStatus,
        context: MeterSnapshotContext.Daily | MeterSnapshotContext.Hourly | MeterSnapshotContext.Live
    ): Promise<void> {
        let statusUpdated = false;
        switch (context) {
            case MeterSnapshotContext.Live:
                statusUpdated = await this.dao.updateLiveMeterAcquisitionStatus(machineId, status);
                break;
            case MeterSnapshotContext.Hourly:
                statusUpdated = await this.dao.updateHourlyMeterAcquisitionStatus(machineId, status);
                break;
            case MeterSnapshotContext.Daily:
                statusUpdated = await this.dao.updateDailyMeterAcquisitionStatus(machineId, status);
                break;
            default:
                return;
        }
        if (statusUpdated) {
            this.logger.debug(`Updated ${context} meter acquisition status for [machineId]: "${machineId}"`);
        }

        const isDailyOrHourlyContext = context === MeterSnapshotContext.Daily || context === MeterSnapshotContext.Hourly;
        if (!isDailyOrHourlyContext) {
            return;
        }

        if (status === MeterAcquisitionStatus.Failed) {
            await this.messageBroker.publish(FailedToAcquireMetersEvent, FailedToAcquireMetersEvent.create({ machineId, context }));
        }
    }

    async getMachineCurrentByIdOrThrow(machineId: string): Promise<MachineCurrent> {
        const machineCurrent = await this.getMachineCurrent(machineId);
        if (!machineCurrent) {
            throw new NotFoundError({
                message: `Machine with id ${machineId} not found`,
                params: { machineId },
                errorType: TgErrorType.MachineNotFound
            });
        }
        return machineCurrent;
    }

    async updateMachineStatus({
        cashableValue,
        machineId,
        promoRestrictedValue,
        promoUnrestrictedValue,
        status
    }: UpdateMachineStatus): Promise<void> {
        const machineCurrent = await this.getMachineCurrentByIdOrThrow(machineId);

        const cashableAmount = CurrencyAmountUtils.fromCentsValue(cashableValue);
        const promoRestrictedAmount = CurrencyAmountUtils.fromCentsValue(promoRestrictedValue);
        const promoUnrestrictedAmount = CurrencyAmountUtils.fromCentsValue(promoUnrestrictedValue);

        const updateRequired =
            machineCurrent.status !== status ||
            machineCurrent.cashableAmount !== cashableAmount ||
            machineCurrent.promoRestrictedAmount !== promoRestrictedAmount ||
            machineCurrent.promoUnrestrictedAmount !== promoUnrestrictedAmount;

        if (!updateRequired) {
            return;
        }

        await this.dao.updateMachineStatus(machineId, status, cashableAmount, promoRestrictedAmount, promoUnrestrictedAmount);
        this.logger.debug(`Updated machine status for [machineId]: "${machineId}"`);
    }

    async markAllConnectedMachinesAsDisconnected(): Promise<void> {
        await this.dao.markAllConnectedMachinesAsDisconnected();
        this.logger.debug('Marked all connected machines as disconnected');
    }

    async updateLastBet({ denomination, lastBetAt, lastBetValue, machineId }: SetMachinesLastBet): Promise<void> {
        const { lastBetAt: currentLastBetAt } = await this.getMachineCurrentByIdOrThrow(machineId);

        if (currentLastBetAt && currentLastBetAt > lastBetAt) {
            return;
        }

        const lastBetAmount = CurrencyAmountUtils.fromCreditValue(lastBetValue, denomination);
        await this.dao.updateLastBet(machineId, lastBetAmount, lastBetAt);
        this.logger.debug(`Updated last bet for [machineId]: "${machineId}"`);
    }

    async updateMachineLockStateStatus({ lockState, lockStateStatus, machineId }: UpdateMachineLockStateStatus): Promise<void> {
        const machineCurrent = await this.getMachineCurrentByIdOrThrow(machineId);

        const updateRequired = machineCurrent.lockState !== lockState || machineCurrent.lockStateStatus !== lockStateStatus;
        if (!updateRequired) {
            return;
        }

        await this.dao.updateLockState(machineId, lockState, lockStateStatus);
        this.logger.debug(`Updated lock state for [machineId]: "${machineId}"`);
    }

    async getMachineCurrentByDeviceId(deviceId: string): Promise<MachineCurrent | null> {
        const machineByDeviceId = await this.dao.findByDeviceId(deviceId);
        return machineByDeviceId;
    }

    async setMachinesCurrentOperationalStatus({ machineId, operationalStatus }: SetMachinesCurrentOperationalStatus): Promise<void> {
        const machine = await this.getMachineCurrentByIdOrThrow(machineId);
        if (machine.currentOperationalStatus === operationalStatus) {
            return;
        }

        await this.dao.updateOperationalStatus(machineId, operationalStatus);
        this.logger.debug(`Updated operational status for [machineId]: "${machineId}"`);
    }

    async assignMachineToMedDevice({ assetNumber, medDeviceId, oldAssignedMachineId }: AssignMachineToMedDevice): Promise<void> {
        const { machine: newMachineByAssetNumber } = await this.messageBroker.query(
            GetMachineByAssetNumberQuery.create({ assetNumber }),
            GetMachineByAssetNumberQueryResult
        );

        // TODO - check if device is a med device

        const newMachineConnectionStatus = await this.getMachineCurrentByIdOrThrow(newMachineByAssetNumber.id);
        const newMachineAlreadyConnected = newMachineConnectionStatus.connectionStatus === ConnectionStatus.Connected;
        const machineAlreadyAssignedToMed = newMachineConnectionStatus.deviceId === medDeviceId;
        const oldAssignedMachineIsSameAsNewMachine = oldAssignedMachineId === newMachineByAssetNumber.id;

        if (machineAlreadyAssignedToMed && newMachineAlreadyConnected && oldAssignedMachineIsSameAsNewMachine) {
            return;
        }

        if (newMachineAlreadyConnected) {
            throw new ConflictError({
                message: `Machine with asset number ${assetNumber} is already market as connected`,
                params: { assetNumber, machineId: newMachineByAssetNumber.id },
                errorType: TgErrorType.MachineConnected
            });
        }

        if (oldAssignedMachineId) {
            await this.dao.clearDeviceId(oldAssignedMachineId);
        }

        await this.dao.assignDeviceId(newMachineByAssetNumber.id, medDeviceId);
        this.logger.debug(`Assigned machine [machineId]: "${newMachineByAssetNumber.id}" to med device [medDeviceId]: "${medDeviceId}"`);
    }

    async getConnectionStatusOfAllMachines(): Promise<MachineConnectionStatus[]> {
        const machines = await this.dao.getConnectionStatusOfAllMachines();
        return machines;
    }

    async list(request: TgPaginationRequest<MachineCurrent>): Promise<TgPaginationResponse<MachineCurrent>> {
        return this.dao.list(request.start, request.count, request.filters, request.sorts, request.relations, request.query);
    }

    async setMachineConnectionStatus(machineId: string, connectionStatus: ConnectionStatus): Promise<void> {
        const connectionStatusUpdated = await this.dao.updateConnectionStatus(machineId, connectionStatus);
        if (!connectionStatusUpdated) {
            return;
        }
        this.logger.debug(`Updated connection status to ${connectionStatus} for [machineId]: "${machineId}"`);
    }

    async createEmpty(id: string, manager?: EntityManager): Promise<MachineCurrent> {
        return this.dao.save({ id }, manager);
    }

    async getMachineCurrent(id: string): Promise<MachineCurrent | null> {
        return this.dao.findMachineWithProfileAndManufacturer(id);
    }

    async updateOperationalStatus(machineId: string, intendedOperationalStatus: OperationalStatus, userId: string): Promise<void> {
        const machineCurrent = await this.getMachineCurrentByIdOrThrow(machineId);
        if (machineCurrent.intendedOperationalStatus === intendedOperationalStatus) {
            return;
        }

        const isDecommissioned = [OperationalStatus.OffFloor, OperationalStatus.Decommissioned].includes(intendedOperationalStatus);
        const isFirstCommission = intendedOperationalStatus === OperationalStatus.Live && !machineCurrent.commissionedDate;
        const requiresLockStateChange = isDecommissioned;

        if (requiresLockStateChange) {
            await this.updateLockState(machineId, MachineLockState.Locked, userId);
        }

        await this.dao.repository.update(machineId, {
            intendedOperationalStatus,
            decommissionedDate: isDecommissioned ? Date.now() : null,
            commissionedDate: isFirstCommission ? Date.now() : undefined
        });

        const updatedMachine = await this.getMachineCurrentByIdOrThrow(machineId);
        this.logger.debug(`Updated operational status for [machineId]: "${machineId}"`);

        await this.messageBroker.publish(
            MachineOperationalStatusChangedEvent,
            MachineOperationalStatusChangedEvent.create({
                triggeredBy: userId,
                operationalStatus: intendedOperationalStatus,
                machineId,
                machineCurrent: updatedMachine
            })
        );
    }

    async updateLockState(machineId: string, lockState: MachineLockState, userId: string): Promise<void> {
        const machineCurrent = await this.getMachineCurrentByIdOrThrow(machineId);
        const currentUpdateLockStateAlreadyRequested =
            machineCurrent.lockState === lockState && machineCurrent.lockStateStatus === MachineLockStateStatus.Requested;
        if (currentUpdateLockStateAlreadyRequested) {
            return;
        }

        await this.dao.updateLockState(machineId, lockState, MachineLockStateStatus.Requested);
        this.logger.debug(`Updated lock state for [machineId]: "${machineId}"`);
        const updatedMachine = await this.getMachineCurrentByIdOrThrow(machineId);

        await this.messageBroker.publish(
            MachineLockStateChangedEvent,
            MachineLockStateChangedEvent.create({
                triggeredBy: userId,
                lockState,
                machineId,
                machineCurrent: updatedMachine
            })
        );
    }

    async updateLastMeterAcquisition(
        machineId: string,
        context: MeterSnapshotContext.Daily | MeterSnapshotContext.Hourly | MeterSnapshotContext.Live,
        metersAcquiredAt: Date
    ): Promise<void> {
        switch (context) {
            case MeterSnapshotContext.Live:
                await this.dao.updateLastLiveMeterAcquisition(machineId, metersAcquiredAt);
                break;
            case MeterSnapshotContext.Hourly:
                await this.dao.updateLastHourlyMeterAcquisition(machineId, metersAcquiredAt);
                break;
            case MeterSnapshotContext.Daily:
                await this.dao.updateLastDailyMeterAcquisition(machineId, metersAcquiredAt);
                break;
            default:
                return;
        }

        this.logger.debug(`Updated last ${context} meter acquisition for [machineId]: "${machineId}"`);
    }

    async rebootMed(machineId: string): Promise<void> {
        await this.messageBroker.command(MachineRebootMedCommand.create({ machineId }));
        this.logger.debug(`Sent reboot MED command for [machineId]: "${machineId}"`);
    }

    async restartMedApp(machineId: string): Promise<void> {
        await this.messageBroker.command(MachineRestartMedAppCommand.create({ machineId }));
        this.logger.debug(`Sent restart MED app command for [machineId]: "${machineId}"`);
    }
}
