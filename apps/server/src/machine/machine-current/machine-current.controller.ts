import { ListEntityAuditQuery, ListEntityAuditQueryResult, ListEntityAuditResponse } from '@cms/entity-audit';
import { MessageBroker } from '@cms/message-broker';
import { CmsIdentity, Identity } from '@cms/server-authentication';
import { CmsPermissions } from '@cms/server-permissions';
import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { ApiOAuth2, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { TgPaginationRequestDto } from '@tronius/backend-common';
import { TgPaginationResponse } from '@tronius/shared-common';
import { CmsAction, MachineCurrent } from '@tronius/shared-domain';

import { UpdateLockStateDto } from './dto/update-lock-state.dto';
import { UpdateOperationalStatusDto } from './dto/update-operational-status.dto';
import { MachineCurrentService } from './machine-current.service';

@ApiTags('Machine Current')
@ApiOAuth2([])
@Controller('machine-current')
export class MachineCurrentController {
    constructor(
        private readonly service: MachineCurrentService,
        private readonly messageBroker: MessageBroker
    ) {}

    @Get()
    @CmsPermissions(CmsAction.MachineList)
    async list(@Query() query: TgPaginationRequestDto<MachineCurrent>): Promise<TgPaginationResponse<MachineCurrent>> {
        return this.service.list(query);
    }

    @Get('/audit')
    @ApiQuery({ name: 'next', type: String, required: false })
    @ApiQuery({ name: 'count', type: Number, required: false })
    @ApiQuery({ name: 'entityId', type: String, required: false })
    @ApiQuery({ name: 'actor', type: String, required: false })
    @ApiOperation({ summary: 'List machine current audit' })
    @CmsPermissions(CmsAction.MachineList)
    async listAudit(
        @Query('next') next?: string,
        @Query('count') count?: string,
        @Query('entityId') entityId?: string,
        @Query('actor') actor?: string
    ): Promise<ListEntityAuditResponse<MachineCurrent>> {
        const { result: response } = await this.messageBroker.query(
            ListEntityAuditQuery.create({
                request: {
                    entityName: 'MachineCurrent',
                    next,
                    count: count as unknown as number,
                    entityId,
                    actor
                }
            }),
            ListEntityAuditQueryResult<MachineCurrent>
        );

        return response;
    }

    @Patch('/:id/operational-status')
    @CmsPermissions(CmsAction.MachineUpdateOperationalStatus)
    async updateOperationalStatus(
        @Param('id') id: string,
        @Body() body: UpdateOperationalStatusDto,
        @Identity() { username }: CmsIdentity
    ): Promise<void> {
        return this.service.updateOperationalStatus(id, body.operationalStatus, username);
    }

    @Patch('/:id/lock-state')
    @CmsPermissions(CmsAction.MachineUpdateLockState)
    async updateMachineLockState(
        @Param('id') id: string,
        @Body() body: UpdateLockStateDto,
        @Identity() { username }: CmsIdentity
    ): Promise<void> {
        return this.service.updateLockState(id, body.lockState, username);
    }

    @Post('/:id/reboot-med')
    @CmsPermissions(CmsAction.MachineRebootMed)
    async rebootMed(@Param('id') id: string): Promise<void> {
        return this.service.rebootMed(id);
    }

    @Post('/:id/restart-med-app')
    @CmsPermissions(CmsAction.MachineRestartMedApp)
    async restartMedApp(@Param('id') id: string): Promise<void> {
        return this.service.restartMedApp(id);
    }
}
