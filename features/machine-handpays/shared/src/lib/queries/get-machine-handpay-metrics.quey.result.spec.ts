import { UUIDFactory } from '@tronius/shared-common';

import { HandpayType, MachineHandpayMetrics, MachineHandpayMetricsForType } from '../models';
import { GetMachineHandpayMetricsQueryResult, GetMachineHandpayMetricsQueryResultProps } from './get-machine-handpay-metrics.query.result';

describe('GetMachineHandpayMetricsQueryResult', () => {
    const correlationId = UUIDFactory.create();

    it.each([
        {
            metricsByType: [
                MachineHandpayMetricsForType.create({
                    type: HandpayType.Jackpot,
                    metrics: MachineHandpayMetrics.createInitial()
                })
            ],
            totalMetrics: MachineHandpayMetrics.createInitial(),
            adjustmentsByType: [
                MachineHandpayMetricsForType.create({
                    type: HandpayType.Jackpot,
                    metrics: MachineHandpayMetrics.createInitial()
                })
            ],
            totalAdjustments: MachineHandpayMetrics.createInitial(),
            correlationId
        },
        {
            metricsByType: [
                MachineHandpayMetricsForType.create({
                    type: HandpayType.Bonus,
                    metrics: MachineHandpayMetrics.createInitial()
                })
            ],
            totalMetrics: MachineHandpayMetrics.createInitial(),
            adjustmentsByType: [
                MachineHandpayMetricsForType.create({
                    type: HandpayType.Bonus,
                    metrics: MachineHandpayMetrics.createInitial()
                })
            ],
            totalAdjustments: MachineHandpayMetrics.createInitial(),
            correlationId
        }
    ])('should deserialize', (props) => {
        const result = GetMachineHandpayMetricsQueryResult.create(props as unknown as GetMachineHandpayMetricsQueryResultProps);
        expect(result).toBeInstanceOf(GetMachineHandpayMetricsQueryResult);
        expect(result.metricsByType).toBeInstanceOf(Array);
        expect(result.metricsByType[0]).toBeInstanceOf(MachineHandpayMetricsForType);
        expect(result.totalMetrics).toBeInstanceOf(MachineHandpayMetrics);
    });

    it.each([
        { metricsByType: null, adjustmentsByType: null, correlationId: UUIDFactory.create() },
        { metricsByType: [{ machine: null }], correlationId: UUIDFactory.create() },
        { totalMetrics: null, correlationId: UUIDFactory.create() },
        { totalMetrics: { machine: null }, correlationId: UUIDFactory.create() }
    ])('should not deserialize', (props) => {
        // eslint-disable-next-line no-console
        console.log(props);
        expect(() => GetMachineHandpayMetricsQueryResult.create(props as unknown as GetMachineHandpayMetricsQueryResultProps)).toThrow();
    });
});
