import { deserialize } from '@cms/serialization';
import { QueryResult, QueryResultProps } from '@tronius/shared-common';
import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';

import { MachineHandpayMetrics, MachineHandpayMetricsForType } from '../models';

export interface GetMachineHandpayMetricsQueryResultProps extends QueryResultProps {
    metricsByType: MachineHandpayMetricsForType[];
    totalMetrics: MachineHandpayMetrics;
    adjustmentsByType: MachineHandpayMetricsForType[];
    totalAdjustments: MachineHandpayMetrics;
}

export abstract class GetMachineHandpayMetricsQueryResult extends QueryResult implements GetMachineHandpayMetricsQueryResultProps {
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => MachineHandpayMetricsForType)
    readonly metricsByType!: MachineHandpayMetricsForType[];

    @ValidateNested()
    @Type(() => MachineHandpayMetrics)
    readonly totalMetrics!: MachineHandpayMetrics;

    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => MachineHandpayMetricsForType)
    readonly adjustmentsByType!: MachineHandpayMetricsForType[];

    @ValidateNested()
    @Type(() => MachineHandpayMetrics)
    readonly totalAdjustments!: MachineHandpayMetrics;

    protected constructor() {
        super();
    }

    static create(props: GetMachineHandpayMetricsQueryResultProps): GetMachineHandpayMetricsQueryResult {
        return deserialize(GetMachineHandpayMetricsQueryResult, props);
    }
}
