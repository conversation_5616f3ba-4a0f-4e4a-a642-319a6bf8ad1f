import { TriggerType } from '@tronius/shared-common';

import { Handpay } from './handpay.model';

export enum HandpayOperationType {
    Created = 'created',
    Cancelled = 'cancelled',
    Authorized = 'authorized',
    PaidInProperty = 'paid-in-property',
    TransferredToWallet = 'transferred-to-wallet',
    Voided = 'voided',
    Reversed = 'reversed'
}

export interface HandpayOperation {
    id: string;
    gamingDay: string;
    type: HandpayOperationType;
    triggeredByType: TriggerType.Machine | TriggerType.TransactionPoint;
    triggeredById: string;
    triggeredAt: number;
    createdAt: number;
    comment?: string;

    createdBy: string;

    handpayId: string;
    handpay?: Handpay;
}
