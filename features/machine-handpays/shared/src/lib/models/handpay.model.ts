import { BaseModel } from '@tronius/shared-common';

import { HandpayOperation } from './handpay-operation.model';

export enum HandpayType {
    Jackpot = 'jackpot',
    Progressive = 'progressive',
    RedeemCredits = 'redeem-credits',
    Bonus = 'bonus'
}

export enum HandpayStatus {
    Pending = 'pending',
    Cancelled = 'cancelled',
    Authorized = 'authorized',
    Completed = 'completed',
    Voided = 'voided',
    Reversed = 'reversed'
}

export interface Handpay extends Omit<BaseModel, 'createdAt' | 'propertyId' | 'updatedAt'> {
    machineId: string;
    type: HandpayType;
    amount: number;
    status: HandpayStatus;
    manual: boolean;

    sessionId: string | null;
    customerId: string | null;

    operations?: HandpayOperation[];
    pendingAt: number;
    authorizedAt: number | null;
    paidAt: number | null;
    resolvedAt: number | null;
}

export type CreateHandpay = Pick<Handpay, 'amount' | 'customerId' | 'machineId' | 'sessionId' | 'type'> & {
    comment?: string;
    gamingDay: string;
    pendingAt: number;
};

export interface CancelHandpay {
    id: string;
    comment?: string;
}

export interface AuthorizeHandpay {
    id: string;
    comment?: string;
}

export interface VoidHandpay {
    id: string;
    comment?: string;
}

export interface PayInPropertyHandpay {
    id: string;
    comment?: string;
    customerId?: string | null;
}

export interface TransferToWalletHandpay {
    id: string;
    comment?: string;
}

export interface ReverseHandpay {
    id: string;
    comment?: string;
}
