import { GetMachineHandpayMetricsForGamingDayQuery, GetMachineHandpayMetricsForGamingDayQueryResult } from '@cms/machine-handpays';
import { OnQuery, QueryHandler } from '@cms/message-broker';
import { Injectable } from '@nestjs/common';

import { HandpayOperationService } from '../handpay-operation.service';

@OnQuery(GetMachineHandpayMetricsForGamingDayQuery, GetMachineHandpayMetricsForGamingDayQueryResult)
@Injectable()
export class GetMachineHandpayMetricsForGamingDayQueryHandler
    implements QueryHandler<GetMachineHandpayMetricsForGamingDayQuery, GetMachineHandpayMetricsForGamingDayQueryResult>
{
    constructor(private readonly handpayOperationService: HandpayOperationService) {}

    async handle({
        correlationId,
        machineId,
        gamingDay
    }: GetMachineHandpayMetricsForGamingDayQuery): Promise<GetMachineHandpayMetricsForGamingDayQueryResult> {
        const [{ metricsByType, totalMetrics }, { metricsByType: adjustmentsByType, totalMetrics: totalAdjustments }] = await Promise.all([
            this.handpayOperationService.getMachineHandpayMetricsForGamingDay(machineId, gamingDay, false),
            this.handpayOperationService.getMachineHandpayMetricsForGamingDay(machineId, gamingDay, true)
        ]);
        return GetMachineHandpayMetricsForGamingDayQueryResult.create({
            correlationId,
            metricsByType,
            totalMetrics,
            adjustmentsByType,
            totalAdjustments
        });
    }
}
