import { HandpayPendingEvent } from '@cms/machine-handpays';
import { EventHandler, OnEvent } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import { ErrorUtils, TriggerType } from '@tronius/shared-common';

import { CreateHandpayProps, HandpayService } from '../handpay.service';

@OnEvent(HandpayPendingEvent)
@Injectable()
export class HandpayPendingEventHandler implements EventHandler<HandpayPendingEvent> {
    private readonly logger = new Logger(HandpayPendingEventHandler.name);

    constructor(private readonly handpayService: HandpayService) {}

    async handle({ amount, type, machineId, username, customerId, sessionId }: HandpayPendingEvent): Promise<void> {
        const gamingDay = await this.handpayService.getGamingDay();
        const props: CreateHandpayProps = {
            createHandpay: {
                type,
                amount,
                machineId,
                customerId,
                sessionId,
                gamingDay,
                pendingAt: Date.now()
            },
            triggeredByType: TriggerType.Machine,
            triggeredById: machineId,
            username,
            manual: false
        };
        await this.handpayService.create(props);
    }

    onError(error: unknown): void {
        this.logger.error(`error: ${ErrorUtils.errorToString(error)}`);
    }
}
