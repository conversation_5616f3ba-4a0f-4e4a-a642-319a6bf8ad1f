import { GetMachineHandpayMetricsForPeriodQuery, GetMachineHandpayMetricsForPeriodQueryResult } from '@cms/machine-handpays';
import { OnQuery, QueryHandler } from '@cms/message-broker';
import { Injectable } from '@nestjs/common';

import { HandpayOperationService } from '../handpay-operation.service';

@OnQuery(GetMachineHandpayMetricsForPeriodQuery, GetMachineHandpayMetricsForPeriodQueryResult)
@Injectable()
export class GetMachineHandpayMetricsForPeriodQueryHandler
    implements QueryHandler<GetMachineHandpayMetricsForPeriodQuery, GetMachineHandpayMetricsForPeriodQueryResult>
{
    constructor(private readonly handpayOperationService: HandpayOperationService) {}

    async handle({
        correlationId,
        machineId,
        from,
        to
    }: GetMachineHandpayMetricsForPeriodQuery): Promise<GetMachineHandpayMetricsForPeriodQueryResult> {
        const [{ metricsByType, totalMetrics }, { metricsByType: adjustmentsByType, totalMetrics: totalAdjustments }] = await Promise.all([
            this.handpayOperationService.getMachineHandpayMetricsForPeriod(machineId, from, to, false),
            this.handpayOperationService.getMachineHandpayMetricsForPeriod(machineId, from, to, true)
        ]);
        return GetMachineHandpayMetricsForPeriodQueryResult.create({
            correlationId,
            metricsByType,
            totalMetrics,
            adjustmentsByType,
            totalAdjustments
        });
    }
}
