import { MachineFundsTransferFailedEvent } from '@cms/machine-funds-transfers';
import { HandpayType } from '@cms/machine-handpays';
import { EventHandler, OnEvent } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import { ErrorUtils, TriggerType } from '@tronius/shared-common';

import { moduleName } from '../constants';
import { CreateHandpayProps, HandpayService } from '../handpay.service';

@Injectable()
@OnEvent(MachineFundsTransferFailedEvent)
export class MachineFundsTransferFailedEventHandler implements EventHandler<MachineFundsTransferFailedEvent> {
    private readonly logger = new Logger(MachineFundsTransferFailedEventHandler.name);

    constructor(private readonly handpayService: HandpayService) {}

    async handle({ machineFundsTransfer: { machineId, amount, customerId }, comment }: MachineFundsTransferFailedEvent): Promise<void> {
        const gamingDay = await this.handpayService.getGamingDay();
        const props: CreateHandpayProps = {
            // TODO: Add sessionId
            createHandpay: {
                machineId,
                amount,
                comment,
                type: HandpayType.Bonus,
                customerId: customerId ?? null,
                sessionId: null,
                pendingAt: Date.now(),
                gamingDay
            },
            triggeredByType: TriggerType.Machine,
            triggeredById: machineId,
            username: moduleName,
            manual: true
        };
        await this.handpayService.create(props);
    }

    onError(error: unknown): void {
        this.logger.error(`error: ${ErrorUtils.errorToString(error)}`);
    }
}
