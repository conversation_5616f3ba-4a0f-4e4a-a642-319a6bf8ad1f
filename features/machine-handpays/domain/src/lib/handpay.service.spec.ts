import { CreateHandpay, Handpay, HandpayCreatedEvent, HandpayStatus, HandpayType } from '@cms/machine-handpays';
import { MessageBroker } from '@cms/message-broker';
import { BadRequestError, NotFoundError, TgErrorType, TriggerType, UUIDFactory } from '@tronius/shared-common';
import { GetMachineProfileListItemsByIdsQuery } from '@tronius/shared-domain';
import { addDays, addHours, format } from 'date-fns';

import { CreateHandpayProps, HandpayDao } from './dao/handpay.dao';
import { HandpayService } from './handpay.service';

describe('HandpayService', () => {
    let service: HandpayService;
    let mockDao: Partial<HandpayDao>;
    let mockMessageBroker: Partial<MessageBroker>;

    beforeEach(() => {
        mockDao = { create: jest.fn() };
        mockMessageBroker = { publish: jest.fn(), query: jest.fn() };
        service = new HandpayService(mockDao as unknown as HandpayDao, mockMessageBroker as unknown as MessageBroker);

        // Mock GetMachineProfileListItemsByIdsQuery.create
        jest.spyOn(GetMachineProfileListItemsByIdsQuery, 'create').mockImplementation(
            (props) => props as GetMachineProfileListItemsByIdsQuery
        );
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('create', () => {
        beforeEach(() => {
            mockMessageBroker.query = jest.fn().mockResolvedValue({ gamingDay: new Date().toISOString().split('T')[0] });
        });

        const customerId = UUIDFactory.create();
        const machineId = UUIDFactory.create();
        const sessionId = UUIDFactory.create();
        const mockCreateHandpay: CreateHandpay = {
            comment: 'some comment',
            gamingDay: '2024-05-23',
            pendingAt: Date.now(),
            amount: 100,
            customerId,
            machineId,
            sessionId,
            type: HandpayType.Bonus
        };
        const createHandpayProps: CreateHandpayProps = {
            createHandpay: mockCreateHandpay,
            triggeredByType: TriggerType.TransactionPoint,
            triggeredById: UUIDFactory.create(),
            username: 'some-username',
            manual: true,
            createdAt: Date.now()
        };

        const validCreateHandpayProps: CreateHandpayProps[] = [
            { ...createHandpayProps, triggeredByType: TriggerType.Machine, manual: false },
            { ...createHandpayProps, triggeredByType: TriggerType.TransactionPoint, manual: true }
        ];

        it.each(validCreateHandpayProps)('should create handpay', async (props) => {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getMachineProfileByIdOrThrow').mockResolvedValue({});
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getOpenTransactionPointByIdOrThrow').mockResolvedValue({});

            let createdAtProp = 1748419912722;

            mockDao.create = jest
                .fn()
                .mockImplementation(async ({ createHandpay, manual, createdAt }: CreateHandpayProps): Promise<Handpay> => {
                    createdAtProp = createdAt;
                    return Promise.resolve({
                        ...createHandpay,
                        status: HandpayStatus.Pending,
                        manual,
                        authorizedAt: null,
                        paidAt: null,
                        resolvedAt: null,
                        id: UUIDFactory.create(),
                        operations: [],
                        propertyId: null,
                        createdAt
                    });
                });

            const createdHandpay = await service.create(props);

            expect(mockDao.create).toHaveBeenCalledWith({
                createHandpay: props.createHandpay,
                triggeredByType: props.triggeredByType,
                triggeredById: props.triggeredById,
                username: props.username,
                manual: props.manual,
                createdAt: createdAtProp
            });

            expect(mockMessageBroker.publish).toHaveBeenCalledWith(
                HandpayCreatedEvent,
                expect.objectContaining({
                    handpay: { ...createdHandpay }
                } as HandpayCreatedEvent)
            );
        });

        it('should not create handpay if gaming day is in the future', async () => {
            const futureGamingDay = addDays(new Date(), 1);
            const props = {
                ...createHandpayProps,
                createHandpay: { ...createHandpayProps.createHandpay, gamingDay: format(futureGamingDay, 'yyyy-MM-dd') }
            };

            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getMachineProfileByIdOrThrow').mockResolvedValue({});
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getOpenTransactionPointByIdOrThrow').mockResolvedValue({});

            await expect(service.create(props)).rejects.toThrowError(BadRequestError);
        });

        it('should not create handpay if pendingAt is in the future', async () => {
            const futureDate = addHours(new Date(), 1);
            const props = {
                ...createHandpayProps,
                createHandpay: { ...createHandpayProps.createHandpay, pendingAt: futureDate.getTime() }
            };

            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getMachineProfileByIdOrThrow').mockResolvedValue({});
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getOpenTransactionPointByIdOrThrow').mockResolvedValue({});

            await expect(service.create(props)).rejects.toThrowError(BadRequestError);
        });

        it('should not create handpay if machine profile was not found', async () => {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getMachineProfileByIdOrThrow').mockRejectedValue(
                new NotFoundError({
                    errorType: TgErrorType.MachineNotFound,
                    message: 'Machine not found.',
                    params: { machineId: createHandpayProps.createHandpay.machineId }
                })
            );
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getOpenTransactionPointByIdOrThrow').mockResolvedValue({});

            await expect(service.create(createHandpayProps)).rejects.toThrowError(NotFoundError);
        });

        it('should not create handpay if transaction point was not found', async () => {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getMachineProfileByIdOrThrow').mockResolvedValue({});
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            jest.spyOn(service as any, 'getOpenTransactionPointByIdOrThrow').mockRejectedValue(
                new NotFoundError({
                    errorType: TgErrorType.TransactionPointNotFound,
                    message: 'Transaction point not found.',
                    params: { transactionPointId: createHandpayProps.createHandpay.sessionId }
                })
            );

            await expect(service.create(createHandpayProps)).rejects.toThrowError(NotFoundError);
        });
    });
});
