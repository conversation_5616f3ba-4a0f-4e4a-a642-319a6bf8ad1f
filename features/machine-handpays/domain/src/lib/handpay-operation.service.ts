import {
    HandpayOperation,
    HandpayType,
    MachineHandpayMetrics,
    MachineHandpayMetricsForType,
    MachineHandpayReconciliationReport
} from '@cms/machine-handpays';
import { MessageBroker } from '@cms/message-broker';
import { Injectable } from '@nestjs/common';
import { TgPaginationRequest, TgPaginationResponse } from '@tronius/shared-common';
import { GetMachineProfileListItemsByIdsQuery, GetMachineProfileListItemsByIdsQueryResult } from '@tronius/shared-domain';
import groupBy from 'lodash/groupBy';

import { HandpayOperationDao } from './dao/handpay-operation.dao';

@Injectable()
export class HandpayOperationService {
    constructor(
        private readonly dao: HandpayOperationDao,
        private readonly messageBroker: MessageBroker
    ) {}

    async list(request: TgPaginationRequest<HandpayOperation>): Promise<TgPaginationResponse<HandpayOperation>> {
        return this.dao.list(request.start, request.count, request.filters, request.sorts, request.relations);
    }

    /**
     * Returns the handpay reconciliation report per machine for the given gaming day
     * @param gamingDay the gaming day (YYYY-MM-DD)
     * @returns the handpay reconciliation report per machine for the given gaming day
     */
    async getHandpayReconciliationReportsForGamingDay(gamingDay: string): Promise<MachineHandpayReconciliationReport[]> {
        const handpaysLatestOperations = await this.getAllHandpaysLatestOperationForGamingDayGroupedByMachineId(gamingDay);

        if (handpaysLatestOperations.size === 0) {
            return [];
        }

        const { machines } = await this.messageBroker.query(
            GetMachineProfileListItemsByIdsQuery.create({ machineIds: Array.from(handpaysLatestOperations.keys()) }),
            GetMachineProfileListItemsByIdsQueryResult
        );

        return machines.map((machine) => {
            const metrics = MachineHandpayMetrics.create({ handpayOperations: handpaysLatestOperations.get(machine.id) || [] });
            return MachineHandpayReconciliationReport.create({ machine, metrics });
        });
    }

    /**
     * Returns the handpay metrics for the given machine and period of time (maximum 24 hours)
     * @param machineId the machine ID
     * @param from the start date
     * @param to the end date
     * @param manual getting only manual handpay or not (undefined = both)
     * @returns the handpay metrics for the given machine and period
     */
    async getMachineHandpayMetricsForPeriod(
        machineId: string,
        from: Date,
        to: Date,
        manual?: boolean
    ): Promise<{ totalMetrics: MachineHandpayMetrics; metricsByType: MachineHandpayMetricsForType[] }> {
        const handpaysLatestOperations = await this.dao.getHandpaysLatestOperationForPeriod(from, to, machineId, manual);

        const totalMetrics = MachineHandpayMetrics.create({ handpayOperations: handpaysLatestOperations });

        const metricsByType = Object.values(HandpayType).map((handpayType) => {
            const operations = handpaysLatestOperations.filter((op) => op.handpay?.type === handpayType);
            return MachineHandpayMetricsForType.create({
                type: handpayType,
                metrics: MachineHandpayMetrics.create({ handpayOperations: operations })
            });
        });

        return { totalMetrics, metricsByType };
    }

    /**
     * Returns the handpay metrics for the given machine and gaming day
     * @param machineId the machine ID
     * @param gamingDay the gaming day (YYYY-MM-DD)
     * @param manual getting only manual handpay or not (undefined = both)
     * @returns the handpay metrics for the given machine and gaming day
     */
    async getMachineHandpayMetricsForGamingDay(
        machineId: string,
        gamingDay: string,
        manual?: boolean
    ): Promise<{ totalMetrics: MachineHandpayMetrics; metricsByType: MachineHandpayMetricsForType[] }> {
        const handpaysLatestOperations = await this.dao.getHandpaysLatestOperationForGamingDay(gamingDay, machineId, manual);
        const totalMetrics = MachineHandpayMetrics.create({ handpayOperations: handpaysLatestOperations });

        const metricsByType = Object.values(HandpayType).map((handpayType) => {
            const operations = handpaysLatestOperations.filter((op) => op.handpay?.type === handpayType);
            return MachineHandpayMetricsForType.create({
                type: handpayType,
                metrics: MachineHandpayMetrics.create({ handpayOperations: operations })
            });
        });

        return { totalMetrics, metricsByType };
    }

    /**
     * Returns the latest handpays operation (per handpay) for the given search params, grouped by machine id
     * @param gamingDay the gaming day (YYYY-MM-DD)
     * @param manual getting only manual handpay or not (undefined = both)
     * @returns the latest handpay operations grouped by machine ID
     */
    private async getAllHandpaysLatestOperationForGamingDayGroupedByMachineId(
        gamingDay: string,
        manual?: boolean
    ): Promise<Map<string, HandpayOperation[]>> {
        const handpaysLatestOperations = await this.dao.getHandpaysLatestOperationForGamingDay(gamingDay, undefined, manual);

        if (handpaysLatestOperations.length === 0) {
            return new Map();
        }

        const handpaysOperationsByMachineId = new Map(
            Object.entries(
                groupBy(
                    handpaysLatestOperations.filter((op) => op.handpay?.machineId),
                    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                    (op) => op.handpay!.machineId
                )
            )
        );

        return handpaysOperationsByMachineId;
    }
}
