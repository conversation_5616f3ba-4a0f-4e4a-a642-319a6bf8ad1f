import { CreateHandpay, HandpayConstraints, HandpayType } from '@cms/machine-handpays';
import { IsGamingDay } from '@cms/validation';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsInt, IsNumber, IsOptional, IsString, IsUUID, Max, Min } from 'class-validator';

export class CreateHandpayDto implements CreateHandpay {
    @ApiProperty({
        enum: HandpayType,
        required: true,
        example: HandpayType.RedeemCredits
    })
    @IsEnum(HandpayType)
    type!: HandpayType;

    @ApiProperty({
        type: 'number',
        required: true,
        example: 12.34
    })
    @IsNumber({ maxDecimalPlaces: HandpayConstraints.handpayMaxDecimalPlaces })
    @Min(HandpayConstraints.handpayMinValue)
    @Max(HandpayConstraints.handpayMaxValue)
    amount!: number;

    @ApiProperty({
        type: 'string',
        required: true,
        // eslint-disable-next-line spellcheck/spell-checker
        example: 'acde070d-8c4c-4f0d-9d8a-162843c10333'
    })
    @IsUUID()
    machineId!: string;

    @ApiProperty({
        type: 'string',
        required: true,
        example: '2025-05-26'
    })
    @IsGamingDay()
    gamingDay!: string;

    @ApiPropertyOptional({
        type: 'number'
    })
    @IsInt()
    pendingAt!: number;

    @ApiPropertyOptional({
        type: 'string'
    })
    @IsOptional()
    @IsString()
    comment?: string;

    @ApiPropertyOptional({
        type: 'string',
        format: 'uuid'
    })
    @IsOptional()
    @IsUUID()
    customerId: string | null = null;

    @ApiPropertyOptional({
        type: 'string',
        format: 'uuid'
    })
    @IsOptional()
    @IsUUID()
    sessionId: string | null = null;
}
