import { GetCasinoSettingsQuery, GetCasinoSettingsQueryResult, GetGamingDayQuery, GetGamingDayQueryResult } from '@cms/casino-operator';
import {
    CreateHandpay,
    Handpay,
    HandpayCreatedEvent,
    HandpayOperationType,
    HandpayResetCommand,
    HandpayStatus,
    HandpayStatusUpdatedEvent
} from '@cms/machine-handpays';
import { MessageBroker } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import {
    BadRequestError,
    ConflictError,
    ConnectionStatus,
    NotFoundError,
    TgErrorType,
    TgFilterOperator,
    TgPaginationRequest,
    TgPaginationResponse,
    TriggerType
} from '@tronius/shared-common';
import {
    GetMachineCurrentByIdQuery,
    GetMachineCurrentByIdQueryResult,
    GetMachineProfileByIdQuery,
    GetMachineProfileByIdQueryResult,
    GetOpenTransactionPointByIdQuery,
    GetOpenTransactionPointByIdQueryResult,
    MachineCurrent,
    MachineProfile,
    TransactionPoint
} from '@tronius/shared-domain';

import { HandpayDao } from './dao/handpay.dao';

export interface CreateHandpayProps {
    createHandpay: CreateHandpay;
    triggeredByType: TriggerType.Machine | TriggerType.TransactionPoint;
    triggeredById: string;
    username: string;
    manual: boolean;
}

export interface CancelHandpayProps {
    handpayId: string;
    triggeredByType: TriggerType.Machine | TriggerType.TransactionPoint;
    triggeredById: string;
    username: string;
    comment?: string;
}

export interface ChangeHandpayStatusProps {
    handpayId: string;
    transactionPointId: string;
    username: string;
    comment?: string;
    customerId?: string;
}

@Injectable()
export class HandpayService {
    private readonly logger = new Logger(HandpayService.name);

    constructor(
        private readonly dao: HandpayDao,
        private readonly messageBroker: MessageBroker
    ) {}

    async list(request: TgPaginationRequest<Handpay>): Promise<TgPaginationResponse<Handpay>> {
        return this.dao.list(request.start, request.count, request.filters, request.sorts, request.relations);
    }

    async getLastHandpayByMachineId(machineId: string): Promise<Handpay> {
        const handpay = await this.dao.getLastHandpayByMachineId(machineId);
        if (!handpay) {
            throw new NotFoundError({
                errorType: TgErrorType.HandpayNotFound,
                message: 'Handpay not found.',
                params: { machineId }
            });
        }

        return handpay;
    }

    async create({ createHandpay, triggeredByType, triggeredById, username, manual }: CreateHandpayProps): Promise<Handpay> {
        const validations: Array<Promise<unknown>> = [this.getMachineProfileByIdOrThrow(createHandpay.machineId)];
        if (triggeredByType === TriggerType.TransactionPoint) {
            validations.push(this.getOpenTransactionPointByIdOrThrow(triggeredById));
        }
        await Promise.all(validations);

        const { gamingDay: currentGamingDay } = await this.messageBroker.query(GetGamingDayQuery.create({}), GetGamingDayQueryResult);
        if (createHandpay.gamingDay > currentGamingDay) {
            throw new BadRequestError({
                message: 'Gaming day is in the future.',
                params: { createHandpay }
            });
        }

        if (createHandpay.pendingAt > Date.now()) {
            throw new BadRequestError({
                message: 'Handpay timestamp is in the future.',
                params: { createHandpay }
            });
        }

        // Create the handpay
        const handpay = await this.dao.create({
            createHandpay,
            triggeredByType,
            triggeredById,
            username,
            manual,
            createdAt: Date.now()
        });

        // Emit the created event
        await this.messageBroker.publish<HandpayCreatedEvent>(
            HandpayCreatedEvent,
            HandpayCreatedEvent.create({
                handpay,
                triggeredByType,
                triggeredById,
                username,
                comment: createHandpay.comment
            })
        );

        return handpay;
    }

    async cancel({ handpayId, triggeredById, username, comment, triggeredByType }: CancelHandpayProps): Promise<void> {
        const [gamingDay, handpay] = await Promise.all([
            triggeredByType === TriggerType.TransactionPoint
                ? this.getOpenTransactionPointByIdOrThrow(triggeredById).then((tp) => tp.currentGamingDay as unknown as string)
                : this.getGamingDay(),
            this.getByIdOrThrow(handpayId)
        ]);

        const previousStatus = handpay.status;
        const resolvedAt = Date.now();
        const triggeredAt = resolvedAt;

        await this.dao.updateStatusAndInsertOperation({
            id: handpayId,
            triggeredById,
            gamingDay,
            username,
            status: HandpayStatus.Cancelled,
            operationType: HandpayOperationType.Cancelled,
            isPreviousStatusInvalid: (status) => status !== HandpayStatus.Pending && status !== HandpayStatus.Authorized,
            comment,
            triggeredByType,
            resolvedAt,
            triggeredAt
        });

        await this.publishHandpayStatusUpdatedEvent(handpayId, username, triggeredByType, triggeredById, previousStatus);
    }

    async authorize({ handpayId, transactionPointId, username, comment, customerId }: ChangeHandpayStatusProps): Promise<void> {
        const [transactionPoint, handpay] = await Promise.all([
            this.getOpenTransactionPointByIdOrThrow(transactionPointId),
            this.getByIdOrThrow(handpayId)
        ]);
        const transactionPointGamingDay = transactionPoint.currentGamingDay as unknown as string;
        const previousStatus = handpay.status;

        const authorizedAt = Date.now();
        const triggeredAt = authorizedAt;

        await this.dao.updateStatusAndInsertOperation({
            id: handpayId,
            triggeredById: transactionPointId,
            gamingDay: transactionPointGamingDay,
            username,
            status: HandpayStatus.Authorized,
            operationType: HandpayOperationType.Authorized,
            isPreviousStatusInvalid: (status) => status !== HandpayStatus.Pending,
            comment,
            triggeredByType: TriggerType.TransactionPoint,
            customerId,
            authorizedAt,
            triggeredAt
        });

        await this.publishHandpayStatusUpdatedEvent(handpayId, username, TriggerType.TransactionPoint, transactionPointId, previousStatus);
    }

    async void({ handpayId, transactionPointId, username, comment, customerId }: ChangeHandpayStatusProps): Promise<void> {
        const [transactionPoint, handpay] = await Promise.all([
            this.getOpenTransactionPointByIdOrThrow(transactionPointId),
            this.getByIdOrThrow(handpayId)
        ]);
        const transactionPointGamingDay = transactionPoint.currentGamingDay as unknown as string;
        const previousStatus = handpay.status;

        const resolvedAt = Date.now();
        const triggeredAt = resolvedAt;

        await this.dao.updateStatusAndInsertOperation({
            id: handpayId,
            triggeredById: transactionPointId,
            gamingDay: transactionPointGamingDay,
            username,
            status: HandpayStatus.Voided,
            operationType: HandpayOperationType.Voided,
            isPreviousStatusInvalid: (status) => status !== HandpayStatus.Authorized && status !== HandpayStatus.Pending,
            comment,
            triggeredByType: TriggerType.TransactionPoint,
            customerId,
            resolvedAt,
            triggeredAt
        });

        await this.publishHandpayStatusUpdatedEvent(handpayId, username, TriggerType.TransactionPoint, transactionPointId, previousStatus);
    }

    async canPayInProperty(
        handpayId: string,
        transactionPointId: string,
        _username: string
    ): Promise<{ handpay: Handpay; transactionPoint: TransactionPoint }> {
        const [transactionPoint, handpay] = await Promise.all([
            this.getOpenTransactionPointByIdOrThrow(transactionPointId),
            this.getPayableHandpayOrThrow(handpayId)
        ]);

        return { handpay, transactionPoint };
    }

    async payInProperty({ handpayId, transactionPointId, username, comment, customerId }: ChangeHandpayStatusProps): Promise<Handpay> {
        const { handpay, transactionPoint } = await this.canPayInProperty(handpayId, transactionPointId, username);
        const transactionPointGamingDay = transactionPoint.currentGamingDay as unknown as string;

        const paidAt = Date.now();
        const triggeredAt = paidAt;

        await this.dao.updateStatusAndInsertOperation({
            id: handpayId,
            triggeredById: transactionPointId,
            gamingDay: transactionPointGamingDay,
            username,
            status: HandpayStatus.Completed,
            operationType: HandpayOperationType.PaidInProperty,
            isPreviousStatusInvalid: (status) => status !== handpay.status,
            comment,
            triggeredByType: TriggerType.TransactionPoint,
            customerId,
            paidAt,
            authorizedAt: handpay.authorizedAt ? undefined : paidAt,
            triggeredAt
        });

        return this.publishHandpayStatusUpdatedEvent(handpayId, username, TriggerType.TransactionPoint, transactionPointId, handpay.status);
    }

    async reverse({ handpayId, transactionPointId, username, comment, customerId }: ChangeHandpayStatusProps): Promise<void> {
        const [transactionPoint, handpay] = await Promise.all([
            this.getOpenTransactionPointByIdOrThrow(transactionPointId),
            this.getByIdOrThrow(handpayId)
        ]);
        const transactionPointGamingDay = transactionPoint.currentGamingDay as unknown as string;
        const previousStatus = handpay.status;

        const resolvedAt = Date.now();
        const triggeredAt = resolvedAt;

        await this.dao.updateStatusAndInsertOperation({
            id: handpayId,
            triggeredById: transactionPointId,
            gamingDay: transactionPointGamingDay,
            username,
            status: HandpayStatus.Reversed,
            operationType: HandpayOperationType.Reversed,
            isPreviousStatusInvalid: (status) => status !== HandpayStatus.Completed,
            comment,
            triggeredByType: TriggerType.TransactionPoint,
            customerId,
            resolvedAt,
            triggeredAt
        });

        await this.publishHandpayStatusUpdatedEvent(handpayId, username, TriggerType.TransactionPoint, transactionPointId, previousStatus);
    }

    async reset(machineId: string, username: string): Promise<void> {
        const machineCurrent = await this.getMachineCurrentByIdOrThrow(machineId);

        if (machineCurrent.connectionStatus !== ConnectionStatus.Connected) {
            throw new ConflictError({
                errorType: TgErrorType.MachineNotConnected,
                message: 'Machine is not connected.',
                params: { machineId, connectionStatus: machineCurrent.connectionStatus }
            });
        }

        await this.messageBroker.command(HandpayResetCommand.create({ machineId, username }));
    }

    async getPayableHandpays(start: number, count: number): Promise<TgPaginationResponse<Handpay>> {
        const payableStatuses = await this.getPayableStatuses();

        return this.list({
            start,
            count,
            filters: [{ field: 'status', operator: TgFilterOperator.In, value: payableStatuses }]
        });
    }

    async getPayableHandpayCount(): Promise<number> {
        const payableStatuses = await this.getPayableStatuses();

        return this.dao.countByStatus(payableStatuses);
    }

    async getPayableHandpayOrThrow(id: string): Promise<Handpay> {
        const handpay = await this.getByIdOrThrow(id);
        const payableStatuses = await this.getPayableStatuses();

        if (!payableStatuses.includes(handpay.status)) {
            throw new ConflictError({
                errorType: TgErrorType.HandpayInvalidStatus,
                message: 'Handpay is not payable.',
                params: { handpayId: id, status: handpay.status }
            });
        }

        return handpay;
    }

    async getByIdOrThrow(id: string): Promise<Handpay> {
        const handpay = await this.dao.findOneBy({ id });
        if (!handpay) {
            throw new NotFoundError({
                errorType: TgErrorType.HandpayNotFound,
                message: 'Handpay not found.',
                params: { handpayId: id }
            });
        }
        return handpay;
    }

    async updateCustomerIdOnHandpaysLinkedToMachineSession(sessionId: string, customerId: string): Promise<void> {
        await this.dao.updateCustomerIdOnHandpaysLinkedToMachineSession(sessionId, customerId);
    }

    async getGamingDay(): Promise<string> {
        const { gamingDay } = await this.messageBroker.query(GetGamingDayQuery.create({}), GetGamingDayQueryResult);
        return gamingDay;
    }

    private async getMachineProfileByIdOrThrow(machineId: string): Promise<MachineProfile> {
        const { machineProfile } = await this.messageBroker.query(
            GetMachineProfileByIdQuery.create({ machineId }),
            GetMachineProfileByIdQueryResult
        );
        return machineProfile;
    }

    private async getMachineCurrentByIdOrThrow(machineId: string): Promise<MachineCurrent> {
        const { machineCurrent } = await this.messageBroker.query(
            GetMachineCurrentByIdQuery.create({ machineId }),
            GetMachineCurrentByIdQueryResult
        );
        return machineCurrent;
    }

    private async getPayableStatuses(): Promise<HandpayStatus[]> {
        const requiresAuthorization = await this.doesHandpayRequireAuthorization();
        return requiresAuthorization ? [HandpayStatus.Authorized] : [HandpayStatus.Pending, HandpayStatus.Authorized];
    }

    private async doesHandpayRequireAuthorization(): Promise<boolean> {
        const { settings: casinoSettings } = await this.messageBroker.query(GetCasinoSettingsQuery.create(), GetCasinoSettingsQueryResult);
        return casinoSettings.payment.handpayRequiresAuthorization;
    }

    private async getOpenTransactionPointByIdOrThrow(transactionPointId: string): Promise<TransactionPoint> {
        const { transactionPoint } = await this.messageBroker.query(
            GetOpenTransactionPointByIdQuery.create({ transactionPointId }),
            GetOpenTransactionPointByIdQueryResult
        );

        return transactionPoint;
    }

    private async publishHandpayStatusUpdatedEvent(
        handpayId: string,
        username: string,
        triggeredByType: TriggerType,
        triggeredById: string,
        previousStatus: HandpayStatus
    ): Promise<Handpay> {
        const handpay = await this.getByIdOrThrow(handpayId);
        await this.messageBroker.publish(
            HandpayStatusUpdatedEvent,
            HandpayStatusUpdatedEvent.create({ triggeredByType, triggeredById, handpay, username, previousStatus })
        );
        return handpay;
    }
}
