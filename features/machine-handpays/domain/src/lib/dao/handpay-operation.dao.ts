import { HandpayOperation } from '@cms/machine-handpays';
import { InjectRepository } from '@cms/typeorm';
import { Injectable } from '@nestjs/common';
import { TgAbstractDao } from '@tronius/backend-common';
import { HandpayOperationEntity } from '@tronius/backend-database';
import { DeepPartial, Repository } from 'typeorm';

import { HandpayDao } from './handpay.dao';

@Injectable()
export class HandpayOperationDao extends TgAbstractDao<HandpayOperation, HandpayOperationEntity> {
    constructor(
        @InjectRepository(HandpayOperationEntity)
        override readonly repository: Repository<HandpayOperationEntity>
    ) {
        super(repository, HandpayOperationEntity);
    }

    /**
     * Returns the latest handpay operation (per handpay) for the given gaming day
     * @param gamingDay the gaming day (YYYY-MM-DD)
     * @param machineId the machine id
     * @param manual if manual handpay
     * @returns the latest handpay operations
     */
    async getHandpaysLatestOperationForGamingDay(gamingDay: string, machineId?: string, manual?: boolean): Promise<HandpayOperation[]> {
        const query = this.repository
            .createQueryBuilder('op')
            .innerJoinAndSelect('op.handpay', 'handpay')
            .distinctOn(['op.handpay_id']) // Sadly no distinct in find()
            .andWhere('op.gamingDay = :gamingDay', { gamingDay })
            .orderBy('op.handpay_id', 'ASC')
            .addOrderBy('op.triggeredAt', 'DESC');

        if (machineId) {
            query.andWhere('handpay.machineId = :machineId', { machineId });
        }
        if (manual) {
            query.andWhere('handpay.manual = :manual', { manual });
        }

        return query.getMany();
    }

    async getHandpaysLatestOperationForPeriod(from: Date, to: Date, machineId?: string, manual?: boolean): Promise<HandpayOperation[]> {
        const query = this.repository
            .createQueryBuilder('op')
            .innerJoinAndSelect('op.handpay', 'handpay')
            .distinctOn(['op.handpay_id']) // Sadly no distinct in find()
            .andWhere('op.triggeredAt BETWEEN :from AND :to', { from, to })
            .orderBy('op.handpay_id', 'ASC')
            .addOrderBy('op.triggeredAt', 'DESC');

        if (machineId) {
            query.andWhere('handpay.machineId = :machineId', { machineId });
        }
        if (manual) {
            query.andWhere('handpay.manual = :manual', { manual });
        }

        return query.getMany();
    }

    protected entityToDto(entity: HandpayOperationEntity): HandpayOperation;
    protected entityToDto(entity: HandpayOperationEntity | null): HandpayOperation | null {
        return entity ? HandpayOperationDao.toDto(entity) : null;
    }

    protected dtoToEntity(dto: HandpayOperation): DeepPartial<HandpayOperationEntity>;
    protected dtoToEntity(dto: HandpayOperation | null): DeepPartial<HandpayOperationEntity> | null {
        return dto ? HandpayOperationDao.fromDto(dto) : null;
    }

    static toDto(entity: HandpayOperationEntity): HandpayOperation {
        return {
            id: entity.id,
            type: entity.type,
            triggeredByType: entity.triggeredByType,
            triggeredById: entity.triggeredById,
            triggeredAt: entity.triggeredAt,
            createdAt: entity.createdAt,
            comment: entity.comment,
            createdBy: entity.createdBy,
            gamingDay: entity.gamingDay,
            handpayId: entity.handpayId,
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
            handpay: entity.handpay ? HandpayDao.toDto(entity.handpay) : undefined
        };
    }

    static fromDto(dto: HandpayOperation): HandpayOperationEntity {
        const entity = new HandpayOperationEntity();
        entity.id = dto.id;
        entity.type = dto.type;
        entity.triggeredByType = dto.triggeredByType;
        entity.triggeredById = dto.triggeredById;
        entity.triggeredAt = dto.triggeredAt;
        entity.createdAt = dto.createdAt;
        entity.comment = dto.comment;
        entity.createdBy = dto.createdBy;
        entity.gamingDay = dto.gamingDay;

        entity.handpayId = dto.handpayId;
        // We don't need relations here

        return entity;
    }
}
