import { Create<PERSON>andpay, Handpay, Hand<PERSON>yOperation, HandpayOperationType, HandpayStatus } from '@cms/machine-handpays';
import { InjectRepository } from '@cms/typeorm';
import { Injectable, Logger } from '@nestjs/common';
import { TgAbstractDao } from '@tronius/backend-common';
import { HandpayEntity, HandpayOperationEntity } from '@tronius/backend-database';
import { ConflictError, TgErrorType, TriggerType } from '@tronius/shared-common';
import { DeepPartial, EntityManager, In, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

import { HandpayOperationDao } from './handpay-operation.dao';

export interface CreateHandpayProps {
    createHandpay: CreateHandpay;
    triggeredByType: TriggerType.Machine | TriggerType.TransactionPoint;
    triggeredById: string;
    username: string;
    manual: boolean;
    createdAt: number;
}

export interface ChangeHandpayStatusProps {
    id: string;
    triggeredById: string;
    gamingDay: string;
    username: string;
    status: HandpayStatus;
    operationType: HandpayOperationType;
    isPreviousStatusInvalid: (status: HandpayStatus) => boolean;
    triggeredAt: number;
    triggeredByType: TriggerType.Machine | TriggerType.TransactionPoint;
    comment?: string;
    customerId?: string;
    pendingAt?: number;
    authorizedAt?: number;
    paidAt?: number;
    resolvedAt?: number;
}

@Injectable()
export class HandpayDao extends TgAbstractDao<Handpay, HandpayEntity> {
    protected override readonly logger = new Logger(HandpayDao.name);

    constructor(
        @InjectRepository(HandpayEntity)
        override readonly repository: Repository<HandpayEntity>
    ) {
        super(repository, HandpayEntity);
    }

    async countByStatus(statuses: HandpayStatus | HandpayStatus[]): Promise<number> {
        if (Array.isArray(statuses)) {
            return this.repository.count({ where: { status: In(statuses) } });
        }
        return this.repository.count({ where: { status: statuses } });
    }

    async countHandpaysAuthorizedBetween(from: Date, to: Date, machineId?: string): Promise<number> {
        // Convert JavaScript Date objects to ISO strings for database compatibility
        const params: string[] = [from.toISOString(), to.toISOString()];

        let sql = `
        SELECT SUM(amount)
        FROM handpay
        WHERE authorized_at BETWEEN $1 AND $2
        AND (resolved_at IS NULL OR resolved_at > $2)
        `;

        if (machineId) {
            sql += ' AND machine_id = $3';
            params.push(machineId);
        }

        const [{ sum }] = (await this.repository.query(sql, params)) as Array<{ sum: number }>;
        return sum || 0;
    }

    async create(props: CreateHandpayProps, manager?: EntityManager): Promise<Handpay> {
        const { createHandpay, triggeredByType, triggeredById, username, manual, createdAt } = props;
        const { comment, pendingAt, gamingDay, ...rest } = createHandpay;
        // We prepare handpays to be saved to the database
        const handpay: Partial<Handpay> = {
            ...rest,
            status: HandpayStatus.Pending,
            manual,
            pendingAt,
            operations: [
                {
                    type: HandpayOperationType.Created,
                    triggeredById,
                    createdBy: username,
                    gamingDay,
                    triggeredByType,
                    comment,
                    triggeredAt: pendingAt,
                    createdAt
                } as HandpayOperation
            ]
        };

        return this.save(handpay, manager);
    }

    async getLastHandpayByMachineId(machineId: string): Promise<Handpay | null> {
        return this.repository.findOne({ where: { machineId, manual: false }, order: { pendingAt: 'DESC' } });
    }

    async getHandpaysLinkedToMachineSession(sessionId: string): Promise<Handpay[]> {
        return this.repository.find({ where: { sessionId } });
    }

    async updateCustomerIdOnHandpaysLinkedToMachineSession(sessionId: string, customerId: string): Promise<void> {
        await this.repository.update({ sessionId }, { customerId });
    }

    async updateStatusAndInsertOperation(props: ChangeHandpayStatusProps): Promise<void> {
        const {
            id,
            triggeredById,
            gamingDay,
            username,
            status,
            operationType,
            isPreviousStatusInvalid,
            comment,
            triggeredAt,
            triggeredByType,
            customerId,
            paidAt,
            authorizedAt,
            resolvedAt
        } = props;
        await this.repository.manager.transaction(async (manager) => {
            const handpay = await manager.findOneOrFail(HandpayEntity, {
                where: { id },
                // Locking, so that other transactions cannot redeem the same handpay
                lock: { mode: 'pessimistic_write' }
            });

            if (isPreviousStatusInvalid(handpay.status)) {
                throw new ConflictError({
                    errorType: TgErrorType.HandpayInvalidStatus,
                    message: 'Handpay is not payable.',
                    params: { handpayId: handpay.id, status: handpay.status }
                });
            }

            const updatedProperties: DeepPartial<HandpayEntity> = { status, customerId };

            if (paidAt) {
                updatedProperties.paidAt = paidAt;
            }

            if (authorizedAt) {
                updatedProperties.authorizedAt = authorizedAt;
            }

            if (resolvedAt) {
                updatedProperties.resolvedAt = resolvedAt;
            }

            await manager.update(HandpayEntity, handpay.id, updatedProperties);

            const operation: QueryDeepPartialEntity<HandpayOperationEntity> = {
                type: operationType,
                triggeredByType,
                triggeredById,
                createdBy: username,
                gamingDay,
                comment,
                handpayId: handpay.id,
                triggeredAt
            };
            await manager.insert(HandpayOperationEntity, operation);
        });
    }

    protected entityToDto(entity: HandpayEntity): Handpay;
    protected entityToDto(entity: HandpayEntity | null): Handpay | null {
        return entity ? HandpayDao.toDto(entity) : null;
    }

    protected dtoToEntity(dto: Handpay): DeepPartial<HandpayEntity>;
    protected dtoToEntity(dto: Handpay | null): DeepPartial<HandpayEntity> | null {
        if (!dto) {
            return null;
        }

        const entity = new HandpayEntity();
        entity.id = dto.id;
        entity.type = dto.type;
        entity.amount = dto.amount;
        entity.status = dto.status;
        entity.manual = dto.manual;
        entity.machineId = dto.machineId;
        entity.sessionId = dto.sessionId;
        entity.customerId = dto.customerId;
        entity.operations = dto.operations?.map((operation) => HandpayOperationDao.fromDto(operation));
        entity.pendingAt = dto.pendingAt;
        entity.authorizedAt = dto.authorizedAt;
        entity.paidAt = dto.paidAt;
        entity.resolvedAt = dto.resolvedAt;

        return entity;
    }

    static toDto(entity: HandpayEntity): Handpay {
        return {
            id: entity.id,
            type: entity.type,
            amount: entity.amount,
            status: entity.status,
            manual: entity.manual,
            machineId: entity.machineId,
            sessionId: entity.sessionId,
            customerId: entity.customerId,
            pendingAt: entity.pendingAt,
            authorizedAt: entity.authorizedAt,
            paidAt: entity.paidAt,
            resolvedAt: entity.resolvedAt,
            operations: entity.operations?.map((operation) => HandpayOperationDao.toDto(operation))
        };
    }
}
