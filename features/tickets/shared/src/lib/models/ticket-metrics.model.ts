import { MachineWinReconciliationReportMetrics, ReconciliationReportMetrics } from '@cms/machine-metrics';
import { deserialize } from '@cms/serialization';
import { IsNullable } from '@cms/validation';
import { IsInt, Min } from 'class-validator';

export interface TicketReconciliationReportMetricsProps {
    accountingTicketsIn: number | null;
    meterTicketsIn: number | null;
    accountingTicketsOut: number | null;
    meterTicketsOut: number | null;
}

export class TicketReconciliationReportMetrics implements TicketReconciliationReportMetricsProps {
    @IsInt()
    @Min(0)
    @IsNullable()
    accountingTicketsIn!: number | null;

    @IsInt()
    @Min(0)
    @IsNullable()
    meterTicketsIn!: number | null;

    @IsInt()
    @Min(0)
    @IsNullable()
    accountingTicketsOut!: number | null;

    @IsInt()
    @Min(0)
    @IsNullable()
    meterTicketsOut!: number | null;

    @IsInt()
    @Min(0)
    @IsNullable()
    get varianceIn(): number | null {
        if (this.accountingTicketsIn === null || this.meterTicketsIn === null) {
            return null;
        }
        return Math.abs(this.accountingTicketsIn - this.meterTicketsIn);
    }

    @IsInt()
    @Min(0)
    @IsNullable()
    get varianceOut(): number | null {
        if (this.accountingTicketsOut === null || this.meterTicketsOut === null) {
            return null;
        }
        return Math.abs(this.accountingTicketsOut - this.meterTicketsOut);
    }

    @IsInt()
    @Min(0)
    @IsNullable()
    get variance(): number | null {
        if (this.varianceIn === null || this.varianceOut === null) {
            return null;
        }
        return Math.max(this.varianceIn, this.varianceOut);
    }

    static map(props: TicketReconciliationReportMetricsProps): TicketReconciliationReportMetrics {
        return deserialize(TicketReconciliationReportMetrics, props);
    }

    static extractFromAllMetrics(reportMetrics: MachineWinReconciliationReportMetrics): TicketReconciliationReportMetrics {
        return this.map({
            accountingTicketsIn: reportMetrics[ReconciliationReportMetrics.TicketRevenue] ?? null,

            meterTicketsIn: reportMetrics[ReconciliationReportMetrics.FinanceTicketsIn] ?? null,
            accountingTicketsOut: reportMetrics[ReconciliationReportMetrics.TicketExpenses] ?? null,
            meterTicketsOut: reportMetrics[ReconciliationReportMetrics.FinanceTicketsOut] ?? null
        });
    }

    static mapList(propsList: TicketReconciliationReportMetrics[]): TicketReconciliationReportMetrics[] {
        return propsList.map((props) => TicketReconciliationReportMetrics.map(props));
    }
}
