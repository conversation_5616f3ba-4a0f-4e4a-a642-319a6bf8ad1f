import { MachineWinReconciliationReportItem } from '@cms/machine-metrics';
import { deserialize } from '@cms/serialization';
import { MachineProfileListItem } from '@tronius/shared-domain';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';

import { TicketReconciliationReportMetrics } from './ticket-metrics.model';

/**
 * Model for ticket reconciliation report data from the backend
 */
export interface TicketReconciliationReportItemProps {
    machine: MachineProfileListItem;
    metrics: TicketReconciliationReportMetrics;
}

export class TicketReconciliationReportItem implements TicketReconciliationReportItemProps {
    @Type(() => MachineProfileListItem)
    @ValidateNested()
    machine!: MachineProfileListItem;

    @Type(() => TicketReconciliationReportMetrics)
    @ValidateNested()
    metrics!: TicketReconciliationReportMetrics;

    static map(props: TicketReconciliationReportItemProps): TicketReconciliationReportItem {
        return deserialize(TicketReconciliationReportItem, props);
    }

    static extractFromFullReport({ machine, metrics: allMetrics }: MachineWinReconciliationReportItem): TicketReconciliationReportItem {
        return this.map({
            machine,
            metrics: TicketReconciliationReportMetrics.extractFromAllMetrics(allMetrics)
        });
    }

    static mapList(propsList: TicketReconciliationReportItemProps[]): TicketReconciliationReportItem[] {
        return propsList.map((props) => TicketReconciliationReportItem.map(props));
    }
}
