import { WinReconciliationDailyRequest, WinReconciliationDailyRequestProps } from '@cms/machine-metrics';
import { deserialize } from '@cms/serialization';

export class TicketReconciliationReportRequest extends WinReconciliationDailyRequest {
    static override create(props: WinReconciliationDailyRequestProps): TicketReconciliationReportRequest {
        return deserialize(TicketReconciliationReportRequest, props);
    }
}
