{"name": "@cms/tickets", "version": "0.0.1", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"reflect-metadata": "^0.2.1", "uuid": "^10.0.0"}, "peerDependencies": {"@cms/machine-metrics": "file:../../machine-metrics/shared", "@cms/serialization": "file:../../shared/serialization", "@cms/validation": "file:../../shared/validation", "@tronius/shared-common": "file:../../shared/common", "@tronius/shared-domain": "file:../../shared/domain", "class-transformer": "^0.5.1", "class-validator": "^0.14.1"}}