{"name": "@cms/tickets-domain", "version": "0.0.1", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"@nestjs/testing": "^10.3.3", "reflect-metadata": "^0.2.1"}, "peerDependencies": {"@cms/casino-operator": "file:../../casino-operator/shared", "@cms/cms-cookies": "file:../../../libs/backend/cms-cookies", "@cms/customers": "file:../../../feature/customers/shared", "@cms/entity-audit": "file:../../entity-audit/shared", "@cms/entity-audit-domain": "file:../../entity-audit/domain", "@cms/machine-metrics": "file:../../machine-metrics/shared", "@cms/machine-sessions": "file:../../machine-sessions/shared", "@cms/message-broker": "file:../../../libs/backend/message-broker", "@cms/server-authentication": "file:../../../libs/backend/cms-server-authentication", "@cms/server-permissions": "file:../../../libs/backend/server-permissions", "@cms/tickets": "file:../shared", "@cms/typeorm": "file:../../typeorm", "@nestjs/common": "^10.3.3", "@nestjs/schedule": "^4.0.2", "@nestjs/swagger": "^7.3.0", "@tronius/backend-common": "file:../../../libs/backend/common", "@tronius/backend-database": "file:../../../libs/backend/database", "@tronius/shared-common": "file:../../../libs/shared/common", "@tronius/shared-domain": "file:../../../libs/shared/domain", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cron": "3.1.7", "date-fns": "^3.3.1", "lodash": "^4.17.21", "randomstring": "^1.3.0", "typeorm": "0.3.17"}}