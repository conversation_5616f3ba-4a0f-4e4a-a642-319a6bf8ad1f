import { WorkstationTransactionPoint, WorkstationTransactionPointPayload } from '@cms/cms-cookies';
import { ListEntityAuditQuery, ListEntityAuditQueryResult, ListEntityAuditResponse } from '@cms/entity-audit';
import { MessageBroker } from '@cms/message-broker';
import { CmsIdentity, Identity } from '@cms/server-authentication';
import { CmsPermissions } from '@cms/server-permissions';
import { TgTicket, TicketReconciliationReportItem } from '@cms/tickets';
import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common';
import { ApiOAuth2, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { TgPaginationRequestDto } from '@tronius/backend-common';
import { TgPaginationResponse, TriggerType } from '@tronius/shared-common';
import { CmsAction } from '@tronius/shared-domain';

import {
    CreateTicketRequestDto,
    RedeemManualTicketDto,
    RedeemTicketDto as RedeemTicketRequestDto,
    ReplaceTicketDto,
    TicketReconciliationReportRequestDto,
    VoidTicketDto
} from './dto';
import { TicketReconciliationReportService } from './ticket-reconciliation-report.service';
import { TicketService } from './ticket.service';

@ApiTags('Tickets')
@ApiOAuth2([])
@Controller('payment/ticket')
export class TicketController {
    constructor(
        private readonly service: TicketService,
        private readonly ticketReconciliationReportService: TicketReconciliationReportService,
        private readonly messageBroker: MessageBroker
    ) {}

    @Get()
    @CmsPermissions(CmsAction.TicketList)
    async list(@Query() query: TgPaginationRequestDto<TgTicket>): Promise<TgPaginationResponse<TgTicket>> {
        return this.service.list(query);
    }

    @Get('/audit')
    @CmsPermissions(CmsAction.TicketList)
    @ApiQuery({ name: 'next', type: String, required: false })
    @ApiQuery({ name: 'count', type: Number, required: false })
    @ApiQuery({ name: 'entityId', type: String, required: false })
    @ApiQuery({ name: 'actor', type: String, required: false })
    async listAudit(
        @Query('next') next?: string,
        @Query('count') count?: string,
        @Query('entityId') entityId?: string,
        @Query('actor') actor?: string
    ): Promise<ListEntityAuditResponse<TgTicket>> {
        const { result: response } = await this.messageBroker.query(
            ListEntityAuditQuery.create({
                request: {
                    entityName: 'Ticket',
                    next,
                    count: count as unknown as number,
                    entityId,
                    actor
                }
            }),
            ListEntityAuditQueryResult<TgTicket>
        );

        return response;
    }

    @Get('/ticket-number/:ticketNumber')
    @CmsPermissions(CmsAction.TicketRedeem)
    @ApiParam({ name: 'ticketNumber', type: String })
    async getByTicketNumber(@Param('ticketNumber') ticketNumber: string): Promise<TgTicket | null> {
        return this.service.getByTicketNumber(ticketNumber);
    }

    @Post('/create')
    @CmsPermissions([CmsAction.TicketCreateCash, CmsAction.TicketCreatePromo])
    async createTicket(
        @WorkstationTransactionPoint() workstationPayload: WorkstationTransactionPointPayload,
        @Body() body: CreateTicketRequestDto,
        @Identity() { username }: CmsIdentity
    ): Promise<TgTicket> {
        const result = await this.service.create({
            amount: body.ticket.amount,
            type: body.ticket.type,
            triggeredByType: TriggerType.TransactionPoint,
            triggeredById: workstationPayload.transactionPointId,
            username,
            printerId: body.printerId
        });

        return result.ticket;
    }

    @Put('/redeem')
    @CmsPermissions(CmsAction.TicketRedeem)
    async redeemTicket(
        @WorkstationTransactionPoint() workstationPayload: WorkstationTransactionPointPayload,
        @Body() body: RedeemTicketRequestDto,
        @Identity() { username }: CmsIdentity
    ): Promise<TgTicket> {
        const { ticketNumber } = body;
        const { transactionPointId } = workstationPayload;
        return this.service.redeemInTransactionPoint({ ticketNumber, transactionPointId, username });
    }

    @Put('/redeem/manual')
    @CmsPermissions(CmsAction.TicketRedeemManual)
    async redeemManualTicket(
        @WorkstationTransactionPoint() workstationPayload: WorkstationTransactionPointPayload,
        @Body() body: RedeemManualTicketDto,
        @Identity() { username }: CmsIdentity
    ): Promise<void> {
        const { id: ticketId } = body;
        const { transactionPointId } = workstationPayload;
        return this.service.redeemManualInTransactionPoint({ ticketId, transactionPointId, username });
    }

    @Put('/void')
    @CmsPermissions(CmsAction.TicketVoid)
    async voidTicket(
        @WorkstationTransactionPoint() workstationPayload: WorkstationTransactionPointPayload,
        @Body() body: VoidTicketDto,
        @Identity() { username }: CmsIdentity
    ): Promise<void> {
        return this.service.void(body.id, username, workstationPayload.transactionPointId);
    }

    @Put('/replace')
    @CmsPermissions(CmsAction.TicketReplace)
    async replaceTicket(
        @WorkstationTransactionPoint() workstationPayload: WorkstationTransactionPointPayload,
        @Body() body: ReplaceTicketDto,
        @Identity() { username }: CmsIdentity
    ): Promise<TgTicket> {
        return this.service.replace(body.id, body.printerId, body.comment, username, workstationPayload.transactionPointId);
    }

    @Get('/reconciliation-report')
    @CmsPermissions(CmsAction.TicketList)
    async getTicketReconciliationReport(@Query() request: TicketReconciliationReportRequestDto): Promise<TicketReconciliationReportItem[]> {
        return this.ticketReconciliationReportService.getTicketReconciliationReport({ ...request });
    }
}
