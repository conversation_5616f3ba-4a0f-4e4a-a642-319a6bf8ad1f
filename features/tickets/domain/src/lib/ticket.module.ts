import { AuditEventProps, EntityAuditModule } from '@cms/entity-audit-domain';
import { TgTicket, TicketCreatedEvent, TicketStatusUpdatedEvent } from '@cms/tickets';
import { TypeOrmModule } from '@cms/typeorm';
import { Module } from '@nestjs/common';
import { TicketEntity, TicketOperationEntity } from '@tronius/backend-database';

import { TicketOperationDao } from './dao/ticket-operation.dao';
import { TicketDao } from './dao/ticket.dao';
import {
    CanCreateTicketQueryHandler,
    CanRedeemTicket<PERSON>ueryHandler,
    CreateTicketCommandHandler,
    GetTicketsMetricsForGamingDayQueryHandler,
    GetTicketsMetricsForPeriodQueryHandler,
    LockTicketCommandHandler,
    MarkTicketAsPrintedOnTicketPrintedByMachineHand<PERSON>,
    MarkTicketAsPrintedOnTicket<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    MarkTicketAsRedeemedOnTicketRedeemedByMachineHandler,
    MarkTicketAsRejectedOnTicketRejectedByMachineHandler,
    RedeemTicketCommandHandler,
    UpdateCustomerOnCustomerAssignedToMachineSessionHandler
} from './handlers';
import { MachineSessionProvider } from './machine-session.provider';
import { ticketAuditEntityTransformer } from './ticket-audit-entity.transformer';
import { TicketOperationController } from './ticket-operation.controller';
import { TicketOperationService } from './ticket-operation.service';
import { TicketReconciliationReportService } from './ticket-reconciliation-report.service';
import { TicketWorkerService } from './ticket-worker.service';
import { TicketController } from './ticket.controller';
import { TicketService } from './ticket.service';

@Module({
    imports: [
        TypeOrmModule.forFeature([TicketEntity, TicketOperationEntity]),
        EntityAuditModule.forEntity<TgTicket>({
            entity: 'Ticket',
            auditEvents: [
                AuditEventProps.create<TicketCreatedEvent, TgTicket>({
                    event: TicketCreatedEvent,
                    entityIdentifier: (event) => event.ticket.id,
                    actorIdentifier: (event) => event.username,
                    entityPayloadIdentifier: (event) => event.ticket,
                    transformer: ticketAuditEntityTransformer
                }),
                AuditEventProps.create<TicketStatusUpdatedEvent, TgTicket>({
                    event: TicketStatusUpdatedEvent,
                    entityIdentifier: (event) => event.ticket.id,
                    actorIdentifier: (event) => event.username,
                    entityPayloadIdentifier: (event) => event.ticket,
                    transformer: ticketAuditEntityTransformer
                })
            ]
        })
    ],
    controllers: [TicketController, TicketOperationController],
    providers: [
        TicketService,
        TicketDao,
        TicketOperationService,
        TicketReconciliationReportService,
        TicketOperationDao,
        TicketWorkerService,
        MachineSessionProvider,
        CanCreateTicketQueryHandler,
        CanRedeemTicketQueryHandler,
        CreateTicketCommandHandler,
        LockTicketCommandHandler,
        MarkTicketAsPrintedOnTicketPrintedByMachineHandler,
        MarkTicketAsPrintedOnTicketPrintedHandler,
        MarkTicketAsRedeemedOnTicketRedeemedByMachineHandler,
        MarkTicketAsRejectedOnTicketRejectedByMachineHandler,
        RedeemTicketCommandHandler,
        UpdateCustomerOnCustomerAssignedToMachineSessionHandler,
        GetTicketsMetricsForGamingDayQueryHandler,
        GetTicketsMetricsForPeriodQueryHandler
    ]
})
export class TicketModule {}
