import { MessageBroker } from '@cms/message-broker';
import { TicketReconciliationReportItem } from '@cms/tickets';
import { Test, TestingModule } from '@nestjs/testing';

import { TicketReconciliationReportService } from './ticket-reconciliation-report.service';

describe('TicketReconciliationReportService', () => {
    let service: TicketReconciliationReportService;

    const mockMessageBroker = {
        publish: jest.fn(),
        subscribe: jest.fn(),
        unsubscribe: jest.fn(),
        registerQueryHandler: jest.fn(),
        query: jest.fn(),
        unsubscribeQueryHandler: jest.fn(),
        command: jest.fn(),
        registerCommandHandler: jest.fn(),
        unsubscribeCommandHandler: jest.fn()
    } as Partial<MessageBroker> as MessageBroker;

    beforeEach(async () => {
        jest.clearAllMocks();

        (mockMessageBroker.query as jest.Mock).mockResolvedValue({ isValid: true });

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                TicketReconciliationReportService,
                {
                    provide: MessageBroker,
                    useValue: mockMessageBroker
                }
            ]
        }).compile();

        service = module.get<TicketReconciliationReportService>(TicketReconciliationReportService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    it('should sort report items', () => {
        const item1 = { metrics: { variance: 10 }, machine: { location: '1' } } as TicketReconciliationReportItem;
        const item2 = { metrics: { variance: null }, machine: { location: '2' } } as TicketReconciliationReportItem;
        const item3 = { metrics: { variance: -5 }, machine: { location: '3' } } as TicketReconciliationReportItem;
        const item4 = { metrics: { variance: 0 }, machine: { location: '4' } } as TicketReconciliationReportItem;
        const item5 = { metrics: { variance: 0 }, machine: { location: '5' } } as TicketReconciliationReportItem;
        const reportItems: TicketReconciliationReportItem[] = [item1, item2, item3, item4, item5];

        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-call, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        const sortedReportItems = (service as any).sortReportItems(reportItems) as TicketReconciliationReportItem[];

        expect(sortedReportItems[0]).toEqual(item1);
        expect(sortedReportItems[1]).toEqual(item3);
        expect(sortedReportItems[2]).toEqual(item4);
        expect(sortedReportItems[3]).toEqual(item5);
        expect(sortedReportItems[4]).toEqual(item2);
    });
});
