import { WinReconciliationReportType } from '@cms/machine-metrics';
import { TicketReconciliationReportRequest } from '@cms/tickets';
import { ApiProperty } from '@nestjs/swagger';

export class TicketReconciliationReportRequestDto extends TicketReconciliationReportRequest {
    @ApiProperty({
        description: 'Report starting gaming day',
        example: '2025-01-20',
        required: true,
        type: String
    })
    override readonly fromGamingDay!: string;

    @ApiProperty({
        description: 'Report Gaming Day',
        example: '2025-01-30',
        required: true,
        type: String
    })
    override readonly toGamingDay!: string;

    @ApiProperty({
        description: 'Report Type',
        example: '2025-01-30',
        required: true,
        enum: WinReconciliationReportType
    })
    override readonly type!: WinReconciliationReportType;
}
