import { GetWinReconciliationDailyReportQuery, GetWinReconciliationDailyReportQueryResult } from '@cms/machine-metrics';
import { MessageBroker } from '@cms/message-broker';
import { TicketReconciliationReportItem, TicketReconciliationReportRequest } from '@cms/tickets';
import { Injectable } from '@nestjs/common';
import { orderBy } from 'lodash';

@Injectable()
export class TicketReconciliationReportService {
    constructor(private readonly messageBroker: MessageBroker) {}

    async getTicketReconciliationReport(request: TicketReconciliationReportRequest): Promise<TicketReconciliationReportItem[]> {
        const { reportItems } = await this.messageBroker.query(
            GetWinReconciliationDailyReportQuery.create({ ...request }),
            GetWinReconciliationDailyReportQueryResult
        );

        const ticketReportItems = reportItems.map((reportItem) => TicketReconciliationReportItem.extractFromFullReport(reportItem));

        return this.sortReportItems(ticketReportItems);
    }

    private sortReportItems(reportItems: TicketReconciliationReportItem[]): TicketReconciliationReportItem[] {
        // First, create a function to determine if variance is null (for sorting nulls last)
        const hasVariance = (item: TicketReconciliationReportItem): number => {
            return item.metrics.variance === null ? 0 : 1;
        };

        // Function to get absolute variance value (for sorting by magnitude)
        const absVariance = (item: TicketReconciliationReportItem): number => {
            const { variance } = item.metrics;
            return variance === null ? 0 : Math.abs(variance);
        };

        // Function to get location for final sorting
        const location = (item: TicketReconciliationReportItem): string => {
            return item.machine.location;
        };

        // Use orderBy with multiple sorting criteria
        return orderBy(reportItems, [hasVariance, absVariance, location], ['desc', 'desc', 'asc']);
    }
}
