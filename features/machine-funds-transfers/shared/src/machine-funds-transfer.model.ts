import { deserialize } from '@cms/serialization';
import { BaseModel } from '@tronius/shared-common';
import { IsN<PERSON>ber, Min } from 'class-validator';

import { MachineFundsTransferOperation } from './machine-funds-transfer-operation.model';

export enum MachineFundsTransferType {
    /**
     * @description Aft bonus transfer. Normal happens when a minor jackpot amount is won.
     */
    AftBonusTransfer = 'AftBonusTransfer',
    /**
     * @description Aft bonus handpay. Normal happens when a major jackpot amount is won.
     */
    AftBonusHandpay = 'AftBonusHandpay',
    /**
     * @description Manual transfer in. Manually transfer funds to the machine from system.
     * It was meant for Belgrade use-case, where they are transferring money in Cage to Machine directly.
     */
    ManualTransferIn = 'ManualTransferIn',
    /**
     * @description Manual transfer out. Manually transfer funds from the machine to system.
     * It was meant for Belgrade use-case, where they are transferring money in Cage from Machine directly.
     */
    ManualTransferOut = 'ManualTransferOut',
    /**
     * @description Wallet transfer to machine. Transfer funds from customers wallet to machine.
     */
    WalletTransferToMachine = 'WalletTransferToMachine',
    /**
     * @description Wallet transfer from machine. Transfer funds from machine to customers wallet.
     */
    WalletTransferFromMachine = 'WalletTransferFromMachine'
}

export enum MachineFundsTransferStatus {
    Pending = 'Pending',
    Failed = 'Failed',
    Completed = 'Completed',
    Cancelled = 'Cancelled',
    Abandoned = 'Abandoned'
}

export interface MachineFundsTransfer extends BaseModel {
    machineId: string;
    type: MachineFundsTransferType;
    /**
     * @description Amount that can be cashed out using a ticket
     */
    cashableAmount: number;
    /**
     * @description Promo amount that cannot be cashed out using a ticket
     */
    restrictedAmount: number;
    /**
     * @deprecated Currently unused
     * @description Promo amount that can be cashed out using a ticket
     */
    unrestrictedAmount: number;
    /**
     * @description Total amount of funds transferred (cashableAmount + restrictedAmount + unrestrictedAmount)
     */
    amount: number;
    status: MachineFundsTransferStatus;
    offerId?: string;
    operations?: MachineFundsTransferOperation[];
    customerId?: string;
    cashWalletOperationReservationId?: string;
}

export type CreateMachineFundsTransfer = Pick<
    MachineFundsTransfer,
    | 'cashableAmount'
    | 'cashWalletOperationReservationId'
    | 'customerId'
    | 'machineId'
    | 'offerId'
    | 'restrictedAmount'
    | 'type'
    | 'unrestrictedAmount'
>;

export type CreateManualFundsTransfer = Pick<
    CreateMachineFundsTransfer,
    'cashableAmount' | 'cashWalletOperationReservationId' | 'customerId' | 'machineId' | 'restrictedAmount' | 'type' | 'unrestrictedAmount'
>;

export type CreateManualFundsTransferIn = Omit<CreateManualFundsTransfer, 'type'>;

export interface CancelMachineFundsTransfer {
    id: string;
}

export interface CompleteMachineFundsTransfer {
    id: string;
    gamingDay: string;
    comment?: string;
    accountingDate: Date;
}

export interface MachineFundsTransferMetricsProps {
    cashableAmountToMachine: number;
    cashableAmountFromMachine: number;
    restrictedAmountToMachine: number;
    restrictedAmountFromMachine: number;
    cashableAmountToMachineAdjustment: number;
    cashableAmountFromMachineAdjustment: number;
    restrictedAmountToMachineAdjustment: number;
    restrictedAmountFromMachineAdjustment: number;
}

export abstract class MachineFundsTransferMetrics implements MachineFundsTransferMetricsProps {
    @IsNumber()
    @Min(0)
    readonly cashableAmountToMachine!: number;

    @IsNumber()
    @Min(0)
    readonly cashableAmountFromMachine!: number;

    @IsNumber()
    @Min(0)
    readonly restrictedAmountToMachine!: number;

    @IsNumber()
    @Min(0)
    readonly restrictedAmountFromMachine!: number;

    @IsNumber()
    @Min(0)
    readonly cashableAmountToMachineAdjustment!: number;

    @IsNumber()
    @Min(0)
    readonly cashableAmountFromMachineAdjustment!: number;

    @IsNumber()
    @Min(0)
    readonly restrictedAmountToMachineAdjustment!: number;

    @IsNumber()
    @Min(0)
    readonly restrictedAmountFromMachineAdjustment!: number;

    static create(props: MachineFundsTransferMetricsProps): MachineFundsTransferMetrics {
        return deserialize(MachineFundsTransferMetrics, props);
    }
}
