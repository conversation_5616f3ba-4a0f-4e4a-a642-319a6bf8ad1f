import { TriggerType } from '@tronius/shared-common';

import { MachineFundsTransfer } from './machine-funds-transfer.model';

export enum MachineFundsTransferOperationType {
    Created = 'Created',
    Failed = 'Failed',
    Completed = 'Completed',
    /**
     * Given that a Funds Transfer In was requested and we did not receive confirmation from the machine, the Funds Transfer In will be left in a pending state.
     * As a machine Machine Supervisor i am able to manually mark the Funds Transfer In as completed. CMSDEV-3273
     */
    CompletedManual = 'CompletedManual',
    Cancelled = 'Cancelled',
    Abandoned = 'Abandoned'
}

export interface MachineFundsTransferOperation {
    id: string;
    type: MachineFundsTransferOperationType;
    triggeredByType: TriggerType;
    triggeredById: string;
    /**
     * @description Accounting date when the operation was triggered (machine, transaction point)
     */
    triggeredAt: number;
    /**
     * @description Accounting Gaming day
     */
    gamingDay: string;
    comment?: string;
    /**
     * @description Date when the operation was created
     */
    createdAt: number;
    machineFundsTransferId: string;
    machineFundsTransfer?: MachineFundsTransfer;
}
