import { deserialize } from '@cms/serialization';
import { IsGamingDay, IsNotEmptyString, IsPlatformComponentUsername } from '@cms/validation';
import { BaseCommand } from '@tronius/shared-common';
import { Type } from 'class-transformer';
import { IsDate, IsOptional, IsUUID } from 'class-validator';

import { CompleteMachineFundsTransfer } from '../machine-funds-transfer.model';

export interface ManuallyCompleteMachineFundsTransferCommandProps extends CompleteMachineFundsTransfer {
    username: string;
}

export class ManuallyCompleteMachineFundsTransferCommand extends BaseCommand implements ManuallyCompleteMachineFundsTransferCommandProps {
    @IsUUID()
    readonly id!: string;

    @IsGamingDay()
    readonly gamingDay!: string;

    @IsOptional()
    @IsNotEmptyString()
    readonly comment?: string;

    @Type(() => Date)
    @IsDate()
    readonly accountingDate!: Date;

    @IsPlatformComponentUsername()
    readonly username!: string;

    protected constructor() {
        super();
    }

    static create(props: ManuallyCompleteMachineFundsTransferCommandProps): ManuallyCompleteMachineFundsTransferCommand {
        return deserialize(ManuallyCompleteMachineFundsTransferCommand, props);
    }
}
