import {
    MachineFundsTransfer,
    MachineFundsTransferCanceledEvent,
    MachineFundsTransferStatus,
    MachineFundsTransferType
} from '@cms/machine-funds-transfers';
import { Test, TestingModule } from '@nestjs/testing';
import { UUIDFactory } from '@tronius/shared-common';

import { ReleaseFundsOnCanceledMachineFundsTransferHandler } from './release-funds-on-canceled-machine-funds-transfer.handler';
import { CardedMachineSessionWalletTransferService } from '../services';

describe('ReleaseFundsOnCanceledMachineFundsTransferHandler', () => {
    let handler: ReleaseFundsOnCanceledMachineFundsTransferHandler;
    let mockService: Partial<CardedMachineSessionWalletTransferService>;

    beforeEach(async () => {
        mockService = {
            releaseReservedFunds: jest.fn()
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                ReleaseFundsOnCanceledMachineFundsTransferHandler,
                { provide: CardedMachineSessionWalletTransferService, useValue: mockService }
            ]
        }).compile();

        handler = module.get<ReleaseFundsOnCanceledMachineFundsTransferHandler>(ReleaseFundsOnCanceledMachineFundsTransferHandler);
    });

    it('should be defined', () => {
        expect(handler).toBeDefined();
    });

    it('should call releaseReservedFunds when handling MachineFundsTransferCanceledEvent', async () => {
        const machineFundsTransfer: MachineFundsTransfer = {
            id: UUIDFactory.create(),
            machineId: UUIDFactory.create(),
            type: MachineFundsTransferType.WalletTransferToMachine,
            cashableAmount: 100,
            restrictedAmount: 0,
            unrestrictedAmount: 0,
            amount: 100,
            cashWalletOperationReservationId: UUIDFactory.create(),
            customerId: UUIDFactory.create(),
            createdAt: Date.now(),
            updatedAt: Date.now(),
            operations: [],
            status: MachineFundsTransferStatus.Cancelled
        };

        const event = MachineFundsTransferCanceledEvent.create({
            machineFundsTransfer,
            username: 'test-user',
            comment: 'Test cancellation'
        });

        await handler.handle(event);

        expect(mockService.releaseReservedFunds).toHaveBeenCalledWith(machineFundsTransfer);
    });

    it('should handle events without comment', async () => {
        const machineFundsTransfer: MachineFundsTransfer = {
            id: UUIDFactory.create(),
            machineId: UUIDFactory.create(),
            type: MachineFundsTransferType.WalletTransferToMachine,
            cashableAmount: 100,
            restrictedAmount: 0,
            unrestrictedAmount: 0,
            amount: 100,
            cashWalletOperationReservationId: UUIDFactory.create(),
            customerId: UUIDFactory.create(),
            createdAt: Date.now(),
            updatedAt: Date.now(),
            operations: [],
            status: MachineFundsTransferStatus.Cancelled
        };

        const event = MachineFundsTransferCanceledEvent.create({
            machineFundsTransfer,
            username: 'test-user'
        });

        await handler.handle(event);

        expect(mockService.releaseReservedFunds).toHaveBeenCalledWith(machineFundsTransfer);
    });
});
