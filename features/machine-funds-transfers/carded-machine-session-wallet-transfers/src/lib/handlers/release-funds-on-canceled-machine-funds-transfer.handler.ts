import { MachineFundsTransferCanceledEvent } from '@cms/machine-funds-transfers';
import { EventHandler, OnEvent } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import { ErrorUtils } from '@tronius/shared-common';

import { CardedMachineSessionWalletTransferService } from '../services';

@Injectable()
@OnEvent(MachineFundsTransferCanceledEvent)
export class ReleaseFundsOnCanceledMachineFundsTransferHandler implements EventHandler<MachineFundsTransferCanceledEvent> {
    private readonly logger = new Logger(ReleaseFundsOnCanceledMachineFundsTransferHandler.name);

    constructor(private readonly cardedMachineSessionWalletTransferService: CardedMachineSessionWalletTransferService) {}

    async handle({ machineFundsTransfer }: MachineFundsTransferCanceledEvent): Promise<void> {
        await this.cardedMachineSessionWalletTransferService.releaseReservedFunds(machineFundsTransfer);
    }

    onError(error: unknown): void {
        this.logger.error(ErrorUtils.errorToString(error));
    }
}
