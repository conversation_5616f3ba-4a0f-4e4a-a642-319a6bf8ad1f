import { Module } from '@nestjs/common';

import {
    InitiateMachineFundTransferOnMachineSessionStartHandler,
    InitiateMachineFundTransferOnMachineSessionUpgradeHandler,
    ProcessWalletTransactionOnMftCompletionHandler,
    ReleaseFundsOnCanceledMachineFundsTransferHandler,
    ReleaseFundsOnFailedMachineFundsTransferHandler
} from './handlers';
import {
    ActiveCustomerProvider,
    CardedMachineSessionWalletTransferService,
    CurrencyProvider,
    CustomerWalletProvider,
    EventLogService,
    FundReservationService,
    MachineFundsTransferService,
    WalletDepositService,
    WalletWithdrawalService
} from './services';

@Module({
    controllers: [],
    providers: [
        CardedMachineSessionWalletTransferService,
        CustomerWalletProvider,
        ActiveCustomerProvider,
        CurrencyProvider,
        FundReservationService,
        InitiateMachineFundTransferOnMachineSessionStartHandler,
        MachineFundsTransferService,
        WalletWithdrawalService,
        ReleaseFundsOnFailedMachineFundsTransferHandler,
        ReleaseFundsOnCanceledMachineFundsTransferHandler,
        ProcessWalletTransactionOnMftCompletionHandler,
        InitiateMachineFundTransferOnMachineSessionUpgradeHandler,
        WalletDepositService,
        EventLogService
    ],
    exports: []
})
export class CardedMachineSessionWalletTransfersModule {}
