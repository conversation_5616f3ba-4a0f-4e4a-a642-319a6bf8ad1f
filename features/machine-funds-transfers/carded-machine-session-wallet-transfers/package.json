{"name": "@cms/carded-machine-session-wallet-transfers", "version": "0.0.1", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"@nestjs/testing": "^10.3.3", "reflect-metadata": "^0.2.1"}, "peerDependencies": {"@cms/casino-operator": "file:../../casino-operator", "@cms/customers": "file:../../customers", "@cms/execution-context": "file:../../../libs/backend/infrastructure/execution-context", "@cms/machine-funds-transfers": "file:../../machine-funds-transfers", "@cms/machine-sessions": "file:../../machine-sessions", "@cms/message-broker": "file:../../../libs/backend/message-broker", "@cms/wallets": "file:../../wallets", "@nestjs/common": "^10.3.3", "@tronius/shared-common": "file:../../../libs/shared/common", "@tronius/shared-domain": "file:../../../libs/shared/domain"}}