import { CardedMachineSessionWalletTransfersModule } from '@cms/carded-machine-session-wallet-transfers';
import { CasinoSettings, GetCasinoSettingsQuery, GetCasinoSettingsQueryResult } from '@cms/casino-operator';
import { CustomerProfile, CustomerStatus, GetCustomerByIdQuery, GetCustomerByIdQueryResult, Sex } from '@cms/customers';
import { InMemoryMessageBrokerModule } from '@cms/in-memory-message-broker';
import {
    CreateMachineFundsTransferCommand,
    CreateMachineFundsTransferCommandResult,
    MachineFundsTransfer,
    MachineFundsTransferCanceledEvent,
    MachineFundsTransferCompletedEvent,
    MachineFundsTransferFailedEvent,
    MachineFundsTransferType
} from '@cms/machine-funds-transfers';
import {
    MachineSession,
    MachineSessionProps,
    MachineSessionStartTriggerEvent,
    MachineSessionStartedEvent,
    MachineSessionStatus,
    MachineSessionType,
    MachineSessionUpgradedEvent
} from '@cms/machine-sessions';
import { MessageBroker, MessageBrokerModule } from '@cms/message-broker';
import { deserialize } from '@cms/serialization';
import {
    CashWallet,
    GetCustomersCashWalletQuery,
    GetCustomersCashWalletQueryResult,
    ProcessCashWalletOperationCommand,
    ReleaseWalletFundsCommand,
    ReserveCashWalletFundsCommand,
    ReserveWalletFundsCommandResult,
    WalletOperationType
} from '@cms/wallets';
import { Test, TestingModule } from '@nestjs/testing';
import { CommandResult, Currency, ProfitCenterType, UUIDFactory } from '@tronius/shared-common';
import { CreateEventLogCommand } from '@tronius/shared-domain';

describe('CardedMachineSessionWalletTransfers', () => {
    let module: TestingModule;
    let messageBroker: MessageBroker;

    const customerId = UUIDFactory.create();
    const machineId = UUIDFactory.create();
    const sessionId = UUIDFactory.create();
    const currency = Currency.ARS;
    const reservationId = UUIDFactory.create();
    const username = 'test-user';

    const machineSession = deserialize(MachineSession, {
        customerId,
        gamingDay: '2021-01-01',
        id: sessionId,
        machineId,
        startedAt: new Date(),
        startTriggerEvent: MachineSessionStartTriggerEvent.Card,
        startMeterCollection: new Date(),
        status: MachineSessionStatus.Active,
        type: MachineSessionType.Carded,
        startMeters: null,
        endMeters: null,
        endTriggerEvent: null,
        metrics: null
    } as unknown as MachineSessionProps);

    const cashWallet = CashWallet.map({
        id: UUIDFactory.create(),
        balance: 1000,
        currency,
        ownerId: customerId,
        openedAt: new Date(),
        reservedFunds: 0
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    beforeAll(async () => {
        const testingModule = await Test.createTestingModule({
            imports: [
                MessageBrokerModule.forRootAsync({
                    imports: [InMemoryMessageBrokerModule.forRoot({ awaitHandlerExecution: true, maxListenersPerEvent: 100 })]
                }),

                CardedMachineSessionWalletTransfersModule
            ]
        }).compile();

        messageBroker = testingModule.get(MessageBroker);
        module = await testingModule.init();

        await Promise.all([
            messageBroker.registerQueryHandler(GetCustomerByIdQuery, GetCustomerByIdQueryResult, ({ correlationId }) => {
                return GetCustomerByIdQueryResult.create({
                    correlationId,
                    customer: CustomerProfile.create({
                        id: customerId,
                        status: CustomerStatus.Active,
                        dateOfBirth: new Date(),
                        firstName: 'John',
                        lastName: 'Doe',
                        nationalityId: 'UK',
                        sex: Sex.Male
                    })
                });
            }),

            messageBroker.registerCommandHandler(CreateEventLogCommand, CommandResult, ({ correlationId }) => {
                return CommandResult.create({ correlationId });
            }),

            messageBroker.registerQueryHandler(GetCasinoSettingsQuery, GetCasinoSettingsQueryResult, ({ correlationId }) => {
                return GetCasinoSettingsQueryResult.create({
                    correlationId,
                    settings: {
                        currency
                    } as Partial<CasinoSettings> as CasinoSettings
                });
            }),

            messageBroker.registerQueryHandler(GetCustomersCashWalletQuery, GetCustomersCashWalletQueryResult, ({ correlationId }) => {
                return GetCustomersCashWalletQueryResult.create({ correlationId, wallet: cashWallet });
            }),

            messageBroker.registerCommandHandler(ReserveCashWalletFundsCommand, ReserveWalletFundsCommandResult, ({ correlationId }) => {
                return ReserveWalletFundsCommandResult.create({
                    correlationId,
                    reservationId
                });
            }),

            messageBroker.registerCommandHandler(
                CreateMachineFundsTransferCommand,
                CreateMachineFundsTransferCommandResult,
                ({ correlationId, cashableAmount, machineId: mftMachineId }) => {
                    return CreateMachineFundsTransferCommandResult.create({
                        mft: {
                            amount: cashableAmount,
                            machineId: mftMachineId
                        } as Partial<MachineFundsTransfer> as MachineFundsTransfer,
                        correlationId
                    });
                }
            ),

            messageBroker.registerCommandHandler(ProcessCashWalletOperationCommand, CommandResult, ({ correlationId }) => {
                return CommandResult.create({ correlationId });
            }),

            messageBroker.registerCommandHandler(ReleaseWalletFundsCommand, CommandResult, ({ correlationId }) => {
                return CommandResult.create({ correlationId });
            })
        ]);
    });

    it('initiates a machine funds transfer from a cash wallet', async () => {
        const commandSpy = jest.spyOn(messageBroker, 'command');
        await messageBroker.publish(MachineSessionStartedEvent, MachineSessionStartedEvent.create({ session: machineSession }));

        expect(commandSpy).toHaveBeenLastCalledWith(
            expect.objectContaining({
                cashableAmount: cashWallet.balance,
                machineId,
                customerId,
                cashWalletOperationReservationId: reservationId,
                type: MachineFundsTransferType.WalletTransferToMachine,
                unrestrictedAmount: 0
            } as Partial<CreateMachineFundsTransferCommand>),
            CreateMachineFundsTransferCommandResult
        );
    });

    it('initiates a machine funds transfer on machine session upgrade', async () => {
        const commandSpy = jest.spyOn(messageBroker, 'command');
        await messageBroker.publish(
            MachineSessionUpgradedEvent,
            MachineSessionUpgradedEvent.create({
                session: machineSession,
                oldCustomerId: null,
                oldType: MachineSessionType.UnknownCustomer
            })
        );

        expect(commandSpy).toHaveBeenLastCalledWith(
            expect.objectContaining({
                cashableAmount: cashWallet.balance,
                machineId,
                customerId,
                cashWalletOperationReservationId: reservationId,
                type: MachineFundsTransferType.WalletTransferToMachine,
                unrestrictedAmount: 0
            } as Partial<CreateMachineFundsTransferCommand>),
            CreateMachineFundsTransferCommandResult
        );
    });

    it('processes a wallet transaction on machine funds transfer completion', async () => {
        const commandSpy = jest.spyOn(messageBroker, 'command');

        const usedAmount = 100;

        await messageBroker.publish(
            MachineFundsTransferCompletedEvent,
            MachineFundsTransferCompletedEvent.create({
                username,
                machineFundsTransfer: {
                    cashableAmount: usedAmount,
                    cashWalletOperationReservationId: reservationId,
                    customerId,
                    id: UUIDFactory.create(),
                    machineId,
                    type: MachineFundsTransferType.WalletTransferToMachine
                } as Partial<MachineFundsTransfer> as MachineFundsTransfer
            })
        );

        expect(commandSpy).toHaveBeenLastCalledWith(
            expect.objectContaining({
                reservationId,
                currency,
                ownerId: customerId,
                amount: usedAmount,
                operationType: WalletOperationType.Withdraw,
                profitCenterType: ProfitCenterType.Machine,
                profitCenterId: machineId
            } as Partial<ProcessCashWalletOperationCommand> as ProcessCashWalletOperationCommand)
        );
    });

    it('releases reserved funds on machine funds transfer failure', async () => {
        const commandSpy = jest.spyOn(messageBroker, 'command');

        await messageBroker.publish(
            MachineFundsTransferFailedEvent,
            MachineFundsTransferFailedEvent.create({
                username,
                machineFundsTransfer: {
                    machineId,
                    customerId,
                    cashWalletOperationReservationId: reservationId,
                    type: MachineFundsTransferType.WalletTransferToMachine
                } as Partial<MachineFundsTransfer> as MachineFundsTransfer
            })
        );

        expect(commandSpy).toHaveBeenLastCalledWith(
            expect.objectContaining({
                reservationId
            } as Partial<ReleaseWalletFundsCommand> as ReleaseWalletFundsCommand)
        );
    });

    it('releases reserved funds on machine funds transfer cancellation', async () => {
        const commandSpy = jest.spyOn(messageBroker, 'command');

        await messageBroker.publish(
            MachineFundsTransferCanceledEvent,
            MachineFundsTransferCanceledEvent.create({
                username,
                machineFundsTransfer: {
                    machineId,
                    customerId,
                    cashWalletOperationReservationId: reservationId,
                    type: MachineFundsTransferType.WalletTransferToMachine
                } as Partial<MachineFundsTransfer> as MachineFundsTransfer,
                comment: 'Test cancellation'
            })
        );

        expect(commandSpy).toHaveBeenLastCalledWith(
            expect.objectContaining({
                reservationId
            } as Partial<ReleaseWalletFundsCommand> as ReleaseWalletFundsCommand)
        );
    });

    it('processes a wallet deposit on machine funds transfer completion', async () => {
        const commandSpy = jest.spyOn(messageBroker, 'command');

        const usedAmount = 100;

        await messageBroker.publish(
            MachineFundsTransferCompletedEvent,
            MachineFundsTransferCompletedEvent.create({
                username,
                machineFundsTransfer: {
                    cashableAmount: usedAmount,
                    customerId,
                    id: UUIDFactory.create(),
                    machineId,
                    type: MachineFundsTransferType.WalletTransferFromMachine
                } as Partial<MachineFundsTransfer> as MachineFundsTransfer
            })
        );

        expect(commandSpy).toHaveBeenCalledWith(
            expect.objectContaining({
                currency,
                ownerId: customerId,
                amount: usedAmount,
                operationType: WalletOperationType.Deposit,
                profitCenterType: ProfitCenterType.Machine,
                profitCenterId: machineId
            } as Partial<ProcessCashWalletOperationCommand> as ProcessCashWalletOperationCommand)
        );
    });

    afterAll(async () => {
        await module.close();
    });
});
