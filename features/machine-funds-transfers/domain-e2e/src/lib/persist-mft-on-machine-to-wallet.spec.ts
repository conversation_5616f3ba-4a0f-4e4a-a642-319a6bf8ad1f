import { Gaming<PERSON><PERSON><PERSON><PERSON>, GetGamingDayDataQuery, GetGamingDayDataQueryResult } from '@cms/casino-operator';
import { EntityAuditModule } from '@cms/entity-audit-domain';
import { InMemoryMessageBrokerModule } from '@cms/in-memory-message-broker';
import {
    MachineFundsTransferCompletedByMachineEvent,
    MachineFundsTransferOperationType,
    MachineFundsTransferStatus,
    MachineFundsTransferType
} from '@cms/machine-funds-transfers';
import { MachineFundsTransferModule } from '@cms/machine-funds-transfers-domain';
import { MessageBroker, MessageBrokerModule, UnsubscribeHandlerMethod } from '@cms/message-broker';
import { INestApplicationContext } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Test } from '@nestjs/testing';
import { CmsDatabaseModule, MachineFundsTransferEntity } from '@tronius/backend-database';
import { <PERSON>urrencyAmountUtils, UUIDFactory, waitFor } from '@tronius/shared-common';
import { EntityManager, Repository } from 'typeorm';

describe('Persist MFT on machine to wallet', () => {
    let app: INestApplicationContext;
    let messageBroker: MessageBroker;
    let mftTypeOrmRepository: Repository<MachineFundsTransferEntity>;
    const unsubscribeQueries: UnsubscribeHandlerMethod[] = [];

    beforeAll(async () => {
        const module = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({ isGlobal: true }),
                MessageBrokerModule.forRootAsync({
                    imports: [InMemoryMessageBrokerModule.forRoot({ awaitHandlerExecution: true, maxListenersPerEvent: 100 })]
                }),
                EntityAuditModule.forMock(),
                CmsDatabaseModule.forRootAsync(),
                MachineFundsTransferModule
            ]
        }).compile();
        app = await module.init();

        messageBroker = module.get(MessageBroker);
        mftTypeOrmRepository = module.get(EntityManager).getRepository(MachineFundsTransferEntity);
    });

    beforeEach(() => {
        jest.clearAllMocks();
        unsubscribeQueries.length = 0;
    });

    it('persists a completed machine funds transfer for "WalletTransferFromMachine"', async () => {
        const cashableCents = 169;
        const expectedAmount = CurrencyAmountUtils.fromCentsValue(cashableCents);
        const getGamingDayData = await messageBroker.registerQueryHandler(
            GetGamingDayDataQuery,
            GetGamingDayDataQueryResult,
            ({ correlationId }) => {
                return {
                    gamingDayData: {
                        date: '2025-01-01',
                        hour: 1,
                        hourIndex: 1,
                        isLastHour: false,
                        isFirstHour: false,
                        timezone: 'timezone',
                        endOfDayHour: 23,
                        startOfHour: new Date(),
                        startOfDay: new Date()
                    } as GamingDayData,
                    correlationId
                };
            }
        );
        unsubscribeQueries.push(getGamingDayData);
        const machineFundsTransferCompletedEvent = MachineFundsTransferCompletedByMachineEvent.create({
            cashableCents,
            customerId: UUIDFactory.create(),
            machineId: UUIDFactory.create(),
            restrictedCents: 0,
            unrestrictedCents: 0,
            username: 'test',
            type: MachineFundsTransferType.WalletTransferFromMachine,
            comment: null
        });

        await messageBroker.publish(MachineFundsTransferCompletedByMachineEvent, machineFundsTransferCompletedEvent);

        await waitFor(3_000);

        // #region Data layer assertions
        const mftEntityFromDatabase = await mftTypeOrmRepository.findOneOrFail({
            where: { type: MachineFundsTransferType.WalletTransferFromMachine },
            relations: { operations: true }
        });

        expect(mftEntityFromDatabase).toBeDefined();

        expect(mftEntityFromDatabase.status).toBe(MachineFundsTransferStatus.Completed);
        expect(mftEntityFromDatabase.amount).toEqual(expectedAmount);

        const { operations } = mftEntityFromDatabase;

        expect(operations).toHaveLength(2);
        const hasCreatedOperation = operations?.some(
            (operation: { type: MachineFundsTransferOperationType }) => operation.type === MachineFundsTransferOperationType.Created
        );
        const hasCompletedOperation = operations?.some(
            (operation: { type: MachineFundsTransferOperationType }) => operation.type === MachineFundsTransferOperationType.Completed
        );

        expect(hasCreatedOperation).toBe(true);
        expect(hasCompletedOperation).toBe(true);
        // #endregion
    });

    afterAll(async () => {
        await Promise.all(unsubscribeQueries.map(async (unsubscribe) => messageBroker.unsubscribeQueryHandler(unsubscribe)));
        await app.close();
    });
});
