/* eslint-disable prefer-destructuring */
import {
    GamingDayData,
    GetGamingDayDataQuery,
    GetGamingDayDataQueryResult,
    GetGamingDayQuery,
    GetGamingDayQueryResult
} from '@cms/casino-operator';
import { EntityAuditModule } from '@cms/entity-audit-domain';
import { InMemoryMessageBrokerModule } from '@cms/in-memory-message-broker';
import {
    CreateMachineFundsTransferCommand,
    CreateMachineFundsTransferCommandResult,
    MachineFundsTransferOperationType,
    MachineFundsTransferStatus,
    MachineFundsTransferType,
    ManuallyCompleteMachineFundsTransferCommand
} from '@cms/machine-funds-transfers';
import { MachineFundsTransferModule } from '@cms/machine-funds-transfers-domain';
import { MessageBroker, MessageBrokerModule, UnsubscribeHandlerMethod } from '@cms/message-broker';
import { INestApplicationContext } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Test } from '@nestjs/testing';
import { CmsDatabaseModule, MachineFundsTransferEntity } from '@tronius/backend-database';
import { ConflictError, ConnectionStatus, UUIDFactory } from '@tronius/shared-common';
import { GetMachineCurrentByIdQuery, GetMachineCurrentByIdQueryResult, MachineCurrent } from '@tronius/shared-domain';
import { EntityManager, Repository } from 'typeorm';

describe('ManuallyCompleteMachineFundsTransfer', () => {
    let app: INestApplicationContext;
    let messageBroker: MessageBroker;
    let mftRepository: Repository<MachineFundsTransferEntity>;
    const unsubscribeQueries: UnsubscribeHandlerMethod[] = [];

    beforeAll(async () => {
        const testModule = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({ isGlobal: true }),
                MessageBrokerModule.forRootAsync({
                    imports: [InMemoryMessageBrokerModule.forRoot({ awaitHandlerExecution: true, maxListenersPerEvent: 100 })]
                }),
                CmsDatabaseModule.forRootAsync(),
                EntityAuditModule.forMock(),
                MachineFundsTransferModule
            ]
        }).compile();

        app = testModule.createNestApplication();
        await app.init();

        messageBroker = testModule.get(MessageBroker);
        mftRepository = testModule.get(EntityManager).getRepository(MachineFundsTransferEntity);
    });

    beforeEach(() => {
        jest.clearAllMocks();
        unsubscribeQueries.length = 0;
    });

    it('should manually complete a pending machine funds transfer', async () => {
        // GIVEN
        const machineId = UUIDFactory.create();
        const customerId = UUIDFactory.create();
        const username = 'test-user';
        const gamingDay = new Date().toISOString().split('T')[0]; // Today's date
        const comment = 'Manual completion test';

        // Mock gaming day data query
        const getGamingDayData = await messageBroker.registerQueryHandler(
            GetGamingDayDataQuery,
            GetGamingDayDataQueryResult,
            ({ correlationId }) => ({
                gamingDayData: {
                    date: gamingDay,
                    hour: 10,
                    hourIndex: 10,
                    isLastHour: false,
                    isFirstHour: false,
                    timezone: 'UTC',
                    endOfDayHour: 23,
                    startOfHour: new Date(),
                    startOfDay: new Date()
                } as GamingDayData,
                correlationId
            })
        );
        unsubscribeQueries.push(getGamingDayData);

        const getGamingDayQuery = await messageBroker.registerQueryHandler(
            GetGamingDayQuery,
            GetGamingDayQueryResult,
            ({ correlationId }) => ({
                gamingDay,
                correlationId
            })
        );
        unsubscribeQueries.push(getGamingDayQuery);

        // Mock machine current query
        const getMachineCurrentById = await messageBroker.registerQueryHandler(
            GetMachineCurrentByIdQuery,
            GetMachineCurrentByIdQueryResult,
            ({ correlationId }) => ({
                machineCurrent: {
                    id: machineId,
                    connectionStatus: ConnectionStatus.Connected
                } as MachineCurrent,
                correlationId
            })
        );
        unsubscribeQueries.push(getMachineCurrentById);

        // Create a pending machine funds transfer
        const createCommand = CreateMachineFundsTransferCommand.create({
            cashableAmount: 100,
            restrictedAmount: 200,
            unrestrictedAmount: 0,
            type: MachineFundsTransferType.ManualTransferIn,
            machineId,
            customerId,
            username
        });

        const { mft } = await messageBroker.command(createCommand, CreateMachineFundsTransferCommandResult);

        // WHEN - Use current time as accounting date (should be after creation)
        const currentAccountingDate = new Date();
        const manuallyCompleteCommand = ManuallyCompleteMachineFundsTransferCommand.create({
            id: mft.id,
            gamingDay,
            accountingDate: currentAccountingDate,
            comment,
            username
        });

        const result = await messageBroker.command(manuallyCompleteCommand);

        // THEN
        expect(result).toBeDefined();
        expect(result.correlationId).toBeDefined();

        // Verify the machine funds transfer was marked as manually completed
        const completedMft = await mftRepository.findOneOrFail({
            where: { id: mft.id },
            relations: { operations: true }
        });

        expect(completedMft.status).toBe(MachineFundsTransferStatus.Completed);
        expect(completedMft.operations).toHaveLength(2); // Created + CompletedManual operations

        const manuallyCompletedOperation = completedMft.operations?.find(
            (op) => op.type === MachineFundsTransferOperationType.CompletedManual
        );
        expect(manuallyCompletedOperation).toBeDefined();
        expect(manuallyCompletedOperation?.comment).toBe(comment);
        expect(manuallyCompletedOperation?.gamingDay).toBe(gamingDay);
    });

    it('should throw ConflictError when trying to manually complete a non-pending transfer', async () => {
        // GIVEN
        const machineId = UUIDFactory.create();
        const customerId = UUIDFactory.create();
        const username = 'test-user';
        const gamingDay = new Date().toISOString().split('T')[0]; // Today's date
        // Mock gaming day data query
        const getGamingDayData = await messageBroker.registerQueryHandler(
            GetGamingDayDataQuery,
            GetGamingDayDataQueryResult,
            ({ correlationId }) => ({
                gamingDayData: {
                    date: gamingDay,
                    hour: 10,
                    hourIndex: 10,
                    isLastHour: false,
                    isFirstHour: false,
                    timezone: 'UTC',
                    endOfDayHour: 23,
                    startOfHour: new Date(),
                    startOfDay: new Date()
                } as GamingDayData,
                correlationId
            })
        );
        unsubscribeQueries.push(getGamingDayData);

        const getGamingDayQuery = await messageBroker.registerQueryHandler(
            GetGamingDayQuery,
            GetGamingDayQueryResult,
            ({ correlationId }) => ({
                gamingDay,
                correlationId
            })
        );
        unsubscribeQueries.push(getGamingDayQuery);

        // Mock machine current query
        const getMachineCurrentById = await messageBroker.registerQueryHandler(
            GetMachineCurrentByIdQuery,
            GetMachineCurrentByIdQueryResult,
            ({ correlationId }) => ({
                machineCurrent: {
                    id: machineId,
                    connectionStatus: ConnectionStatus.Connected
                } as MachineCurrent,
                correlationId
            })
        );
        unsubscribeQueries.push(getMachineCurrentById);

        // Create and complete a machine funds transfer
        const createCommand = CreateMachineFundsTransferCommand.create({
            cashableAmount: 100,
            restrictedAmount: 200,
            unrestrictedAmount: 0,
            type: MachineFundsTransferType.ManualTransferIn,
            machineId,
            customerId,
            username
        });

        const { mft } = await messageBroker.command(createCommand, CreateMachineFundsTransferCommandResult);

        // First manually complete it - Use current time as accounting date
        const currentAccountingDate = new Date();
        const firstCompleteCommand = ManuallyCompleteMachineFundsTransferCommand.create({
            id: mft.id,
            gamingDay,
            accountingDate: currentAccountingDate,
            username
        });

        await messageBroker.command(firstCompleteCommand);

        // WHEN & THEN - Try to manually complete it again
        const secondCompleteCommand = ManuallyCompleteMachineFundsTransferCommand.create({
            id: mft.id,
            gamingDay,
            accountingDate: new Date(), // Current time
            username
        });

        await expect(messageBroker.command(secondCompleteCommand)).rejects.toThrow(ConflictError);
    });

    afterEach(async () => {
        await Promise.all(unsubscribeQueries.map(async (unsubscribe) => messageBroker.unsubscribeQueryHandler(unsubscribe)));
        unsubscribeQueries.length = 0;
    });

    afterAll(async () => {
        await app.close();
    });
});
