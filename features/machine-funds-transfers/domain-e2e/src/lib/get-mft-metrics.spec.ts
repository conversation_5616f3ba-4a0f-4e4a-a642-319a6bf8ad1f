import { EntityAuditModule } from '@cms/entity-audit-domain';
import { InMemoryMessageBrokerModule } from '@cms/in-memory-message-broker';
import {
    GetMachineFundsTransferMetricsForGamingDayQuery,
    GetMachineFundsTransferMetricsForGamingDayQueryResult,
    GetMachineFundsTransferMetricsForPeriodQuery,
    GetMachineFundsTransferMetricsForPeriodQueryResult,
    MachineFundsTransfer,
    MachineFundsTransferOperationType,
    MachineFundsTransferStatus,
    MachineFundsTransferType
} from '@cms/machine-funds-transfers';
import { MachineFundsTransferModule } from '@cms/machine-funds-transfers-domain';
import { MessageBroker, MessageBrokerModule, UnsubscribeHandlerMethod } from '@cms/message-broker';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { Test } from '@nestjs/testing';
import { CmsDatabaseModule, MachineFundsTransferEntity, MachineFundsTransferOperationEntity } from '@tronius/backend-database';
import { TriggerType, UUIDFactory } from '@tronius/shared-common';
import { EntityManager, Repository } from 'typeorm';

describe('Get Machine Funds Transfer metrics for period', () => {
    let app: INestApplication;
    let messageBroker: MessageBroker;
    let mftRepository: Repository<MachineFundsTransferEntity>;
    let mftOperationRepository: Repository<MachineFundsTransferOperationEntity>;
    const unsubscribeQueries: UnsubscribeHandlerMethod[] = [];

    beforeEach(async () => {
        jest.clearAllMocks();
        await mftOperationRepository.query('DELETE FROM machine_funds_transfer_operation');
        await mftRepository.query('DELETE FROM machine_funds_transfer');
        unsubscribeQueries.length = 0;
    });

    beforeAll(async () => {
        const testModule = await Test.createTestingModule({
            imports: [
                ConfigModule.forRoot({ isGlobal: true }),
                ScheduleModule.forRoot(),
                MessageBrokerModule.forRootAsync({
                    imports: [InMemoryMessageBrokerModule.forRoot({ awaitHandlerExecution: true, maxListenersPerEvent: 100 })]
                }),
                CmsDatabaseModule.forRootAsync(),
                EntityAuditModule.forMock(),
                MachineFundsTransferModule
            ]
        }).compile();

        app = testModule.createNestApplication();
        await app.init();

        messageBroker = testModule.get(MessageBroker);
        mftRepository = testModule.get(EntityManager).getRepository(MachineFundsTransferEntity);
        mftOperationRepository = testModule.get(EntityManager).getRepository(MachineFundsTransferOperationEntity);
    });

    it('should return mft metrics for period', async () => {
        // GIVEN
        const machineId = UUIDFactory.create();
        const gamingDay = '2025-01-01';
        const date = new Date(gamingDay);
        const machineFundsTransfers = [
            prepareMftAndOperation(100, 200, MachineFundsTransferType.ManualTransferIn, date, machineId, gamingDay),
            prepareMftAndOperation(100, 200, MachineFundsTransferType.ManualTransferIn, date, machineId, gamingDay),
            prepareMftAndOperation(100, 200, MachineFundsTransferType.WalletTransferToMachine, date, machineId, gamingDay),
            prepareMftAndOperation(75, 150, MachineFundsTransferType.ManualTransferOut, date, machineId, gamingDay),
            prepareMftAndOperation(75, 150, MachineFundsTransferType.ManualTransferOut, date, machineId, gamingDay),
            prepareMftAndOperation(75, 150, MachineFundsTransferType.WalletTransferFromMachine, date, machineId, gamingDay)
        ];
        await Promise.all(machineFundsTransfers.map(async (mft) => mftRepository.save(mft)));

        const { metrics } = await messageBroker.query(
            GetMachineFundsTransferMetricsForPeriodQuery.create({ from: date, to: date, machineId }),
            GetMachineFundsTransferMetricsForPeriodQueryResult
        );

        expect(metrics).toEqual(
            expect.objectContaining({
                cashableAmountToMachine: 300,
                cashableAmountFromMachine: 225,
                restrictedAmountToMachine: 600,
                restrictedAmountFromMachine: 450,
                // All transfers are completed by machine, so adjustments should be 0
                cashableAmountToMachineAdjustment: 0,
                cashableAmountFromMachineAdjustment: 0,
                restrictedAmountToMachineAdjustment: 0,
                restrictedAmountFromMachineAdjustment: 0
            })
        );
    });

    it('should return mft metrics for gaming day', async () => {
        // GIVEN
        const machineId = UUIDFactory.create();
        const gamingDay = '2025-01-01';
        const date = new Date(gamingDay);
        const machineFundsTransfers = [
            prepareMftAndOperation(100, 200, MachineFundsTransferType.ManualTransferIn, date, machineId, gamingDay),
            prepareMftAndOperation(100, 200, MachineFundsTransferType.ManualTransferIn, date, machineId, gamingDay),
            prepareMftAndOperation(100, 200, MachineFundsTransferType.WalletTransferToMachine, date, machineId, gamingDay),
            prepareMftAndOperation(75, 150, MachineFundsTransferType.ManualTransferOut, date, machineId, gamingDay),
            prepareMftAndOperation(75, 150, MachineFundsTransferType.ManualTransferOut, date, machineId, gamingDay),
            prepareMftAndOperation(75, 150, MachineFundsTransferType.WalletTransferFromMachine, date, machineId, gamingDay)
        ];
        await Promise.all(machineFundsTransfers.map(async (mft) => mftRepository.save(mft)));

        const { metrics } = await messageBroker.query(
            GetMachineFundsTransferMetricsForGamingDayQuery.create({ gamingDay, machineId }),
            GetMachineFundsTransferMetricsForGamingDayQueryResult
        );

        expect(metrics).toEqual(
            expect.objectContaining({
                cashableAmountToMachine: 300,
                cashableAmountFromMachine: 225,
                restrictedAmountToMachine: 600,
                restrictedAmountFromMachine: 450,
                // All transfers are completed by machine, so adjustments should be 0
                cashableAmountToMachineAdjustment: 0,
                cashableAmountFromMachineAdjustment: 0,
                restrictedAmountToMachineAdjustment: 0,
                restrictedAmountFromMachineAdjustment: 0
            })
        );
    });

    it('should return mft metrics with adjustments for manually completed transfers', async () => {
        // GIVEN
        const machineId = UUIDFactory.create();
        const gamingDay = '2025-01-01';
        const date = new Date(gamingDay);

        const machineFundsTransfers = [
            // Regular completed transfers (by machine)
            prepareMftAndOperation(100, 200, MachineFundsTransferType.ManualTransferIn, date, machineId, gamingDay),
            prepareMftAndOperation(75, 150, MachineFundsTransferType.ManualTransferOut, date, machineId, gamingDay),
            // Manually completed transfers
            prepareMftAndOperationManual(200, 300, MachineFundsTransferType.WalletTransferToMachine, date, machineId, gamingDay),
            prepareMftAndOperationManual(125, 175, MachineFundsTransferType.ManualTransferOut, date, machineId, gamingDay)
        ];
        await Promise.all(machineFundsTransfers.map(async (mft) => mftRepository.save(mft)));

        const { metrics } = await messageBroker.query(
            GetMachineFundsTransferMetricsForPeriodQuery.create({ from: date, to: date, machineId }),
            GetMachineFundsTransferMetricsForPeriodQueryResult
        );

        expect(metrics).toEqual(
            expect.objectContaining({
                // Main amounts (only regular transfers, excluding manually completed)
                cashableAmountToMachine: 100, // Only regular ManualTransferIn
                cashableAmountFromMachine: 75, // Only regular ManualTransferOut
                restrictedAmountToMachine: 200, // Only regular ManualTransferIn
                restrictedAmountFromMachine: 150, // Only regular ManualTransferOut
                // Adjustment amounts (only manually completed)
                cashableAmountToMachineAdjustment: 200, // Only WalletTransferToMachine
                cashableAmountFromMachineAdjustment: 125, // Only ManualTransferOut
                restrictedAmountToMachineAdjustment: 300, // Only WalletTransferToMachine
                restrictedAmountFromMachineAdjustment: 175 // Only ManualTransferOut
            })
        );
    });

    const prepareMftAndOperation = (
        cashableAmount: number,
        restrictedAmount: number,
        type: MachineFundsTransferType,
        triggeredAt: Date,
        machineId: string,
        gamingDay: string
    ): Partial<MachineFundsTransfer> => {
        const mftId = UUIDFactory.create();
        return {
            id: mftId,
            machineId,
            type,
            restrictedAmount,
            cashableAmount,
            amount: cashableAmount + restrictedAmount,
            status: MachineFundsTransferStatus.Completed,
            operations: [
                {
                    id: UUIDFactory.create(),
                    triggeredAt: triggeredAt.getTime(),
                    type: MachineFundsTransferOperationType.Completed,
                    triggeredByType: TriggerType.Machine,
                    triggeredById: UUIDFactory.create(),
                    gamingDay,
                    machineFundsTransferId: mftId,
                    createdAt: triggeredAt.getTime()
                }
            ]
        };
    };

    const prepareMftAndOperationManual = (
        cashableAmount: number,
        restrictedAmount: number,
        type: MachineFundsTransferType,
        triggeredAt: Date,
        machineId: string,
        gamingDay: string
    ): Partial<MachineFundsTransfer> => {
        const mftId = UUIDFactory.create();
        return {
            id: mftId,
            machineId,
            type,
            restrictedAmount,
            cashableAmount,
            amount: cashableAmount + restrictedAmount,
            status: MachineFundsTransferStatus.Completed,
            operations: [
                {
                    id: UUIDFactory.create(),
                    triggeredAt: triggeredAt.getTime(),
                    type: MachineFundsTransferOperationType.Created,
                    triggeredByType: TriggerType.User,
                    triggeredById: 'user1',
                    gamingDay,
                    machineFundsTransferId: mftId,
                    createdAt: triggeredAt.getTime()
                },
                {
                    id: UUIDFactory.create(),
                    triggeredAt: triggeredAt.getTime(),
                    type: MachineFundsTransferOperationType.CompletedManual,
                    triggeredByType: TriggerType.User,
                    triggeredById: 'user2',
                    gamingDay,
                    machineFundsTransferId: mftId,
                    createdAt: triggeredAt.getTime()
                }
            ]
        };
    };

    afterAll(async () => {
        await app.close();
    });
});
