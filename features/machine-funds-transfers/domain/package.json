{"name": "@cms/machine-funds-transfers-domain", "version": "0.0.1", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"reflect-metadata": "^0.2.1"}, "peerDependencies": {"@cms/casino-operator": "file:../../features/casino-operator/shared", "@cms/entity-audit": "file:../../entity-audit/shared", "@cms/entity-audit-domain": "file:../../entity-audit/domain", "@cms/execution-context": "file:../../../libs/backend/infrastructure/execution-context", "@cms/machine-funds-transfers": "file:../shared", "@cms/message-broker": "file:../../../libs/backend/message-broker", "@cms/server-authentication": "file:../../../libs/backend/cms-server-authentication", "@cms/server-permissions": "file:../../../libs/backend/server-permissions", "@cms/typeorm": "file:../../typeorm", "@cms/validation": "0.0.4", "@nestjs/common": "^10.3.3", "@nestjs/swagger": "^7.3.0", "@tronius/backend-common": "file:../../../libs/backend/common", "@tronius/backend-database": "file:../../../libs/backend/database", "@tronius/shared-common": "file:../../../libs/shared/common", "@tronius/shared-domain": "file:../../../libs/shared/domain", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "lodash": "^4.17.21", "typeorm": "0.3.17"}}