import { MachineFundsTransferOperation } from '@cms/machine-funds-transfers';
import { InjectRepository } from '@cms/typeorm';
import { Injectable } from '@nestjs/common';
import { TgAbstractDao } from '@tronius/backend-common';
import { MachineFundsTransferOperationEntity } from '@tronius/backend-database';
import { DeepPartial, Repository } from 'typeorm';

import { MachineFundsTransferDao } from './machine-funds-transfer.dao';

@Injectable()
export class MachineFundsTransferOperationDao extends TgAbstractDao<MachineFundsTransferOperation, MachineFundsTransferOperationEntity> {
    constructor(
        @InjectRepository(MachineFundsTransferOperationEntity)
        override readonly repository: Repository<MachineFundsTransferOperationEntity>
    ) {
        super(repository, MachineFundsTransferOperationEntity);
    }

    protected entityToDto(entity: MachineFundsTransferOperationEntity): MachineFundsTransferOperation;
    protected entityToDto(entity: MachineFundsTransferOperationEntity | null): MachineFundsTransferOperation | null {
        return entity ? MachineFundsTransferOperationDao.toDto(entity) : null;
    }

    protected dtoToEntity(dto: MachineFundsTransferOperation): DeepPartial<MachineFundsTransferOperationEntity>;
    protected dtoToEntity(dto: MachineFundsTransferOperation | null): DeepPartial<MachineFundsTransferOperationEntity> | null {
        return dto ? MachineFundsTransferOperationDao.fromDto(dto) : null;
    }

    static toDto(entity: MachineFundsTransferOperationEntity): MachineFundsTransferOperation {
        return {
            id: entity.id,
            type: entity.type,
            triggeredByType: entity.triggeredByType,
            triggeredById: entity.triggeredById,
            triggeredAt: entity.triggeredAt,
            comment: entity.comment,
            gamingDay: entity.gamingDay,
            machineFundsTransferId: entity.machineFundsTransferId,
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
            machineFundsTransfer: entity.machineFundsTransfer ? MachineFundsTransferDao.toDto(entity.machineFundsTransfer) : undefined,
            createdAt: entity.createdAt
        };
    }

    static fromDto(dto: MachineFundsTransferOperation): MachineFundsTransferOperationEntity {
        const entity = new MachineFundsTransferOperationEntity();
        entity.id = dto.id;
        entity.type = dto.type;
        entity.triggeredByType = dto.triggeredByType;
        entity.triggeredById = dto.triggeredById;
        entity.triggeredAt = dto.triggeredAt;
        entity.comment = dto.comment;
        entity.gamingDay = dto.gamingDay;

        entity.machineFundsTransferId = dto.machineFundsTransferId;
        entity.createdAt = dto.createdAt;
        // We don't need relations here

        return entity;
    }
}
