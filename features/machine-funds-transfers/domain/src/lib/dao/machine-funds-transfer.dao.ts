import {
    CreateMachineFundsTransfer,
    MachineFundsTransfer,
    MachineFundsTransferOperation,
    MachineFundsTransferOperationType,
    MachineFundsTransferStatus
} from '@cms/machine-funds-transfers';
import { InjectRepository } from '@cms/typeorm';
import { Injectable, Logger } from '@nestjs/common';
import { TgAbstractDao } from '@tronius/backend-common';
import { MachineFundsTransferEntity, MachineFundsTransferOperationEntity } from '@tronius/backend-database';
import { ConflictError, TgErrorType, TriggerType } from '@tronius/shared-common';
import { DeepPartial, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

import { MachineFundsTransferOperationDao } from './machine-funds-transfer-operation.dao';
import { GetMachineFundsTransferGamingDayMetricProps, GetMachineFundsTransferPeriodMetricProps } from '../machine-funds-transfer.service';

type InsertMachineFundsTransferOperationMethod = (mft: MachineFundsTransferOperation) => Promise<void>;
type UpdateMachineFundsTransferStatusMethod = (
    mftId: string,
    status: MachineFundsTransferStatus,
    currentStatus: MachineFundsTransferStatus
) => Promise<boolean>;

@Injectable()
export class MachineFundsTransferDao extends TgAbstractDao<MachineFundsTransfer, MachineFundsTransferEntity> {
    protected override readonly logger = new Logger(MachineFundsTransferDao.name);

    constructor(
        @InjectRepository(MachineFundsTransferEntity)
        override readonly repository: Repository<MachineFundsTransferEntity>
    ) {
        super(repository, MachineFundsTransferEntity);
    }

    async findById(id: string): Promise<MachineFundsTransfer | null> {
        const mft = await this.repository.findOneBy({ id });
        if (!mft) {
            return null;
        }
        return this.entityToDto(mft);
    }

    async create(
        data: CreateMachineFundsTransfer,
        triggeredById: string,
        gamingDay: string,
        triggeredByType: TriggerType = TriggerType.User
    ): Promise<MachineFundsTransfer> {
        const mft = await this.repository.manager.transaction(async (manager) => {
            const transactionalRepository = manager.getRepository(MachineFundsTransferEntity);
            const newMft = manager.create(MachineFundsTransferEntity, {
                ...data,
                amount: data.cashableAmount + data.restrictedAmount + data.unrestrictedAmount,
                status: MachineFundsTransferStatus.Pending,
                operations: [
                    {
                        type: MachineFundsTransferOperationType.Created,
                        triggeredByType,
                        triggeredById,
                        gamingDay,
                        createdAt: Date.now(),
                        triggeredAt: Date.now()
                    } as Partial<MachineFundsTransferOperationEntity> as MachineFundsTransferOperationEntity
                ]
            } as DeepPartial<MachineFundsTransferEntity>);

            return transactionalRepository.save(newMft);
        });

        return mft;
    }

    async processMftOperationTransaction(
        transactionalProcedure: (
            insertMftOperationMethod: InsertMachineFundsTransferOperationMethod,
            updateMftStatusMethod: UpdateMachineFundsTransferStatusMethod
        ) => Promise<void>
    ): Promise<void> {
        return this.repository.manager.transaction(async (manager) => {
            const insertMftOperationMethod: InsertMachineFundsTransferOperationMethod = async (mftOperation) => {
                await manager.insert(MachineFundsTransferOperationEntity, mftOperation);
            };

            const updateMftStatusMethod: UpdateMachineFundsTransferStatusMethod = async (mftId, status, currentStatus) => {
                const { affected } = await manager.update(
                    MachineFundsTransferEntity,
                    {
                        id: mftId,
                        status: currentStatus
                    },
                    { status }
                );

                return affected === 1;
            };

            await transactionalProcedure(insertMftOperationMethod, updateMftStatusMethod);
        });
    }

    async updateStatusAndInsertOperation(
        id: string,
        triggeredById: string,
        gamingDay: string,
        status: MachineFundsTransferStatus,
        operationType: MachineFundsTransferOperationType,
        isPreviousStatusInvalid: (status: MachineFundsTransferStatus) => boolean,
        comment?: string
    ): Promise<void> {
        await this.repository.manager.transaction(async (manager) => {
            const mft = await manager.findOneOrFail(MachineFundsTransferEntity, {
                where: { id },
                // Locking, so that other transactions cannot redeem the same machine-funds-transfer
                lock: { mode: 'pessimistic_write' }
            });

            if (isPreviousStatusInvalid(mft.status)) {
                throw new ConflictError({
                    errorType: TgErrorType.MachineFundsTransferInvalidStatus,
                    message: `MachineFundsTransfer status is ${status} and the action can not be performed.`,
                    params: [{ status }]
                });
            }

            await manager.update(MachineFundsTransferEntity, mft.id, { status });

            const operation: QueryDeepPartialEntity<MachineFundsTransferOperationEntity> = {
                type: operationType,
                triggeredByType: TriggerType.User,
                triggeredById,
                triggeredAt: Date.now(),
                machineFundsTransferId: mft.id,
                createdAt: Date.now(),
                comment,
                gamingDay
            };
            await manager.insert(MachineFundsTransferOperationEntity, operation);
        });
    }

    async getCompletedMachineFundsTransfersWithOperationsForPeriod({
        from,
        to,
        machineId
    }: GetMachineFundsTransferPeriodMetricProps): Promise<MachineFundsTransfer[]> {
        const machineFundsTransfers = await this.repository
            .createQueryBuilder('mft')
            .leftJoinAndSelect('mft.operations', 'operation')
            .innerJoin('mft.operations', 'completionOperation', 'completionOperation.type IN (:...types)', {
                types: [MachineFundsTransferOperationType.Completed, MachineFundsTransferOperationType.CompletedManual]
            })
            .where('mft.status = :status', { status: MachineFundsTransferStatus.Completed })
            .andWhere('mft.machineId = :machineId', { machineId })
            .andWhere('completionOperation.triggeredAt BETWEEN :from AND :to', { from, to })
            .getMany();

        return machineFundsTransfers.map((mft) => this.entityToDto(mft));
    }

    async getCompletedMachineFundsTransfersWithOperationsForGamingDay({
        gamingDay,
        machineId
    }: GetMachineFundsTransferGamingDayMetricProps): Promise<MachineFundsTransfer[]> {
        const machineFundsTransfers = await this.repository
            .createQueryBuilder('mft')
            .leftJoinAndSelect('mft.operations', 'operation')
            .innerJoin('mft.operations', 'completionOperation', 'completionOperation.type IN (:...types)', {
                types: [MachineFundsTransferOperationType.Completed, MachineFundsTransferOperationType.CompletedManual]
            })
            .where('mft.status = :status', { status: MachineFundsTransferStatus.Completed })
            .andWhere('mft.machineId = :machineId', { machineId })
            .andWhere('completionOperation.gamingDay = :gamingDay', { gamingDay })
            .getMany();

        return machineFundsTransfers.map((mft) => this.entityToDto(mft));
    }

    protected entityToDto(entity: MachineFundsTransferEntity): MachineFundsTransfer;
    protected entityToDto(entity: MachineFundsTransferEntity | null): MachineFundsTransfer | null {
        return entity ? MachineFundsTransferDao.toDto(entity) : null;
    }

    protected dtoToEntity(dto: MachineFundsTransfer): DeepPartial<MachineFundsTransferEntity>;
    protected dtoToEntity(dto: MachineFundsTransfer | null): DeepPartial<MachineFundsTransferEntity> | null {
        if (!dto) {
            return null;
        }

        const entity = new MachineFundsTransferEntity();
        entity.id = dto.id;
        entity.type = dto.type;
        entity.cashableAmount = dto.cashableAmount;
        entity.restrictedAmount = dto.restrictedAmount;
        entity.unrestrictedAmount = dto.unrestrictedAmount;
        entity.amount = dto.amount;
        entity.status = dto.status;
        entity.offerId = dto.offerId;
        entity.machineId = dto.machineId;
        entity.createdAt = dto.createdAt;
        entity.updatedAt = dto.updatedAt;
        entity.cashWalletOperationReservationId = dto.cashWalletOperationReservationId;
        entity.customerId = dto.customerId;

        entity.operations = dto.operations?.map((operation) => MachineFundsTransferOperationDao.fromDto(operation));

        return entity;
    }

    static toDto(entity: MachineFundsTransferEntity): MachineFundsTransfer {
        return {
            id: entity.id,
            type: entity.type,
            cashableAmount: entity.cashableAmount,
            restrictedAmount: entity.restrictedAmount,
            unrestrictedAmount: entity.unrestrictedAmount,
            amount: entity.amount,
            status: entity.status,
            offerId: entity.offerId,
            machineId: entity.machineId,
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
            customerId: entity.customerId,
            cashWalletOperationReservationId: entity.cashWalletOperationReservationId,
            operations: entity.operations?.map((operation) => MachineFundsTransferOperationDao.toDto(operation))
        };
    }
}
