import { error } from 'console';

import {
    GetCasinoSettingsQuery,
    GetCasinoSettingsQueryResult,
    GetGamingDayDataQuery,
    GetGamingDayDataQueryResult,
    GetGamingDayQuery,
    GetGamingDayQueryResult
} from '@cms/casino-operator';
import { ExecutionContext } from '@cms/execution-context';
import {
    CompleteMachineFundsTransfer,
    CreateMachineFundsTransfer,
    CreateManualFundsTransfer,
    MachineFundsTransfer,
    MachineFundsTransferCanceledEvent,
    MachineFundsTransferCompletedEvent,
    MachineFundsTransferCreatedEvent,
    MachineFundsTransferFailedEvent,
    MachineFundsTransferMetrics,
    MachineFundsTransferOperation,
    MachineFundsTransferOperationType,
    MachineFundsTransferStatus,
    MachineFundsTransferType
} from '@cms/machine-funds-transfers';
import { MessageBroker } from '@cms/message-broker';
import { Injectable, Logger, Optional } from '@nestjs/common';
import {
    BadRequestError,
    ConflictError,
    ConnectionStatus,
    ErrorUtils,
    NotFoundError,
    TgErrorType,
    TgPaginationRequest,
    TgPaginationResponse,
    TriggerType,
    UUIDFactory
} from '@tronius/shared-common';
import { GetMachineCurrentByIdQuery, GetMachineCurrentByIdQueryResult, MachineCurrent } from '@tronius/shared-domain';
import max from 'lodash/max';

import { MachineFundsTransferDao } from './dao/machine-funds-transfer.dao';
import { MachineFundsMetricCalculationService } from './machine-funds-metric-calculation.service';

export interface GetMachineFundsTransferPeriodMetricProps {
    from: Date;
    to: Date;
    machineId: string;
}

export interface GetMachineFundsTransferGamingDayMetricProps {
    gamingDay: string;
    machineId: string;
}

@Injectable()
export class MachineFundsTransferService {
    private readonly logger = new Logger(MachineFundsTransferService.name);

    constructor(
        private readonly dao: MachineFundsTransferDao,
        private readonly messageBroker: MessageBroker,
        private readonly machineFundsMetricCalculationService: MachineFundsMetricCalculationService,
        @Optional() private readonly executionContext?: ExecutionContext
    ) {}

    async list(request: TgPaginationRequest<MachineFundsTransfer>): Promise<TgPaginationResponse<MachineFundsTransfer>> {
        return this.dao.list(request.start, request.count, request.filters, request.sorts, request.relations);
    }

    async createManualTransfer(data: CreateManualFundsTransfer, username: string): Promise<MachineFundsTransfer> {
        // Check machine status
        await this.checkForMachineOrThrow(data.machineId);
        const { gamingDayData } = await this.messageBroker.query(GetGamingDayDataQuery.create({}), GetGamingDayDataQueryResult);
        // Create machine funds transfer entry
        const mft = await this.dao.create(data, username, gamingDayData.date);

        await this.messageBroker.publish(
            MachineFundsTransferCreatedEvent,
            MachineFundsTransferCreatedEvent.create({ triggeredBy: username, machineFundsTransfer: mft })
        );
        return mft;
    }

    async createAndCompleteWalletTransferFromMachine(data: Omit<CreateMachineFundsTransfer, 'type'>, username: string): Promise<void> {
        const { customerId, cashableAmount, restrictedAmount, unrestrictedAmount, machineId } = data;
        this.executionContext?.assign({ machineId, username });

        const anyValueAssigned = [cashableAmount, restrictedAmount, unrestrictedAmount].some((value) => value > 0);
        if (!anyValueAssigned) {
            this.logger.debug('No values assigned to transfer. Skipping transfer persistence.');
            return;
        }

        if (!customerId) {
            throw new BadRequestError({
                message: 'customerId is required for wallet transfer from machine'
            });
        }
        const { gamingDayData } = await this.messageBroker.query(GetGamingDayDataQuery.create({}), GetGamingDayDataQueryResult);
        const { id } = await this.dao.create(
            { ...data, type: MachineFundsTransferType.WalletTransferFromMachine },
            username,
            gamingDayData.date,
            TriggerType.Med
        );

        await this.complete(id, username);
    }

    async cancel(id: string, username: string, comment?: string): Promise<void> {
        const { gamingDayData } = await this.messageBroker.query(GetGamingDayDataQuery.create({}), GetGamingDayDataQueryResult);
        await this.dao.updateStatusAndInsertOperation(
            id,
            username,
            gamingDayData.date,
            MachineFundsTransferStatus.Cancelled,
            MachineFundsTransferOperationType.Cancelled,
            (status) => status !== MachineFundsTransferStatus.Pending,
            comment
        );

        const canceledMachineFundsTransfer = await this.dao.findOneByOrFail({ id });
        await this.messageBroker.publish(
            MachineFundsTransferCanceledEvent,
            MachineFundsTransferCanceledEvent.create({
                machineFundsTransfer: canceledMachineFundsTransfer,
                username,
                comment
            })
        );
    }

    async complete(mftId: string, username: string, comment?: string): Promise<void> {
        const { gamingDayData } = await this.messageBroker.query(GetGamingDayDataQuery.create({}), GetGamingDayDataQueryResult);
        await this.dao.updateStatusAndInsertOperation(
            mftId,
            username,
            gamingDayData.date,
            MachineFundsTransferStatus.Completed,
            MachineFundsTransferOperationType.Completed,
            (status) => status !== MachineFundsTransferStatus.Pending,
            comment
        );

        const completedMachineFundsTransfer = await this.dao.findOneByOrFail({ id: mftId });
        await this.messageBroker.publish(
            MachineFundsTransferCompletedEvent,
            MachineFundsTransferCompletedEvent.create({
                machineFundsTransfer: completedMachineFundsTransfer,
                username
            })
        );
    }

    async markAftInAsManuallyCompleted(
        { gamingDay, accountingDate, comment, id: mftId }: CompleteMachineFundsTransfer,
        username: string
    ): Promise<void> {
        const [machineFundsTransfer, { gamingDay: currentGamingDay }, { gamingDay: accountingDateGamingDay }] = await Promise.all([
            this.getMachineFundsTransferByIdOrThrow(mftId),
            this.messageBroker.query(GetGamingDayQuery.create({}), GetGamingDayQueryResult),
            this.messageBroker.query(GetGamingDayQuery.create({ timestamp: accountingDate.getTime() }), GetGamingDayQueryResult)
        ]);

        if (machineFundsTransfer.status !== MachineFundsTransferStatus.Pending) {
            throw new ConflictError({
                message: `Machine funds transfer with id ${mftId} is not pending`,
                params: { mftId, status: machineFundsTransfer.status }
            });
        }

        const allowedMftInOperations = [MachineFundsTransferType.WalletTransferToMachine, MachineFundsTransferType.ManualTransferIn];

        if (!allowedMftInOperations.includes(machineFundsTransfer.type)) {
            throw new BadRequestError({
                message: `Machine funds transfer with id ${mftId} cannot be marked as completed manually`,
                params: { mftId, type: machineFundsTransfer.type }
            });
        }

        const accountingDateIsInFuture = accountingDate.getTime() > Date.now();
        if (accountingDateIsInFuture) {
            throw new BadRequestError({
                message: `Accounting date ${accountingDate.toISOString()} is in the future`
            });
        }

        const gamingDayInFuture = gamingDay > currentGamingDay;
        if (gamingDayInFuture) {
            throw new BadRequestError({
                message: `Gaming day ${gamingDay} is in the future`
            });
        }

        const lastOperationAccountingDate = max((machineFundsTransfer.operations || []).map((operation) => operation.triggeredAt)) || 0;

        if (accountingDate.getTime() < lastOperationAccountingDate) {
            throw new BadRequestError({
                message: `Accounting date ${accountingDate.toISOString()} is before the last operation date ${lastOperationAccountingDate}`
            });
        }

        const accountingDateAndGamingDayMatch = accountingDateGamingDay === gamingDay;
        if (!accountingDateAndGamingDayMatch) {
            throw new BadRequestError({
                message: `Accounting date ${accountingDate.toISOString()} does not match the gaming day ${gamingDay}`
            });
        }

        await this.dao.processMftOperationTransaction(async (insertMftOperationMethod, updateMftStatusMethod) => {
            const mftOperation: MachineFundsTransferOperation = {
                id: UUIDFactory.create(),
                createdAt: Date.now(),
                gamingDay,
                triggeredAt: accountingDate.getTime(),
                triggeredByType: TriggerType.User,
                triggeredById: username,
                type: MachineFundsTransferOperationType.CompletedManual,
                comment,
                machineFundsTransferId: mftId
            };

            await insertMftOperationMethod(mftOperation);
            const statusUpdated = await updateMftStatusMethod(
                mftId,
                MachineFundsTransferStatus.Completed,
                MachineFundsTransferStatus.Pending
            );

            if (statusUpdated) {
                return;
            }

            throw new ConflictError({
                message: 'Machine funds transfer status update failed',
                params: { mftId, status: machineFundsTransfer.status }
            });
        });

        machineFundsTransfer.status = MachineFundsTransferStatus.Completed;
        await this.messageBroker.publish(
            MachineFundsTransferCompletedEvent,
            MachineFundsTransferCompletedEvent.create({
                machineFundsTransfer,
                username
            })
        );
    }

    async getMachineFundsTransferByIdOrThrow(id: string): Promise<MachineFundsTransfer> {
        const mft = await this.dao.repository.findOne({ where: { id }, relations: { operations: true } });

        if (!mft) {
            throw new NotFoundError({
                message: `Machine funds transfer with id ${id} not found`,
                params: { id }
            });
        }

        return mft;
    }

    async failed(mftId: string, username: string, comment?: string): Promise<void> {
        const mft = await this.dao.findOneByOrFail({ id: mftId });

        // If MFT is still pending, update status to failed
        if (mft.status !== MachineFundsTransferStatus.Pending) {
            return;
        }
        const { gamingDayData } = await this.messageBroker.query(GetGamingDayDataQuery.create({}), GetGamingDayDataQueryResult);
        // Update MFT status to failed
        await this.dao.updateStatusAndInsertOperation(
            mftId,
            username,
            gamingDayData.date,
            MachineFundsTransferStatus.Failed,
            MachineFundsTransferOperationType.Failed,
            (status) => status !== MachineFundsTransferStatus.Pending,
            comment
        );

        const machineFundsTransfer = await this.dao.findOneByOrFail({ id: mftId });
        await this.messageBroker.publish(
            MachineFundsTransferFailedEvent,
            MachineFundsTransferFailedEvent.create({ machineFundsTransfer, username })
        );
    }

    async jackpotWon(machineId: string, amount: number, offerId: string): Promise<void> {
        this.logger.log(`Jackpot won on machine ${machineId}. Sending AFT bonus of ${amount} with offer ID ${offerId}...`);
        let mftId: string | undefined;
        try {
            const [
                {
                    settings: { maxBonusTransferToMachineAmount }
                },
                {
                    gamingDayData: { date }
                }
            ] = await Promise.all([
                this.messageBroker.query(GetCasinoSettingsQuery.create(), GetCasinoSettingsQueryResult),
                this.messageBroker.query(GetGamingDayDataQuery.create({}), GetGamingDayDataQueryResult),
                this.checkForMachineOrThrow(machineId)
            ]);

            const type =
                amount < maxBonusTransferToMachineAmount
                    ? MachineFundsTransferType.AftBonusTransfer
                    : MachineFundsTransferType.AftBonusHandpay;
            this.logger.log(`AFT bonus type: ${type}`);

            const mft = await this.dao.save({
                cashableAmount: amount,
                amount,
                machineId,
                offerId,
                operations: [
                    {
                        type: MachineFundsTransferOperationType.Created,
                        triggeredByType: TriggerType.System,
                        triggeredById: 'cms',
                        gamingDay: date
                    }
                ],
                status: MachineFundsTransferStatus.Pending,
                type:
                    amount < maxBonusTransferToMachineAmount
                        ? MachineFundsTransferType.AftBonusTransfer
                        : MachineFundsTransferType.AftBonusHandpay
            });
            mftId = mft.id;

            this.logger.log(`AFT bonus of ${amount} sent to machine ${machineId}. Machine founds transfer ID: ${mft.id}`);
        } catch (ignoreError) {
            this.logger.error(`Failed to send machine funds transfer to machine ${machineId}: ${ErrorUtils.errorToString(error)}`);

            if (mftId) {
                // Update MFT status to failed and create manual handpay
                await this.failed(machineId, mftId, 'Failed to send AFT bonus to machine.').catch((e) =>
                    this.logger.error(`Failed to update MFT status to failed: ${ErrorUtils.errorToString(e)}`)
                );
            }
        }
    }

    async getMachineFundsTransferPeriodMetrics(props: GetMachineFundsTransferPeriodMetricProps): Promise<MachineFundsTransferMetrics> {
        const machineFunds = await this.dao.getCompletedMachineFundsTransfersWithOperationsForPeriod(props);
        return this.machineFundsMetricCalculationService.calculateMachineFundsMetric(machineFunds);
    }

    async getMachineFundsTransferGamingDayMetrics(
        props: GetMachineFundsTransferGamingDayMetricProps
    ): Promise<MachineFundsTransferMetrics> {
        const machineFunds = await this.dao.getCompletedMachineFundsTransfersWithOperationsForGamingDay(props);
        return this.machineFundsMetricCalculationService.calculateMachineFundsMetric(machineFunds);
    }

    private async checkForMachineOrThrow(machineId: string): Promise<MachineCurrent> {
        const { machineCurrent } = await this.messageBroker.query(
            GetMachineCurrentByIdQuery.create({ machineId }),
            GetMachineCurrentByIdQueryResult
        );
        if (machineCurrent.connectionStatus !== ConnectionStatus.Connected) {
            throw new ConflictError({
                message: `Machine ${machineId} is not connected`,
                errorType: TgErrorType.MachineNotConnected
            });
        }
        return machineCurrent;
    }
}
