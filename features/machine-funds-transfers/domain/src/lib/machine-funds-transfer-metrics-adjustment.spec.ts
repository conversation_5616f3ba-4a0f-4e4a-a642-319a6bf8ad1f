import {
    MachineFundsTransfer,
    MachineFundsTransferOperationType,
    MachineFundsTransferStatus,
    MachineFundsTransferType
} from '@cms/machine-funds-transfers';
import { TriggerType, UUIDFactory } from '@tronius/shared-common';

import { MachineFundsMetricCalculationService } from './machine-funds-metric-calculation.service';

describe('MachineFundsMetricCalculationService', () => {
    let service: MachineFundsMetricCalculationService;

    beforeEach(() => {
        service = new MachineFundsMetricCalculationService();
    });

    describe('calculateMachineFundsMetric', () => {
        it('should calculate adjustment properties for manually completed transfers', () => {
            // GIVEN
            const machineId = UUIDFactory.create();
            const gamingDay = '2025-01-30';

            const machineFunds: MachineFundsTransfer[] = [
                // Regular completed transfer (not manually completed)
                {
                    id: UUIDFactory.create(),
                    machineId,
                    type: MachineFundsTransferType.ManualTransferIn,
                    status: MachineFundsTransferStatus.Completed,
                    cashableAmount: 100,
                    restrictedAmount: 50,
                    unrestrictedAmount: 0,
                    amount: 150,
                    createdAt: Date.now(),
                    updatedAt: Date.now(),
                    operations: [
                        {
                            id: UUIDFactory.create(),
                            type: MachineFundsTransferOperationType.Created,
                            triggeredByType: TriggerType.User,
                            triggeredById: 'user1',
                            gamingDay,
                            createdAt: Date.now(),
                            triggeredAt: Date.now(),
                            machineFundsTransferId: UUIDFactory.create()
                        },
                        {
                            id: UUIDFactory.create(),
                            type: MachineFundsTransferOperationType.Completed,
                            triggeredByType: TriggerType.Machine,
                            triggeredById: 'machine',
                            gamingDay,
                            createdAt: Date.now(),
                            triggeredAt: Date.now(),
                            machineFundsTransferId: UUIDFactory.create()
                        }
                    ]
                },
                // Manually completed transfer
                {
                    id: UUIDFactory.create(),
                    machineId,
                    type: MachineFundsTransferType.WalletTransferToMachine,
                    status: MachineFundsTransferStatus.Completed,
                    cashableAmount: 200,
                    restrictedAmount: 75,
                    unrestrictedAmount: 0,
                    amount: 275,
                    createdAt: Date.now(),
                    updatedAt: Date.now(),
                    operations: [
                        {
                            id: UUIDFactory.create(),
                            type: MachineFundsTransferOperationType.Created,
                            triggeredByType: TriggerType.User,
                            triggeredById: 'user1',
                            gamingDay,
                            createdAt: Date.now(),
                            triggeredAt: Date.now(),
                            machineFundsTransferId: UUIDFactory.create()
                        },
                        {
                            id: UUIDFactory.create(),
                            type: MachineFundsTransferOperationType.CompletedManual,
                            triggeredByType: TriggerType.User,
                            triggeredById: 'user2',
                            gamingDay,
                            createdAt: Date.now(),
                            triggeredAt: Date.now(),
                            machineFundsTransferId: UUIDFactory.create()
                        }
                    ]
                },
                // Another manually completed transfer (from machine)
                {
                    id: UUIDFactory.create(),
                    machineId,
                    type: MachineFundsTransferType.ManualTransferOut,
                    status: MachineFundsTransferStatus.Completed,
                    cashableAmount: 150,
                    restrictedAmount: 25,
                    unrestrictedAmount: 0,
                    amount: 175,
                    createdAt: Date.now(),
                    updatedAt: Date.now(),
                    operations: [
                        {
                            id: UUIDFactory.create(),
                            type: MachineFundsTransferOperationType.Created,
                            triggeredByType: TriggerType.User,
                            triggeredById: 'user1',
                            gamingDay,
                            createdAt: Date.now(),
                            triggeredAt: Date.now(),
                            machineFundsTransferId: UUIDFactory.create()
                        },
                        {
                            id: UUIDFactory.create(),
                            type: MachineFundsTransferOperationType.CompletedManual,
                            triggeredByType: TriggerType.User,
                            triggeredById: 'user3',
                            gamingDay,
                            createdAt: Date.now(),
                            triggeredAt: Date.now(),
                            machineFundsTransferId: UUIDFactory.create()
                        }
                    ]
                }
            ];

            // WHEN
            const result = service.calculateMachineFundsMetric(machineFunds);

            // THEN
            expect(result).toBeDefined();

            // Main amounts (only regular transfers, excluding manually completed)
            expect(result.cashableAmountToMachine).toBe(100); // Only the regular ManualTransferIn
            expect(result.restrictedAmountToMachine).toBe(50); // Only the regular ManualTransferIn
            expect(result.cashableAmountFromMachine).toBe(0); // No regular transfers from machine
            expect(result.restrictedAmountFromMachine).toBe(0); // No regular transfers from machine

            // Adjustment amounts (only manually completed transfers)
            expect(result.cashableAmountToMachineAdjustment).toBe(200); // Only the WalletTransferToMachine
            expect(result.restrictedAmountToMachineAdjustment).toBe(75); // Only the WalletTransferToMachine
            expect(result.cashableAmountFromMachineAdjustment).toBe(150); // Only the ManualTransferOut
            expect(result.restrictedAmountFromMachineAdjustment).toBe(25); // Only the ManualTransferOut
        });

        it('should have zero adjustment properties when no transfers are manually completed', () => {
            // GIVEN
            const machineId = UUIDFactory.create();
            const gamingDay = '2025-01-30';

            const machineFunds: MachineFundsTransfer[] = [
                {
                    id: UUIDFactory.create(),
                    machineId,
                    type: MachineFundsTransferType.ManualTransferIn,
                    status: MachineFundsTransferStatus.Completed,
                    cashableAmount: 100,
                    restrictedAmount: 50,
                    unrestrictedAmount: 0,
                    amount: 150,
                    createdAt: Date.now(),
                    updatedAt: Date.now(),
                    operations: [
                        {
                            id: UUIDFactory.create(),
                            type: MachineFundsTransferOperationType.Completed,
                            triggeredByType: TriggerType.Machine,
                            triggeredById: 'machine',
                            gamingDay,
                            createdAt: Date.now(),
                            triggeredAt: Date.now(),
                            machineFundsTransferId: UUIDFactory.create()
                        }
                    ]
                }
            ];

            // WHEN
            const result = service.calculateMachineFundsMetric(machineFunds);

            // THEN
            expect(result.cashableAmountToMachine).toBe(100);
            expect(result.restrictedAmountToMachine).toBe(50);

            // All adjustment properties should be zero
            expect(result.cashableAmountToMachineAdjustment).toBe(0);
            expect(result.restrictedAmountToMachineAdjustment).toBe(0);
            expect(result.cashableAmountFromMachineAdjustment).toBe(0);
            expect(result.restrictedAmountFromMachineAdjustment).toBe(0);
        });
    });
});
