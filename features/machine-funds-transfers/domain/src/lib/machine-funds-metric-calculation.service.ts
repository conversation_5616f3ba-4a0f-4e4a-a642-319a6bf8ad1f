import {
    MachineFundsTransfer,
    MachineFundsTransferMetrics,
    MachineFundsTransferOperationType,
    MachineFundsTransferType
} from '@cms/machine-funds-transfers';
import { Injectable } from '@nestjs/common';

@Injectable()
export class MachineFundsMetricCalculationService {
    calculateMachineFundsMetric(machineFunds: MachineFundsTransfer[]): MachineFundsTransferMetrics {
        const fundsToMachine = machineFunds.filter((mft) =>
            [
                MachineFundsTransferType.ManualTransferIn,
                MachineFundsTransferType.WalletTransferToMachine,
                MachineFundsTransferType.AftBonusTransfer,
                MachineFundsTransferType.AftBonusHandpay
            ].includes(mft.type)
        );
        const fundsFromMachine = machineFunds.filter((mft) =>
            [MachineFundsTransferType.ManualTransferOut, MachineFundsTransferType.WalletTransferFromMachine].includes(mft.type)
        );

        // Helper function to check if a transfer was manually completed
        const isManuallyCompleted = (mft: MachineFundsTransfer): boolean => {
            return mft.operations?.some((op) => op.type === MachineFundsTransferOperationType.CompletedManual) ?? false;
        };

        // Separate manually completed transfers for adjustments
        const manuallyCompletedFundsToMachine = fundsToMachine.filter(isManuallyCompleted);
        const manuallyCompletedFundsFromMachine = fundsFromMachine.filter(isManuallyCompleted);

        // Regular transfers (excluding manually completed ones)
        const regularFundsToMachine = fundsToMachine.filter((mft) => !isManuallyCompleted(mft));
        const regularFundsFromMachine = fundsFromMachine.filter((mft) => !isManuallyCompleted(mft));

        const cashableAmountToMachine = regularFundsToMachine.reduce((acc, mft) => {
            return acc + mft.cashableAmount;
        }, 0);
        const cashableAmountFromMachine = regularFundsFromMachine.reduce((acc, mft) => {
            return acc + mft.cashableAmount;
        }, 0);

        const restrictedAmountToMachine = regularFundsToMachine.reduce((acc, mft) => {
            return acc + mft.restrictedAmount;
        }, 0);

        const restrictedAmountFromMachine = regularFundsFromMachine.reduce((acc, mft) => {
            return acc + mft.restrictedAmount;
        }, 0);

        // Calculate adjustment amounts for manually completed transfers
        const cashableAmountToMachineAdjustment = manuallyCompletedFundsToMachine.reduce((acc, mft) => {
            return acc + mft.cashableAmount;
        }, 0);

        const cashableAmountFromMachineAdjustment = manuallyCompletedFundsFromMachine.reduce((acc, mft) => {
            return acc + mft.cashableAmount;
        }, 0);

        const restrictedAmountToMachineAdjustment = manuallyCompletedFundsToMachine.reduce((acc, mft) => {
            return acc + mft.restrictedAmount;
        }, 0);

        const restrictedAmountFromMachineAdjustment = manuallyCompletedFundsFromMachine.reduce((acc, mft) => {
            return acc + mft.restrictedAmount;
        }, 0);

        return MachineFundsTransferMetrics.create({
            cashableAmountFromMachine,
            restrictedAmountToMachine,
            cashableAmountToMachine,
            restrictedAmountFromMachine,
            cashableAmountToMachineAdjustment,
            cashableAmountFromMachineAdjustment,
            restrictedAmountToMachineAdjustment,
            restrictedAmountFromMachineAdjustment
        });
    }
}
