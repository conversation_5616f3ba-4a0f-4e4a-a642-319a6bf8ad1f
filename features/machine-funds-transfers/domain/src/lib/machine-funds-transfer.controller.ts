import { ListEntityAuditQuery, ListEntityAuditQueryResult, ListEntityAuditResponse } from '@cms/entity-audit';
import { MachineFundsTransfer, MachineFundsTransferOperation, MachineFundsTransferType } from '@cms/machine-funds-transfers';
import { MessageBroker } from '@cms/message-broker';
import { CmsIdentity, Identity } from '@cms/server-authentication';
import { CmsPermissions } from '@cms/server-permissions';
import { Body, Controller, Get, Post, Put, Query } from '@nestjs/common';
import { ApiOAuth2, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { TgPaginationRequestDto } from '@tronius/backend-common';
import { TgPaginationResponse } from '@tronius/shared-common';
import { CmsAction } from '@tronius/shared-domain';

import { CancelMachineFundsTransferDto } from './dto/cancel-machine-funds-transfer.dto';
import { CompleteMachineFundsTransferDto } from './dto/complete-machine-funds-transfer.dto';
import { CreateManualTransferInDto } from './dto/create-machine-funds-transfer.dto';
import { MachineFundsTransferOperationService } from './machine-funds-transfer-operation.service';
import { MachineFundsTransferService } from './machine-funds-transfer.service';

@ApiTags('Machine Funds Transfers')
@ApiOAuth2([])
@Controller('machine-funds-transfers')
export class MachineFundsTransferController {
    constructor(
        private readonly service: MachineFundsTransferService,
        private readonly operationService: MachineFundsTransferOperationService,
        private readonly messageBroker: MessageBroker
    ) {}

    @Get()
    @CmsPermissions(CmsAction.MachineFundsTransferList)
    async list(@Query() query: TgPaginationRequestDto<MachineFundsTransfer>): Promise<TgPaginationResponse<MachineFundsTransfer>> {
        return this.service.list(query);
    }

    @Get('/operations')
    @CmsPermissions(CmsAction.MachineFundTransferOperationsList)
    async listOperations(
        @Query() query: TgPaginationRequestDto<MachineFundsTransferOperation>
    ): Promise<TgPaginationResponse<MachineFundsTransferOperation>> {
        return this.operationService.list(query);
    }

    @Post('/create/manual-transfer-in')
    @CmsPermissions(CmsAction.MachineFundsTransferCreateManualTransferIn)
    async createManualTransferIn(
        @Body() body: CreateManualTransferInDto,
        @Identity() { username }: CmsIdentity
    ): Promise<MachineFundsTransfer> {
        return this.service.createManualTransfer({ ...body, type: MachineFundsTransferType.ManualTransferIn }, username);
    }

    @Put('/cancel')
    @CmsPermissions(CmsAction.MachineFundsTransferCancel)
    async cancelMachineFundsTransfer(@Body() body: CancelMachineFundsTransferDto, @Identity() { username }: CmsIdentity): Promise<void> {
        return this.service.cancel(body.id, username);
    }

    @Put('/complete')
    @CmsPermissions(CmsAction.MachineFundsTransferComplete)
    async completeMachineFundsTransfer(
        @Body() completeMachineFundsTransfer: CompleteMachineFundsTransferDto,
        @Identity() { username }: CmsIdentity
    ): Promise<void> {
        return this.service.markAftInAsManuallyCompleted(completeMachineFundsTransfer, username);
    }

    @Get('/audit')
    @CmsPermissions(CmsAction.MachineFundsTransferList)
    @ApiOperation({ summary: 'List machine funds transfer audit' })
    @ApiQuery({ name: 'next', type: String, required: false })
    @ApiQuery({ name: 'count', type: Number, required: false })
    @ApiQuery({ name: 'entityId', type: String, required: false })
    @ApiQuery({ name: 'actor', type: String, required: false })
    async listAudit(
        @Query('next') next?: string,
        @Query('count') count?: string,
        @Query('entityId') entityId?: string,
        @Query('actor') actor?: string
    ): Promise<ListEntityAuditResponse<MachineFundsTransfer>> {
        const { result: response } = await this.messageBroker.query(
            ListEntityAuditQuery.create({
                request: {
                    entityName: 'MachineFundsTransfer',
                    next,
                    count: count as unknown as number,
                    entityId,
                    actor
                }
            }),
            ListEntityAuditQueryResult<MachineFundsTransfer>
        );

        return response;
    }
}
