import { CompleteMachineFundsTransfer } from '@cms/machine-funds-transfers';
import { IsGamingDay, IsNotEmptyString } from '@cms/validation';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsOptional, IsUUID } from 'class-validator';

export class CompleteMachineFundsTransferDto implements CompleteMachineFundsTransfer {
    @ApiProperty({
        type: 'string',
        format: 'uuid',
        required: true
    })
    @IsUUID()
    id!: string;

    @IsGamingDay()
    @ApiProperty({
        type: 'string',
        required: true,
        example: '2025-01-30'
    })
    gamingDay!: string;

    @ApiPropertyOptional({
        type: 'string'
    })
    @IsOptional()
    @IsNotEmptyString()
    comment?: string | undefined;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        required: true
    })
    @Type(() => Date)
    @IsDate()
    accountingDate!: Date;
}
