import { AuditEventProps, EntityAuditModule } from '@cms/entity-audit-domain';
import {
    MachineFundsTransfer,
    MachineFundsTransferCanceledEvent,
    MachineFundsTransferCompletedEvent,
    MachineFundsTransferCreatedEvent,
    MachineFundsTransferFailedEvent
} from '@cms/machine-funds-transfers';
import { TypeOrmModule } from '@cms/typeorm';
import { Module } from '@nestjs/common';
import { MachineFundsTransferEntity, MachineFundsTransferOperationEntity } from '@tronius/backend-database';

import { MachineFundsTransferOperationDao } from './dao/machine-funds-transfer-operation.dao';
import { MachineFundsTransferDao } from './dao/machine-funds-transfer.dao';
import {
    CreateManualFundsTransferCommandHandler,
    JackpotWonEventHandler,
    MachineFundsTransferCompletedByMachineEventHandler,
    MachineFundsTransferFailedByMachineEvent<PERSON><PERSON><PERSON>,
    ManuallyCompleteMachineFundsTransferCommandHandler
} from './handlers';
import { GetMachineFundsTransferMetricForGamingDayQueryHandler } from './handlers/get-machine-funds-transfer-metric-for-gaming-day.query.handler';
import { GetMachineFundsTransferMetricForPeriodQueryHandler } from './handlers/get-machine-funds-transfer-metric-for-period.query.handler';
import { MachineFundsMetricCalculationService } from './machine-funds-metric-calculation.service';
import { machineFundsTransferAuditEntityTransformer } from './machine-funds-transfer-audit-entity.transformer';
import { MachineFundsTransferOperationService } from './machine-funds-transfer-operation.service';
import { MachineFundsTransferController } from './machine-funds-transfer.controller';
import { MachineFundsTransferService } from './machine-funds-transfer.service';

@Module({
    imports: [
        TypeOrmModule.forFeature([MachineFundsTransferEntity, MachineFundsTransferOperationEntity]),
        EntityAuditModule.forEntity<MachineFundsTransfer>({
            entity: 'MachineFundsTransfer',
            auditEvents: [
                AuditEventProps.create<MachineFundsTransferCreatedEvent, MachineFundsTransfer>({
                    event: MachineFundsTransferCreatedEvent,
                    entityIdentifier: (event) => event.machineFundsTransfer.id,
                    actorIdentifier: (event) => event.triggeredBy,
                    entityPayloadIdentifier: (event) => event.machineFundsTransfer,
                    transformer: machineFundsTransferAuditEntityTransformer
                }),
                AuditEventProps.create<MachineFundsTransferFailedEvent, MachineFundsTransfer>({
                    event: MachineFundsTransferFailedEvent,
                    entityIdentifier: (event) => event.machineFundsTransfer.id,
                    actorIdentifier: (event) => event.username,
                    entityPayloadIdentifier: (event) => event.machineFundsTransfer,
                    transformer: machineFundsTransferAuditEntityTransformer
                }),
                AuditEventProps.create<MachineFundsTransferCompletedEvent, MachineFundsTransfer>({
                    event: MachineFundsTransferCompletedEvent,
                    entityIdentifier: (event) => event.machineFundsTransfer.id,
                    actorIdentifier: (event) => event.username,
                    entityPayloadIdentifier: (event) => event.machineFundsTransfer,
                    transformer: machineFundsTransferAuditEntityTransformer
                }),
                AuditEventProps.create<MachineFundsTransferCanceledEvent, MachineFundsTransfer>({
                    event: MachineFundsTransferCanceledEvent,
                    entityIdentifier: (event) => event.machineFundsTransfer.id,
                    actorIdentifier: (event) => event.username,
                    entityPayloadIdentifier: (event) => event.machineFundsTransfer,
                    transformer: machineFundsTransferAuditEntityTransformer
                })
            ]
        })
    ],
    controllers: [MachineFundsTransferController],
    providers: [
        CreateManualFundsTransferCommandHandler,
        JackpotWonEventHandler,
        MachineFundsTransferCompletedByMachineEventHandler,
        MachineFundsTransferFailedByMachineEventHandler,
        ManuallyCompleteMachineFundsTransferCommandHandler,
        MachineFundsTransferDao,
        MachineFundsTransferOperationDao,
        MachineFundsTransferOperationService,
        MachineFundsTransferService,
        MachineFundsMetricCalculationService,
        GetMachineFundsTransferMetricForPeriodQueryHandler,
        GetMachineFundsTransferMetricForGamingDayQueryHandler
    ]
})
export class MachineFundsTransferModule {}
