import { ManuallyCompleteMachineFundsTransferCommand } from '@cms/machine-funds-transfers';
import { Command<PERSON><PERSON><PERSON>, OnCommand } from '@cms/message-broker';
import { Injectable } from '@nestjs/common';
import { CommandResult } from '@tronius/shared-common';

import { MachineFundsTransferService } from '../machine-funds-transfer.service';

@Injectable()
@OnCommand(ManuallyCompleteMachineFundsTransferCommand)
export class ManuallyCompleteMachineFundsTransferCommandHandler implements CommandHandler<ManuallyCompleteMachineFundsTransferCommand> {
    constructor(private readonly machineFundsTransferService: MachineFundsTransferService) {}

    async handle({
        correlationId,
        requestedAt: _requestedAt,
        executionContext: _executionContext,
        username,
        ...completeMachineFundsTransfer
    }: ManuallyCompleteMachineFundsTransferCommand): Promise<CommandResult> {
        await this.machineFundsTransferService.markAftInAsManuallyCompleted(completeMachineFundsTransfer, username);

        return CommandResult.create({ correlationId });
    }
}
