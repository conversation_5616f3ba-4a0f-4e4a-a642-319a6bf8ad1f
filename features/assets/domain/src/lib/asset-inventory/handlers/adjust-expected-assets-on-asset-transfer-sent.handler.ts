import { AdjustmentOperation, AssetFlowStatus, AssetTransferSentEvent } from '@cms/assets';
import { EventHandler, OnEvent } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import { ErrorUtils } from '@tronius/shared-common';

import { AssetInventoryService } from '../asset-inventory.service';
import { ChangeExpectedAssetsProps } from '../dao/asset-inventory.dao';

@OnEvent(AssetTransferSentEvent)
@Injectable()
export class AdjustExpectedAssetsOnAssetTransferSentHandler implements EventHandler<AssetTransferSentEvent> {
    private readonly logger = new Logger(AdjustExpectedAssetsOnAssetTransferSentHandler.name);

    constructor(private readonly assetInventoryService: AssetInventoryService) {}

    async handle(event: AssetTransferSentEvent): Promise<void> {
        this.logger.log(`Received AssetTransferSentEvent for transferId: ${event.updatedAssetTransfer.id}`);
        this.logger.debug(`Event payload: ${JSON.stringify(event)}`);

        const {
            updatedAssetTransfer: { assetFlowStatus, assetInventoryId, totalCash, denominationBreakdownValues, totalChips, totalOther }
        } = event;

        if (assetFlowStatus !== AssetFlowStatus.Completed) {
            this.logger.log(`Asset flow status is not ${AssetFlowStatus.Completed}. Skipping adjustment.`);
            return;
        }

        this.logger.log(
            `Asset flow status is ${AssetFlowStatus.Completed}. Preparing adjustment for assetInventoryId: ${assetInventoryId}`
        );

        const adjustmentProps: ChangeExpectedAssetsProps = {
            assetInventoryId,
            operation: AdjustmentOperation.Decrease,
            changeTotalCash: totalCash,
            changeDenominationBreakdownValues: denominationBreakdownValues,
            changeTotalChips: totalChips,
            changeTotalOther: totalOther
        };

        this.logger.debug(`Adjustment props: ${JSON.stringify(adjustmentProps)}`);

        await this.assetInventoryService.adjustExpectedAssets(adjustmentProps);

        this.logger.log(`Successfully adjusted expected assets for assetInventoryId: ${adjustmentProps.assetInventoryId}`);
    }

    onError(error: unknown): void {
        this.logger.error(`AdjustExpectedAssetsOnAssetTransferSentHandler error: ${ErrorUtils.errorToString(error)}`);
    }
}
