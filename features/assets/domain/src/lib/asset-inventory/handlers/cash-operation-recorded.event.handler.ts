import { AssetFlowStatus, AssetFlowType, AssetInventoryMode, CreateAssetTransfer, CreateAssetsTransferCommand } from '@cms/assets';
import { CashOperationRecordedEvent, CashOperationType } from '@cms/cash-operations';
import { EventHand<PERSON>, MessageBroker, OnEvent } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import { ErrorUtils, NotFoundError, ProfitCenterType } from '@tronius/shared-common';

import { AssetInventoryService } from '../asset-inventory.service';

@Injectable()
@OnEvent(CashOperationRecordedEvent)
export class CashOperationRecordedEventHandler implements EventHandler<CashOperationRecordedEvent> {
    private readonly logger = new Logger(CashOperationRecordedEventHandler.name);

    constructor(
        private readonly assetInventoryService: AssetInventoryService,
        private readonly messageBroker: MessageBroker
    ) {}

    async handle(event: CashOperationRecordedEvent): Promise<void> {
        const {
            cashOperation: { profitCenterId, profitCenterType, cashOperationType, totalAmount, denominationBreakdownValues }
        } = event;

        this.logger.log(`Processing cash operation for profit center ${profitCenterId}`);

        const inventoryMode = profitCenterType === ProfitCenterType.Machine ? AssetInventoryMode.Active : AssetInventoryMode.Main;

        const assetInventory = await this.assetInventoryService.getAssetInventoryByProfitCenterIdAndMode(profitCenterId, inventoryMode);

        if (!assetInventory) {
            throw new NotFoundError({
                message: 'Asset Inventory not found for profitCenterId in specified mode',
                params: { profitCenterId, assetInventoryMode: inventoryMode }
            });
        }

        const isIncrease =
            cashOperationType === CashOperationType.CashAccepted || cashOperationType === CashOperationType.ReverseCashIssued;

        const assetFlowType = isIncrease ? AssetFlowType.In : AssetFlowType.Out;

        this.logger.debug(
            `Creating asset transfer for profit center ${profitCenterId} (${profitCenterType}) with total cash: ${totalAmount}, flow type: ${assetFlowType}, and denomination breakdown: ${JSON.stringify(denominationBreakdownValues)}`
        );

        const transfer: CreateAssetTransfer = {
            profitCenterId,
            assetInventoryId: assetInventory.id,
            assetFlowType,
            assetFlowStatus: AssetFlowStatus.Requested,
            totalCash: totalAmount,
            totalChips: 0,
            totalOther: 0,
            denominationBreakdownValues: denominationBreakdownValues ?? []
        };

        const username = 'AssetsDomainModule'; // Change to use constants.ts after re-base
        const command = CreateAssetsTransferCommand.create({ assetTransfer: transfer, username });

        await this.messageBroker.command(command);

        this.logger.log(`Asset transfer command sent for profit center ${profitCenterId}`);
    }

    onError(error: unknown): void {
        this.logger.error(`CashOperationRecordedEventHandler error: ${ErrorUtils.errorToString(error)}`);
    }
}
