import {
    Asset<PERSON>lowStatus,
    Asset<PERSON>lowType,
    AssetInventoryMode,
    AssetsMovedAfterMachineClearedEvent,
    CreateAssetTransfer,
    CreateAssetsTransferCommand
} from '@cms/assets';
import { EventHandler, MessageBroker, OnEvent } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import { ErrorUtils } from '@tronius/shared-common';
import { MachineMetersCollectedEvent, MeterSnapshotContext } from '@tronius/shared-domain';

import { assetsDomainModuleName } from '../../constants';
import { AssetInventoryService } from '../asset-inventory.service';

@Injectable()
@OnEvent(MachineMetersCollectedEvent)
export class MoveAssetsOnMachineClearedHandler implements EventHandler<MachineMetersCollectedEvent> {
    private readonly logger = new Logger(MoveAssetsOnMachineClearedHandler.name);

    constructor(
        private readonly assetInventoryService: AssetInventoryService,
        private readonly messageBroker: MessageBroker
    ) {}

    async handle(event: MachineMetersCollectedEvent): Promise<void> {
        const { machineId, collections } = event;

        if (collections.length === 0) {
            this.logger.debug(`No collections found for machine ${machineId}. Skipping.`);
            return;
        }

        const hasClearanceContext = collections.some((collection) => collection.context === MeterSnapshotContext.Clearance);

        if (!hasClearanceContext) {
            this.logger.debug(
                `Machine ${machineId} has no collections with ${MeterSnapshotContext.Clearance} context. Skipping asset inventory movement.`
            );
            return;
        }

        this.logger.log(
            `Machine ${machineId} has a collection with ${MeterSnapshotContext.Clearance} context. Starting asset inventory movement...`
        );

        const assetInventories = await this.assetInventoryService.getAllAssetInventoriesByProfitCenterId(machineId);

        const activeInventory = assetInventories.find((inventory) => inventory.assetInventoryMode === AssetInventoryMode.Active);
        const clearedInventory = assetInventories.find((inventory) => inventory.assetInventoryMode === AssetInventoryMode.Cleared);

        if (!activeInventory || !clearedInventory) {
            this.logger.warn(`Machine ${machineId} must have both Active and Cleared inventories, but one or both are missing.`);
            return;
        }

        this.logger.log(
            `Found ${activeInventory.assetInventoryMode} Inventory: ${activeInventory.id}, ${clearedInventory.assetInventoryMode} Inventory: ${clearedInventory.id} for machine ${machineId}`
        );

        const {
            expectedTotalCash,
            expectedTotalChips,
            expectedTotalOther,
            expectedDenominationBreakdownValues: denominationBreakdownValues,
            profitCenterId: activeProfitCenterId,
            id: activeInventoryId
        } = activeInventory;

        const { id: clearedInventoryId, profitCenterId: clearedProfitCenterId } = clearedInventory;

        const username = assetsDomainModuleName;

        const sendTransfer: CreateAssetTransfer = {
            profitCenterId: activeProfitCenterId,
            assetInventoryId: activeInventoryId,
            assetFlowType: AssetFlowType.Out,
            assetFlowStatus: AssetFlowStatus.Requested,
            totalCash: expectedTotalCash,
            totalChips: expectedTotalChips,
            totalOther: expectedTotalOther,
            denominationBreakdownValues
        };

        const receiveTransfer: CreateAssetTransfer = {
            profitCenterId: clearedProfitCenterId,
            assetInventoryId: clearedInventoryId,
            assetFlowType: AssetFlowType.In,
            assetFlowStatus: AssetFlowStatus.Requested,
            totalCash: expectedTotalCash,
            totalChips: expectedTotalChips,
            totalOther: expectedTotalOther,
            denominationBreakdownValues
        };

        const sendCommand = CreateAssetsTransferCommand.create({ assetTransfer: sendTransfer, username });
        const receiveCommand = CreateAssetsTransferCommand.create({ assetTransfer: receiveTransfer, username });

        await this.messageBroker.command(sendCommand);
        await this.messageBroker.command(receiveCommand);

        this.logger.log(
            `Asset transfer commands for machine ${machineId} sent successfully. From (${activeInventory.assetInventoryMode}): ${activeInventory.id} → To (${clearedInventory.assetInventoryMode}): ${clearedInventory.id}`
        );

        await this.messageBroker.publish(
            AssetsMovedAfterMachineClearedEvent,
            AssetsMovedAfterMachineClearedEvent.create({
                machineId,
                assetInventoryId: clearedInventory.id
            })
        );
    }

    onError(error: unknown): void {
        this.logger.error(`MoveAssetsOnMachineClearedHandler error: ${ErrorUtils.errorToString(error)}`);
    }
}
