import { TypeOrmModule } from '@cms/typeorm';
import { Module } from '@nestjs/common';
import {
    AssetInventoryEntity,
    AssetTransferEntity,
    AssetTransferOperationEntity,
    InventorySnapshotEntity
} from '@tronius/backend-database';

import { AssetInventoryService } from './asset-inventory/asset-inventory.service';
import { AssetInventoryDao } from './asset-inventory/dao/asset-inventory.dao';
import {
    AdjustExpectedAssetsOnAssetTransferReceivedHandler,
    AdjustExpectedAssetsOnAssetTransferSentHandler,
    CashOperationRecordedEventHandler,
    CreateAssetInventoryOnMachineProfileCreatedEventHandler,
    CreateAssetInventoryOnTransactionPointCreatedEventHandler,
    MoveAssetsOnMachineClearedHandler
} from './asset-inventory/handlers';
import { AssetTransferOperationService } from './asset-transfer/asset-transfer-operation.service';
import { AssetTransferService } from './asset-transfer/asset-transfer.service';
import { AssetTransferOperationDao } from './asset-transfer/dao/asset-transfer-operation.dao';
import { AssetTransferDao } from './asset-transfer/dao/asset-transfer.dao';
import {
    CreateAssetInventoriesSnapshotCommandHandler,
    CreateAssetsTransferCommandHandler,
    GetAssetInventoriesByProfitCenterIdQueryHandler,
    GetAssetInventoryOpeningBalanceQueryHandler,
    GetLatestAssetInventorySnapshotQueryHandler,
    GetProfitCenterAssetInventoriesQueryHandler,
    GetProfitCenterAssetsTransferSummaryQueryHandler
} from './handlers';
import { InventorySnapshotDao } from './inventory-snapshot/dao/inventory-snapshot.dao';
import { InventorySnapshotService } from './inventory-snapshot/inventory-snapshot.service';

@Module({
    imports: [TypeOrmModule.forFeature([AssetInventoryEntity, InventorySnapshotEntity, AssetTransferEntity, AssetTransferOperationEntity])],
    controllers: [],
    providers: [
        AssetInventoryService,
        AssetInventoryDao,
        InventorySnapshotService,
        InventorySnapshotDao,
        AssetTransferService,
        AssetTransferDao,
        AssetTransferOperationService,
        AssetTransferOperationDao,
        GetAssetInventoryOpeningBalanceQueryHandler,
        GetProfitCenterAssetInventoriesQueryHandler,
        CreateAssetInventoriesSnapshotCommandHandler,
        GetLatestAssetInventorySnapshotQueryHandler,
        GetAssetInventoriesByProfitCenterIdQueryHandler,
        CreateAssetsTransferCommandHandler,
        GetProfitCenterAssetsTransferSummaryQueryHandler,
        CashOperationRecordedEventHandler,
        CreateAssetInventoryOnTransactionPointCreatedEventHandler,
        CreateAssetInventoryOnMachineProfileCreatedEventHandler,
        AdjustExpectedAssetsOnAssetTransferReceivedHandler,
        AdjustExpectedAssetsOnAssetTransferSentHandler,
        MoveAssetsOnMachineClearedHandler
    ],
    exports: []
})
export class AssetsDomainModule {}
