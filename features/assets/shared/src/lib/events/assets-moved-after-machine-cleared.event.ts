import { deserialize } from '@cms/serialization';
import { BaseEvent } from '@tronius/shared-common';
import { IsUUID } from 'class-validator';

export interface AssetsMovedAfterMachineClearedEventProps {
    readonly machineId: string;
    readonly assetInventoryId: string;
}

export class AssetsMovedAfterMachineClearedEvent extends BaseEvent implements AssetsMovedAfterMachineClearedEventProps {
    /**
     * The UUID of the machine that has been cleared.
     */
    @IsUUID()
    readonly machineId!: string;

    /**
     * The UUID of the inventory where the machine's assets are moved after clearance.
     */
    @IsUUID()
    readonly assetInventoryId!: string;

    protected constructor() {
        super();
    }

    static create(props: AssetsMovedAfterMachineClearedEventProps): AssetsMovedAfterMachineClearedEvent {
        return deserialize(AssetsMovedAfterMachineClearedEvent, props);
    }
}
