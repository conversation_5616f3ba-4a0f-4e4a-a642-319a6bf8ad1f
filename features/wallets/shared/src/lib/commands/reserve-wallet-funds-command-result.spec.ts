import { UUIDFactory } from '@tronius/shared-common';

import { ReserveWalletFundsCommandResult, ReserveWalletFundsCommandResultProps } from './reserve-wallet-funds-command-result';

describe('ReserveWalletFundsCommandResult', () => {
    it('should map valid props to a valid ReserveWalletFundsCommandResult entity', () => {
        const props: ReserveWalletFundsCommandResultProps = {
            reservationId: '123e4567-e89b-12d3-a456-************',
            correlationId: UUIDFactory.create()
        };

        const result = ReserveWalletFundsCommandResult.create(props);

        expect(result).toBeInstanceOf(ReserveWalletFundsCommandResult);
        expect(result.reservationId).toBe(props.reservationId);
    });
});
