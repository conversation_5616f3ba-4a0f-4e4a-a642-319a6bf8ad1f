import { deserialize } from '@cms/serialization';
import { CommandResult, CommandResultProps } from '@tronius/shared-common';
import { IsUUID } from 'class-validator';

export interface ReserveWalletFundsCommandResultProps extends CommandResultProps {
    readonly reservationId: string;
}

export class ReserveWalletFundsCommandResult extends CommandResult implements ReserveWalletFundsCommandResultProps {
    @IsUUID()
    readonly reservationId!: string;

    protected constructor() {
        super();
    }

    static override create(props: ReserveWalletFundsCommandResultProps): ReserveWalletFundsCommandResult {
        return deserialize(ReserveWalletFundsCommandResult, props);
    }
}
