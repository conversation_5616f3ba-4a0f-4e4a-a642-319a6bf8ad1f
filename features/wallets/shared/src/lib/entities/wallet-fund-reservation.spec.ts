import { ProfitCenterType, UUIDFactory } from '@tronius/shared-common';

import { ReservationStatus, WalletFundReservation, WalletFundReservationProps } from './wallet-fund-reservation';

describe('WalletFundReservation', () => {
    it('should map from valid props', () => {
        const props: WalletFundReservationProps = {
            id: UUIDFactory.create(),
            walletId: '123e4567-e89b-12d3-a456-************',
            amountInWalletCurrency: 100,
            username: 'test-user',
            profitCenterType: ProfitCenterType.TransactionPoint,
            profitCenterId: '123e4567-e89b-12d3-a456-************',
            status: ReservationStatus.Used,
            reservedAt: new Date()
        };

        const reservation = WalletFundReservation.map(props);

        expect(reservation).toBeInstanceOf(WalletFundReservation);
        expect(reservation.id).toBe(props.id);
        expect(reservation.walletId).toBe(props.walletId);
        expect(reservation.amountInWalletCurrency).toBe(props.amountInWalletCurrency);

        expect(reservation.username).toBe(props.username);
        expect(reservation.profitCenterType).toBe(props.profitCenterType);
        expect(reservation.profitCenterId).toBe(props.profitCenterId);
        expect(reservation.status).toBe(props.status);
        expect(reservation.reservedAt).toEqual(props.reservedAt);
    });
});
