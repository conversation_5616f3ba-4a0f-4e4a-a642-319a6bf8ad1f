import { TransformNumber, deserialize } from '@cms/serialization';
import { IsNotEmptyString, IsPlatformComponentUsername } from '@cms/validation';
import { ProfitCenterType, UUIDFactory } from '@tronius/shared-common';
import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsNumber, IsUUID, Min } from 'class-validator';

export enum ReservationStatus {
    Active = 'Active',
    Used = 'Used'
}

export interface WalletFundReservationProps {
    readonly id: string;
    readonly status: ReservationStatus;
    readonly reservedAt: Date;
    readonly walletId: string;
    readonly amountInWalletCurrency: number;
    readonly username: string;
    readonly profitCenterType: ProfitCenterType;
    readonly profitCenterId: string;
}

export abstract class WalletFundReservation implements WalletFundReservationProps {
    @IsUUID()
    readonly id: string = UUIDFactory.create();

    @IsUUID()
    readonly walletId!: string;

    @IsNumber()
    @TransformNumber()
    @Type(() => Number)
    @Min(0)
    readonly amountInWalletCurrency!: number;

    @IsEnum(ReservationStatus)
    readonly status!: ReservationStatus;

    @IsDate()
    @Type(() => Date)
    readonly reservedAt!: Date;

    @IsPlatformComponentUsername()
    readonly username!: string;

    @IsEnum(ProfitCenterType)
    readonly profitCenterType!: ProfitCenterType;

    @IsNotEmptyString()
    readonly profitCenterId!: string;

    static map(props: WalletFundReservationProps): WalletFundReservation {
        return deserialize(WalletFundReservation, props);
    }
}
