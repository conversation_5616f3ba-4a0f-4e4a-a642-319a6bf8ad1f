import { MessageBroker } from '@cms/message-broker';
import { ReserveWalletFundsProps, WalletFundsReservationUsedEvent, WalletFundsReservedEvent, WalletOperationType } from '@cms/wallets';
import { Injectable } from '@nestjs/common';

import { WalletOperationVerificationService } from './wallet-operation-verification.service';
import { WalletFundReservationEntity } from '../entities';
import { WalletRepositoryPort } from '../repositories';
import { WalletFundReservationListingService } from './wallet-fund-reservation-listing.service';

@Injectable()
export class WalletFundReservationService {
    constructor(
        private readonly walletOperationVerificationService: WalletOperationVerificationService,
        private readonly cashWalletRepository: WalletRepositoryPort,
        private readonly walletFundReservationListingService: WalletFundReservationListingService,
        private readonly messageBroker: MessageBroker
    ) {}

    async reserveFunds({
        ownerId,
        currency,
        amount,
        profitCenterId,
        profitCenterType,
        username,
        walletType: type
    }: ReserveWalletFundsProps): Promise<{ reservationId: string }> {
        const wallet = await this.walletOperationVerificationService.verifyWalletOperation({
            type,
            amount,
            currency,
            ownerId,
            operationType: WalletOperationType.Withdraw
        });

        const reservation = WalletFundReservationEntity.createReservation({
            amountInWalletCurrency: amount,
            walletId: wallet.id,
            profitCenterId,
            profitCenterType,
            username
        });

        await this.cashWalletRepository.saveReservation(reservation);
        await this.messageBroker.publish(WalletFundsReservedEvent, WalletFundsReservedEvent.create({ reservation }));

        return {
            reservationId: reservation.id
        };
    }

    async releaseFundReservation(reservationId: string, usedAmount: number): Promise<void> {
        const reservation = await this.walletFundReservationListingService.getActiveReservationByIdOrThrow(reservationId);

        const updatedReservation = WalletFundReservationEntity.releaseReservation(reservation, usedAmount);
        await this.cashWalletRepository.updateReservationStatus(
            reservationId,
            updatedReservation.status,
            updatedReservation.amountInWalletCurrency
        );

        await this.messageBroker.publish(
            WalletFundsReservationUsedEvent,
            WalletFundsReservationUsedEvent.create({ reservation, updatedReservation })
        );
    }
}
