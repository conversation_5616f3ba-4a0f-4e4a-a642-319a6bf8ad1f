import { MessageBroker } from '@cms/message-broker';
import {
    CashWallet,
    ReservationStatus,
    ReserveWalletFundsProps,
    WalletFundReservation,
    WalletFundsReservationUsedEvent,
    WalletFundsReservedEvent,
    WalletOperationType,
    WalletType
} from '@cms/wallets';
import { Test, TestingModule } from '@nestjs/testing';
import { Currency, ProfitCenterType, UUIDFactory } from '@tronius/shared-common';

import { WalletFundReservationService } from './wallet-fund-reservation.service';
import { VerifyWalletOperation, WalletOperationVerificationService } from './wallet-operation-verification.service';
import { WalletRepositoryPort } from '../repositories';
import { WalletFundReservationListingService } from './wallet-fund-reservation-listing.service';

describe('WalletFundReservationService', () => {
    let service: WalletFundReservationService;
    let mockVerificationService: Partial<WalletOperationVerificationService>;
    let mockRepository: Partial<WalletRepositoryPort>;
    let mockMessageBroker: Partial<MessageBroker>;
    let mockListingService: Partial<WalletFundReservationListingService>;
    let testingModule: TestingModule;

    beforeAll(async () => {
        mockVerificationService = {
            verifyWalletOperation: jest.fn()
        };

        mockRepository = {
            saveReservation: jest.fn(),
            findReservationById: jest.fn(),
            updateReservationStatus: jest.fn()
        };

        mockMessageBroker = {
            publish: jest.fn()
        };

        mockListingService = {
            getActiveReservationByIdOrThrow: jest.fn(),
            getReservationByIdOrThrow: jest.fn()
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                WalletFundReservationService,
                { provide: WalletFundReservationListingService, useValue: mockListingService },
                { provide: WalletOperationVerificationService, useValue: mockVerificationService },
                { provide: WalletRepositoryPort, useValue: mockRepository },
                { provide: MessageBroker, useValue: mockMessageBroker }
            ]
        }).compile();

        service = module.get<WalletFundReservationService>(WalletFundReservationService);
        testingModule = await module.init();
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should reserve funds successfully', async () => {
        const ownerId = UUIDFactory.create();
        const currency = Currency.BSD;
        const amount = 100;
        const profitCenterId = UUIDFactory.create();
        const profitCenterType = ProfitCenterType.Machine;
        const username = 'test-user';

        const cashWallet = CashWallet.map({
            balance: 100,
            reservedFunds: 0,
            currency,
            ownerId: UUIDFactory.create(),
            id: UUIDFactory.create(),
            openedAt: new Date()
        });
        (mockVerificationService.verifyWalletOperation as jest.Mock).mockResolvedValue(cashWallet);

        const reservationProps: ReserveWalletFundsProps = {
            ownerId,
            currency,
            amount,
            profitCenterId,
            profitCenterType,
            username,
            walletType: WalletType.CashWallet
        };

        const result = await service.reserveFunds(reservationProps);

        expect(mockVerificationService.verifyWalletOperation).toHaveBeenCalledWith({
            amount,
            currency,
            ownerId,
            operationType: WalletOperationType.Withdraw,
            type: WalletType.CashWallet
        } as VerifyWalletOperation);

        expect(mockRepository.saveReservation).toHaveBeenCalledWith(
            expect.objectContaining({
                id: result.reservationId,
                amountInWalletCurrency: amount,
                walletId: cashWallet.id,
                status: ReservationStatus.Active
            } as Partial<WalletFundReservation>)
        );
        expect(mockMessageBroker.publish).toHaveBeenCalledWith(WalletFundsReservedEvent, expect.any(Object));
    });

    it('should release funds successfully', async () => {
        const reservationId = UUIDFactory.create();
        const usedAmount = 50;

        const reservation = WalletFundReservation.map({
            id: reservationId,
            amountInWalletCurrency: 100,
            status: ReservationStatus.Active,
            walletId: UUIDFactory.create(),
            profitCenterId: UUIDFactory.create(),
            profitCenterType: ProfitCenterType.Machine,
            username: 'test-user',
            reservedAt: new Date()
        });

        (mockListingService.getActiveReservationByIdOrThrow as jest.Mock).mockResolvedValue(reservation);

        await service.releaseFundReservation(reservationId, usedAmount);

        expect(mockRepository.updateReservationStatus).toHaveBeenCalledWith(reservationId, ReservationStatus.Used, 50);
        expect(mockMessageBroker.publish).toHaveBeenCalledWith(WalletFundsReservationUsedEvent, expect.any(Object));
    });

    afterAll(async () => {
        await testingModule.close();
    });
});
