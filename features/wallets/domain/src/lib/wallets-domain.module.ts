import { Module } from '@nestjs/common';

import {
    CanProcessCashWalletOperationQueryHandler,
    CanProcessFreePlayWalletOperationQueryHandler,
    GetCustomersCashWalletQueryHandler,
    GetCustomersFreePlayWalletQueryHandler,
    ProcessCashWalletOperationCommandHandler,
    ProcessFreePlayWalletOperationCommandHandler,
    ReleaseFundsOnTransactionProcessedEventHandler,
    ReleaseWalletFundsCommandHandler,
    ReserveCashWalletFundsCommandHandler,
    ReserveFreePlayWalletFundsCommandHandler
} from './handlers';
import {
    OpenWalletService,
    WalletFundReservationListingService,
    WalletFundReservationService,
    WalletListingService,
    WalletOperationService,
    WalletOperationVerificationService
} from './services';

@Module({
    providers: [
        OpenWalletService,
        WalletListingService,
        WalletOperationService,
        GetCustomersCashWalletQueryHandler,
        GetCustomersFreePlayWalletQueryHandler,
        WalletOperationVerificationService,
        CanProcessCashWalletOperationQueryHandler,
        CanProcessFreePlayWalletOperationQueryHandler,
        ProcessCashWalletOperationCommandHandler,
        ProcessFreePlayWalletOperationCommandHandler,
        ReserveCashWalletFundsCommandHandler,
        ReserveFreePlayWalletFundsCommandHandler,
        WalletFundReservationService,
        WalletFundReservationListingService,
        ReleaseFundsOnTransactionProcessedEventHandler,
        ReleaseWalletFundsCommandHandler
    ],
    exports: []
})
export class WalletsDomainModule {}
