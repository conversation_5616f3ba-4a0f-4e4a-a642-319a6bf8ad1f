import { ReservationStatus } from '@cms/wallets';
import { ProfitCenterType, UUIDFactory } from '@tronius/shared-common';

import { CreateWalletFundReservationProps, WalletFundReservationEntity } from './wallet-fund-reservation.entity';

describe('WalletFundReservationEntity', () => {
    it('should create an instance with default values', () => {
        const props: CreateWalletFundReservationProps = {
            walletId: UUIDFactory.create(),
            amountInWalletCurrency: 100,
            username: 'admin',
            profitCenterType: ProfitCenterType.TransactionPoint,
            profitCenterId: UUIDFactory.create()
        };

        const reservation = WalletFundReservationEntity.createReservation(props);

        expect(reservation).toBeInstanceOf(WalletFundReservationEntity);
        expect(reservation.id).toBeDefined();
        expect(reservation.reservedAt).toBeInstanceOf(Date);
        expect(reservation.status).toBe(ReservationStatus.Active);
        expect(reservation.walletId).toBe(props.walletId);
        expect(reservation.amountInWalletCurrency).toBe(props.amountInWalletCurrency);

        expect(reservation.username).toBe(props.username);
        expect(reservation.profitCenterType).toBe(props.profitCenterType);
        expect(reservation.profitCenterId).toBe(props.profitCenterId);
    });
});
