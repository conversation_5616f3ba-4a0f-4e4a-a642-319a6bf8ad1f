import { deserialize } from '@cms/serialization';
import { ReservationStatus, WalletFundReservation, WalletFundReservationProps } from '@cms/wallets';
import { ConflictError, InternalServerError, UUIDFactory } from '@tronius/shared-common';

export type CreateWalletFundReservationProps = Omit<WalletFundReservation, 'id' | 'reservedAt' | 'status'>;

export class WalletFundReservationEntity extends WalletFundReservation {
    override readonly id = UUIDFactory.create();

    override readonly reservedAt: Date = new Date();

    override readonly status: ReservationStatus = ReservationStatus.Active;

    protected constructor() {
        super();
    }

    static createReservation(props: CreateWalletFundReservationProps): WalletFundReservation {
        return deserialize(WalletFundReservationEntity, props);
    }

    static releaseReservation(reservation: WalletFundReservation, usedAmount: number): WalletFundReservation {
        if (reservation.status !== ReservationStatus.Active) {
            throw new ConflictError({ message: 'Cannot release a fund reservation that is not active.' });
        }

        if (reservation.amountInWalletCurrency < usedAmount) {
            throw new InternalServerError({ message: 'Cannot release more funds than there were reserved.' });
        }

        const updatedReservation = deserialize(WalletFundReservation, {
            ...reservation,
            amountInWalletCurrency: usedAmount,
            status: ReservationStatus.Used
        } as WalletFundReservationProps);

        return updatedReservation;
    }
}
