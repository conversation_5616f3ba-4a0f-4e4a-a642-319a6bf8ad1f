import { <PERSON><PERSON><PERSON><PERSON>, OnCommand } from '@cms/message-broker';
import { ReserveFreePlayWalletFundsCommand, ReserveWalletFundsCommandResult, WalletType } from '@cms/wallets';
import { Injectable } from '@nestjs/common';

import { WalletFundReservationService } from '../services/wallet-fund-reservation.service';

@Injectable()
@OnCommand(ReserveFreePlayWalletFundsCommand, ReserveWalletFundsCommandResult)
export class ReserveFreePlayWalletFundsCommandHandler
    implements CommandHandler<ReserveFreePlayWalletFundsCommand, ReserveWalletFundsCommandResult>
{
    constructor(private readonly reserveWalletFundsService: WalletFundReservationService) {}

    async handle({
        correlationId,
        executionContext: _executionContext,
        ...command
    }: ReserveFreePlayWalletFundsCommand): Promise<ReserveWalletFundsCommandResult> {
        const { reservationId } = await this.reserveWalletFundsService.reserveFunds({
            ...command,
            walletType: WalletType.FreePlayWallet
        });

        return ReserveWalletFundsCommandResult.create({
            correlationId,
            reservationId
        });
    }
}
