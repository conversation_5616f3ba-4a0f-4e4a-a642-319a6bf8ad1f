import { Command<PERSON><PERSON><PERSON>, OnCommand } from '@cms/message-broker';
import { ReserveCashWalletFundsCommand, ReserveWalletFundsCommandResult, WalletType } from '@cms/wallets';
import { Injectable } from '@nestjs/common';

import { WalletFundReservationService } from '../services/wallet-fund-reservation.service';

@Injectable()
@OnCommand(ReserveCashWalletFundsCommand, ReserveWalletFundsCommandResult)
export class ReserveCashWalletFundsCommandHandler
    implements CommandHandler<ReserveCashWalletFundsCommand, ReserveWalletFundsCommandResult>
{
    constructor(private readonly reserveWalletFundsService: WalletFundReservationService) {}

    async handle({
        correlationId,
        executionContext: _executionContext,
        ...command
    }: ReserveCashWalletFundsCommand): Promise<ReserveWalletFundsCommandResult> {
        const { reservationId } = await this.reserveWalletFundsService.reserveFunds({
            ...command,
            walletType: WalletType.CashWallet
        });

        return ReserveWalletFundsCommandResult.create({
            correlationId,
            reservationId
        });
    }
}
