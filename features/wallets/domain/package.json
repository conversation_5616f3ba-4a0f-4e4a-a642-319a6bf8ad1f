{"name": "@cms/wallets-domain", "version": "0.0.1", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"@nestjs/testing": "^10.3.3", "reflect-metadata": "^0.2.1"}, "peerDependencies": {"@cms/message-broker": "file:../../../libs/backend/message-broker", "@cms/serialization": "0.0.4", "@cms/wallets": "file:../shared", "@nestjs/common": "^10.3.3", "@tronius/shared-common": "file:../../../libs/shared/common"}}