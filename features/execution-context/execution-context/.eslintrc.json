{"extends": ["../../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "parserOptions": {"project": ["features/execution-context/execution-context/tsconfig.*?.json"]}, "rules": {}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}, {"files": ["*.json"], "parser": "jsonc-eslint-parser", "rules": {"@nx/dependency-checks": ["error", {"ignoredFiles": ["{projectRoot}/eslint.config.{js,cjs,mjs}"]}]}}]}