import { HttpStatus, Injectable, Logger, NestMiddleware } from '@nestjs/common';
import { PlainExecutionContextStore, UUIDFactory } from '@tronius/shared-common';
import { NextFunction, Request, Response } from 'express';

import { ExecutionContext } from './execution-context';

@Injectable()
export class ExecutionContextMiddleware implements NestMiddleware {
    private readonly logger = new Logger(ExecutionContextMiddleware.name);

    constructor(private readonly executionContext: ExecutionContext) {}

    use(
        req: Request & {
            requestId: string;
        },
        res: Response,
        next: NextFunction
    ): void {
        const requestStart = Date.now();
        const { method, baseUrl, query } = req;

        const requestId = UUIDFactory.create();

        let ip: string | undefined;

        // Proxies pass the ip in the x-forwarded-for header
        // If there were multiple proxies, the first one is the user's external IP
        const forwardedForHeader = req.headers['x-forwarded-for'];
        if (forwardedForHeader) {
            const forwardedFor = forwardedForHeader as string;
            ip = forwardedFor.split(',')[0]?.trim() as string | undefined;
        }

        if (!ip) {
            ip = req.socket.remoteAddress;
        }

        const context: PlainExecutionContextStore = {
            requestId,
            requestMethod: method,
            requestPath: baseUrl
        };

        if (ip) {
            context.ip = ip;
        }

        if (Object.keys(query).length) {
            context.query = JSON.stringify(query);
        }

        req.once('end', () => {
            const { statusCode } = res;
            const duration = Date.now() - requestStart;

            this.executionContext.set('statusCode', statusCode);
            this.executionContext.set('duration', duration);

            const statusAsHttpStatus = statusCode as HttpStatus;
            const requestEndMessage = `[http] request completed ${method} ${baseUrl}: ${statusCode} - ${duration}ms`;

            if (statusAsHttpStatus >= HttpStatus.INTERNAL_SERVER_ERROR) {
                this.logger.error(requestEndMessage);
                return;
            }

            if (statusAsHttpStatus >= HttpStatus.BAD_REQUEST) {
                this.logger.warn(requestEndMessage);
                return;
            }

            this.logger.log(requestEndMessage);
        });

        void this.executionContext.run(context, () => {
            this.logger.log(`[http] request received ${method} ${baseUrl}`);
            next();
        });
    }
}
