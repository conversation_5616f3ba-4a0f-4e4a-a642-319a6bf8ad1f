import { CallHandler, Injectable, ExecutionContext as NestExecutionContext, NestInterceptor } from '@nestjs/common';
import { DeepPartial, PlainExecutionContextStore } from '@tronius/shared-common';
import { Observable } from 'rxjs';

import { ExecutionContext } from './execution-context';

// We extracted the YClient and MachineData interfaces in order to prevent circular dependencies
type YClientData = DeepPartial<{
    user: { username: string };
    data: { machine: { id: string }; device: { id: string } };
}>;

@Injectable()
export class WsExecutionContextInterceptor implements NestInterceptor {
    constructor(private readonly executionContext: ExecutionContext) {}

    intercept(context: NestExecutionContext, next: CallHandler): Observable<unknown> {
        const { user, data } = context.switchToWs().getClient<YClientData>();

        const currentExecutionContext = this.executionContext.serialize();

        const executionStore: PlainExecutionContextStore = {
            ...currentExecutionContext
        };

        if (user?.username) {
            executionStore.username = user.username;
        }

        if (data?.machine?.id) {
            executionStore.machineId = data.machine.id;
        }

        if (data?.device?.id) {
            executionStore.deviceId = data.device.id;
        }

        return new Observable((subscriber) => {
            void this.executionContext.run(executionStore, async () => {
                return new Promise<void>((resolve) => {
                    next.handle().subscribe({
                        next: (value) => {
                            subscriber.next(value);
                        },
                        error: (error) => {
                            subscriber.error(error);
                            resolve();
                        },
                        complete: () => {
                            subscriber.complete();
                            resolve();
                        }
                    });
                });
            });
        });
    }
}
