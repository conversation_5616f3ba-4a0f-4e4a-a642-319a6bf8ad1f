import { DynamicModule, MiddlewareConsumer, NestModule } from '@nestjs/common';

import { ExecutionContext } from './execution-context';
import { ExecutionContextMiddleware } from './execution-context.middleware';

export class ExecutionContextModule implements NestModule {
    configure(consumer: MiddlewareConsumer): void {
        consumer.apply(ExecutionContextMiddleware).forRoutes('*');
    }

    static forRoot(): DynamicModule {
        return {
            module: ExecutionContextModule,
            providers: [ExecutionContext],
            exports: [ExecutionContext],
            global: true
        };
    }
}
