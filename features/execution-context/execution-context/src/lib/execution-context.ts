import { AsyncLocalStorage } from 'node:async_hooks';

import { Injectable, Logger } from '@nestjs/common';
import { ExecutionContextStore, ExecutionContextValue, PlainExecutionContextStore } from '@tronius/shared-common';
import { Observable } from 'rxjs';

@Injectable()
export class ExecutionContext {
    private readonly asyncLocalStorage = new AsyncLocalStorage<Map<string, ExecutionContextValue>>();

    private readonly logger = new Logger(ExecutionContext.name);

    getStore(warningOnNoActiveContext = true): ExecutionContextStore {
        const store = this.asyncLocalStorage.getStore();

        if (!store) {
            if (warningOnNoActiveContext) {
                this.logger.warn('ExecutionContext not available');
            }
            return new Map();
        }

        return store;
    }

    set(key: string, value: ExecutionContextValue | undefined): void {
        if (!value) {
            this.getStore().delete(key);
            return;
        }

        this.getStore().set(key, value);
    }

    get(key: string): ExecutionContextValue | undefined {
        return this.getStore().get(key);
    }

    clear(key: string): void {
        this.getStore().delete(key);
    }

    assign(context: PlainExecutionContextStore): void {
        Object.entries(context).forEach(([key, value]) => {
            this.set(key, value);
        });
    }

    run<R, TArgs extends unknown[]>(context: PlainExecutionContextStore, callback: (...args: TArgs) => R, ...args: TArgs): Promise<R> | R {
        const store = new Map(Object.entries(context));
        return this.asyncLocalStorage.run(store, () => callback(...args));
    }

    /**
     * Runs an observable in the execution context.
     * This is useful for websocket connections where you need to maintain the execution context across the observable's lifecycle.
     *
     * @param context The execution context to run the observable in
     * @param observable The observable to run in the execution context
     * @returns A new observable that runs in the execution context
     */
    runObservable<T>(context: PlainExecutionContextStore, observable: Observable<T>): Observable<T> {
        return new Observable<T>((subscriber) => {
            void this.run(context, async () => {
                return new Promise<void>((resolve) => {
                    observable.subscribe({
                        next: (value) => subscriber.next(value),
                        error: (error) => {
                            subscriber.error(error);
                            resolve();
                        },
                        complete: () => {
                            subscriber.complete();
                            resolve();
                        }
                    });
                });
            });
        });
    }

    serialize(warningOnNoActiveContext = true): PlainExecutionContextStore {
        return Object.fromEntries(this.getStore(warningOnNoActiveContext).entries());
    }
}
