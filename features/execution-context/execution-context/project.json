{"name": "@cms/execution-context", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "features/execution-context/execution-context/src", "projectType": "library", "tags": ["scope:backend", "type:library"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/features/execution-context/execution-context", "tsConfig": "features/execution-context/execution-context/tsconfig.lib.json", "packageJson": "features/execution-context/execution-context/package.json", "main": "features/execution-context/execution-context/src/index.ts", "assets": ["features/execution-context/execution-context/*.md"]}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "features/execution-context/execution-context/jest.config.ts"}}}}