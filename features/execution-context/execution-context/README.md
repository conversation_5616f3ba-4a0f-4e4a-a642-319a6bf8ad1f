# Execution Context

This library provides functionality to manage the execution context of applications and requests. It allows developers to set and get the
current execution context, which can be used to store information that needs to be accessed by different parts of the application at
different stages of the execution.

## Problem Statement

Once we’re at a point where we can enable distributed messaging from an infrastructure perspective, the execution context is lost whenever a
message goes through the message broker or a job processor.

## Solution

Introduce a generalized execution context module on the server apps that allows seamless and transparent execution context information
forwarding through the message broker.

## Acceptance Criteria

- As a developer, the whole execution context (userId, requestId, med username, etc.) is seamlessly propagated to every subsequent
  distributed handler.
- As a developer, I am not required to pass any additional information explicitly to any distributed call.
- As a developer, I can attach any additional context information to the current execution context.
- As a developer, I can see log entries with all necessary context attached to it.
- As a platform maintainer, I can see log entries with all necessary context attached to it.

## Usage

### Setting Up the Execution Context Module

To set up the execution context module, import it into your application's root module:

```typescript
import { Module } from '@nestjs/common';
import { ExecutionContextModule } from '@cms/execution-context';

@Module({
    imports: [ExecutionContextModule.forRoot()]
})
export class AppModule {}
```

### Using the Execution Context in Services

You can inject the `ExecutionContext` service into your services and use it to set and get context values:

```typescript
import { Injectable } from '@nestjs/common';
import { ExecutionContext } from '@cms/execution-context';

@Injectable()
export class SomeService {
    constructor(private readonly executionContext: ExecutionContext) {}

    someMethod() {
        this.executionContext.set('key', 'value');
        const value = this.executionContext.get('key');
        console.log(value); // Output: 'value'
    }
}
```

### Propagating Execution Context Through Message Broker

The execution context is automatically propagated through the message broker. You do not need to pass any additional information explicitly.

### Propagating the execution context through job workers

The execution context is automatically propagated through job workers. You do not need to pass any additional information explicitly.

### Running Observables in Execution Context

You can run observables in the execution context using the `runObservable` method. This is particularly useful for websocket connections
where you need to maintain the execution context across the observable's lifecycle:

```typescript
import { Injectable } from '@nestjs/common';
import { ExecutionContext } from '@cms/execution-context';
import { Observable } from 'rxjs';

@Injectable()
export class WebsocketService {
    constructor(private readonly executionContext: ExecutionContext) {}

    processMessage(message: any, requestId: string): Observable<any> {
        // Create your observable
        const messageObservable = this.createMessageObservable(message);

        // Create the execution context
        const context = { requestId, messageType: message.type };

        // Run the observable in the execution context
        return this.executionContext.runObservable(context, messageObservable);
    }

    private createMessageObservable(message: any): Observable<any> {
        // Your implementation here
    }
}
```

## Integrations

- The module applies an [app middleware](./src/lib/execution-context.middleware.ts) that runs all requests in a separate execution context
  and provides request logging capabilities with request context propagation.
- [WebSockets: y-adapter](../../../backend/common/src/lib/y-protocol/y-adapter.ts) is integrated with the execution context module to
  propagate the execution context through WebSocket connections. In order to assure propagation, please apply the
  [Ws Execution context interceptor](./src/lib/ws-execution-context.interceptor.ts) interceptor to the consuming WS gateways.
    - We have tried multiple implementation approaches for web socket requests and have encountered multiple issues with the context being
      lost. Starting a context in the adapters bindMessageHandler method and additionally forwarding it in the interceptor is the most
      stable solution we have found so far.
- [Message Broker](../../message-broker/src/lib/message.broker.ts)
- [Job Processor](../../job-queue/src/lib/queue.service.ts)

## Payload types

We have integrated the execution context payload to common objects. The following objects have been integrated with the execution context:

- [BaseEvent](../../../shared/common/src/lib/base.event.ts)
- [BaseQuery](../../../shared/common/src/lib/base.query.ts)
- [BaseCommand](../../../shared/common/src/lib/base.command.ts)
- [CommandResult](../../../shared/common/src/lib/command.result.ts)
- [QueryResult](../../../shared/common/src/lib/query.result.ts)
- [Job](../../../shared/common/src/lib/job.model.ts)

### Note on optional injections

The execution context integration are designed to be optional, which makes testing easier.
