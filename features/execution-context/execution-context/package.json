{"name": "@cms/execution-context", "version": "0.0.1", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"@nestjs/testing": "^10.3.3", "reflect-metadata": "^0.2.1", "supertest": "^6.3.4"}, "peerDependencies": {"@nestjs/common": "^10.3.3", "@tronius/shared-common": "file:../../../shared/common", "express": "^4.18.0", "rxjs": "^7.8.1"}}