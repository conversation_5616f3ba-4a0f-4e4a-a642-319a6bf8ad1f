{"name": "execution-context-e2e", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "features/execution-context/e2e/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/features/execution-context/e2e", "main": "features/execution-context/e2e/src/index.ts", "tsConfig": "features/execution-context/e2e/tsconfig.lib.json", "assets": ["features/execution-context/e2e/*.md"]}}, "lint": {"executor": "@nx/eslint:lint"}, "e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "features/execution-context/e2e/jest.config.ts"}}}}