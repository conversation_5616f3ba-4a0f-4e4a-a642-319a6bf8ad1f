import { BullQueueProv<PERSON><PERSON>dapter, BullQueuesMap, BullmqQueueModule, bullQueuesMapInjectionToken } from '@cms/bullmq-queue';
import { ExecutionContext, ExecutionContextModule } from '@cms/execution-context';
import { InjectQueueService, JobQueueModule, ProcessJob, QueueService, Worker } from '@cms/job-queue';
import { deserialize } from '@cms/serialization';
import { Injectable, Module, ModuleMetadata } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { Job, UUIDFactory, waitFor } from '@tronius/shared-common';
import { NestShutdownModule } from 'nest-shutdown-module';

class TestJob extends Job {
    static create(): TestJob {
        return deserialize(TestJob, {});
    }
}

@Injectable()
@ProcessJob(TestJob)
class TestJobWorker implements Worker<TestJob> {
    process(): Promise<void> | void {
        //
    }
}

@Injectable()
class TestQueueService {
    constructor(@InjectQueueService(TestJob) private readonly queueService: QueueService<TestJob>) {}

    async addJob(job: TestJob): Promise<void> {
        await this.queueService.addJob(job);
    }
}

@Module({
    providers: [TestQueueService],
    imports: [JobQueueModule.forJob(TestJob)]
})
class TestJobQueueModule {}

@Module({
    imports: [JobQueueModule.forJob(TestJob)],
    providers: [TestJobWorker]
})
class JobWorkerModule {}

describe('job-query-integration', () => {
    let firstModule: TestingModule;
    let secondModule: TestingModule;

    beforeAll(async () => {
        const queuePrefix = UUIDFactory.create();

        const commonImports: ModuleMetadata['imports'] = [
            ConfigModule.forRoot({ isGlobal: true }),
            ExecutionContextModule.forRoot(),
            NestShutdownModule,
            JobQueueModule.forRootAsync({
                // We're using two separate modules, so the queue destruction has to be done manually
                imports: [BullmqQueueModule.forE2E(queuePrefix, false)],
                inject: [BullQueueProviderAdapter],
                useFactory: (bullQueueProvider: BullQueueProviderAdapter) => bullQueueProvider
            })
        ];

        firstModule = await Test.createTestingModule({
            imports: [...commonImports, TestJobQueueModule]
        }).compile();

        await firstModule.init();

        secondModule = await Test.createTestingModule({
            imports: [...commonImports, JobWorkerModule]
        }).compile();

        await secondModule.init();
    });

    it('should pass the execution context to the job worker', async () => {
        const initialContext = {
            executionId: '1',
            username: 'firstUser'
        };

        const firstModulesExecutionContext = firstModule.get(ExecutionContext);
        const secondModulesExecutionContext = secondModule.get(ExecutionContext);
        const secondModulesExecutionContextInvocationSpy = jest.spyOn(secondModulesExecutionContext, 'run');

        await firstModulesExecutionContext.run(initialContext, async () => {
            const testJob = TestJob.create();

            const queueService = firstModule.get(TestQueueService);
            await queueService.addJob(testJob);

            await waitFor(3_000);

            const expectedInvocationContext = {
                ...initialContext,
                job: TestJob.name,
                jobId: testJob.jobId
            };

            const [[jobWorkerInvocationContextPayload]] = secondModulesExecutionContextInvocationSpy.mock.calls;
            expect(jobWorkerInvocationContextPayload).toMatchObject(expectedInvocationContext);
        });
    });

    afterAll(async () => {
        const bullQueuesMap: BullQueuesMap = firstModule.get(bullQueuesMapInjectionToken);
        const queues = Array.from(bullQueuesMap.values());

        const obliteratePromises = queues.map(async (queue) => queue.obliterate({ force: true }));

        await Promise.all(obliteratePromises);

        await Promise.all([firstModule.close(), secondModule.close()]);
    });
});
