import { ExecutionContext, ExecutionContextModule } from '@cms/execution-context';
import { EventH<PERSON><PERSON>, MessageBroker, MessageBrokerModule, OnEvent } from '@cms/message-broker';
import { deserialize } from '@cms/serialization';
import { Injectable, Module, ModuleMetadata } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { BaseEvent, UUIDFactory, waitFor } from '@tronius/shared-common';
import { NestShutdownModule } from 'nest-shutdown-module';
import { RabbitMqConfig, RabbitMqMessageBrokerModule } from 'rabbit-mq-message-broker';

class TestEvent extends BaseEvent {
    static create(): TestEvent {
        return deserialize(TestEvent, {});
    }
}

@Injectable()
@OnEvent(TestEvent)
class TestEventHandler implements EventHandler<TestEvent> {
    async handle(): Promise<void> {
        //
    }

    onError(_error: unknown, _eventPayload: unknown): void {
        //
    }
}

@Module({
    providers: [TestEventHandler]
})
class EventHandlingModule {}

describe('Message-broker-event-integration', () => {
    let firstModule: TestingModule;
    let secondModule: TestingModule;

    beforeAll(async () => {
        const brokerPrefix = UUIDFactory.create();

        const commonImports: ModuleMetadata['imports'] = [
            ConfigModule.forRoot({ isGlobal: true }),
            ExecutionContextModule.forRoot(),
            NestShutdownModule,
            MessageBrokerModule.forRootAsync({
                imports: [
                    RabbitMqMessageBrokerModule.forRootAsync({
                        imports: [],
                        inject: [ConfigService],
                        useFactory: (configService: ConfigService): RabbitMqConfig => {
                            return RabbitMqConfig.create({
                                /*  eslint-disable @typescript-eslint/no-non-null-assertion */
                                hostname: configService.get<string>('RABBIT_MQ_HOSTNAME')!,
                                port: configService.get<number>('RABBIT_MQ_PORT')!,
                                username: configService.get<string>('RABBIT_MQ_USERNAME')!,
                                password: configService.get<string>('RABBIT_MQ_PASSWORD')!,
                                // Bigger timeout for debugging
                                rpcTimeoutInMs: 60_000_000,
                                ssl: configService.get<boolean>('RABBIT_MQ_SSL')!,
                                autoDeleteOnConnectionClose: true,
                                prefix: brokerPrefix
                                /*  eslint-enable @typescript-eslint/no-non-null-assertion */
                            });
                        }
                    })
                ]
            })
        ];

        firstModule = await Test.createTestingModule({
            imports: [
                MessageBrokerModule.forRootAsync({
                    imports: [...commonImports]
                })
            ]
        }).compile();

        await firstModule.init();

        secondModule = await Test.createTestingModule({
            imports: [...commonImports, EventHandlingModule]
        }).compile();

        await secondModule.init();
    });

    it('should pass the execution context to the event handler', async () => {
        const firstModulesMessageBroker = firstModule.get(MessageBroker);

        const initialContext = {
            executionId: '1',
            username: 'firstUser'
        };

        const firstModulesExecutionContext = firstModule.get(ExecutionContext);
        const secondModulesExecutionContext = secondModule.get(ExecutionContext);
        const secondModulesExecutionContextInvocationSpy = jest.spyOn(secondModulesExecutionContext, 'run');

        await firstModulesExecutionContext.run(initialContext, async () => {
            const testEvent = TestEvent.create();

            await firstModulesMessageBroker.publish(TestEvent, testEvent);

            await waitFor(3_000);

            const expectedInvocationContext = {
                ...initialContext,
                event: TestEvent.name,
                eventId: testEvent.eventId,
                eventHandlerName: TestEventHandler.name
            };

            const [[eventHandlerInvocationContextPayload]] = secondModulesExecutionContextInvocationSpy.mock.calls;
            expect(eventHandlerInvocationContextPayload).toMatchObject(expectedInvocationContext);

            const executionContextAfterEvent = firstModulesExecutionContext.serialize();
            const expectedContextAfterCommandResult = initialContext;
            expect(executionContextAfterEvent).toMatchObject(expectedContextAfterCommandResult);
        });
    });

    afterAll(async () => {
        await Promise.all([firstModule.close(), secondModule.close()]);
    });
});
