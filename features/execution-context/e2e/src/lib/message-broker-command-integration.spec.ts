import { ExecutionContext, ExecutionContextModule } from '@cms/execution-context';
import { Command<PERSON><PERSON><PERSON>, MessageBroker, MessageBrokerModule, OnCommand } from '@cms/message-broker';
import { deserialize } from '@cms/serialization';
import { Injectable, Module, ModuleMetadata } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { BaseCommand, CommandResult, UUIDFactory } from '@tronius/shared-common';
import { NestShutdownModule } from 'nest-shutdown-module';
import { RabbitMqConfig, RabbitMqMessageBrokerModule } from 'rabbit-mq-message-broker';

class TestCommand extends BaseCommand {
    static create(): TestCommand {
        return deserialize(TestCommand, {});
    }
}

const commandExecutionContextAddition = 'commandExecutionContextAddition';

@Injectable()
@OnCommand(TestCommand)
class TestCommandHandler implements CommandHandler<TestCommand> {
    constructor(private readonly executionContext: ExecutionContext) {}

    handle({ correlationId }: TestCommand): CommandResult {
        this.executionContext.set(commandExecutionContextAddition, commandExecutionContextAddition);

        return CommandResult.create({ correlationId });
    }
}

@Module({
    providers: [TestCommandHandler]
})
class CommandHandlingModule {}

describe('Message-broker-command-integration', () => {
    let firstModule: TestingModule;
    let secondModule: TestingModule;

    beforeAll(async () => {
        const brokerPrefix = UUIDFactory.create();

        const commonImports: ModuleMetadata['imports'] = [
            ConfigModule.forRoot({ isGlobal: true }),
            ExecutionContextModule.forRoot(),
            NestShutdownModule,
            MessageBrokerModule.forRootAsync({
                imports: [
                    RabbitMqMessageBrokerModule.forRootAsync({
                        imports: [],
                        inject: [ConfigService],
                        useFactory: (configService: ConfigService): RabbitMqConfig => {
                            return RabbitMqConfig.create({
                                /*  eslint-disable @typescript-eslint/no-non-null-assertion */
                                hostname: configService.get<string>('RABBIT_MQ_HOSTNAME')!,
                                port: configService.get<number>('RABBIT_MQ_PORT')!,
                                username: configService.get<string>('RABBIT_MQ_USERNAME')!,
                                password: configService.get<string>('RABBIT_MQ_PASSWORD')!,
                                // Bigger timeout for debugging
                                rpcTimeoutInMs: 60_000_000,
                                ssl: configService.get<boolean>('RABBIT_MQ_SSL')!,
                                autoDeleteOnConnectionClose: true,
                                prefix: brokerPrefix
                                /*  eslint-enable @typescript-eslint/no-non-null-assertion */
                            });
                        }
                    })
                ]
            })
        ];

        firstModule = await Test.createTestingModule({
            imports: [...commonImports]
        }).compile();

        await firstModule.init();

        secondModule = await Test.createTestingModule({
            imports: [...commonImports, CommandHandlingModule]
        }).compile();

        await secondModule.init();
    });

    it('should pass the execution context to the command handler', async () => {
        const firstModulesMessageBroker = firstModule.get(MessageBroker);

        const initialContext = {
            executionId: '1',
            username: 'firstUser'
        };

        const firstModulesExecutionContext = firstModule.get(ExecutionContext);
        const secondModulesExecutionContext = secondModule.get(ExecutionContext);
        const secondModulesExecutionContextInvocationSpy = jest.spyOn(secondModulesExecutionContext, 'run');

        await firstModulesExecutionContext.run(initialContext, async () => {
            const testCommand = TestCommand.create();

            await firstModulesMessageBroker.command(testCommand);

            const expectedInvocationContext = {
                ...initialContext,
                command: TestCommand.name,
                correlationId: testCommand.correlationId
            };

            const [[commandHandlerInvocationContextPayload]] = secondModulesExecutionContextInvocationSpy.mock.calls;
            expect(commandHandlerInvocationContextPayload).toMatchObject(expectedInvocationContext);

            const executionContextAfterCommandResult = firstModulesExecutionContext.serialize();
            const expectedContextAfterCommandResult = {
                ...initialContext,
                [commandExecutionContextAddition]: commandExecutionContextAddition
            };
            expect(executionContextAfterCommandResult).toMatchObject(expectedContextAfterCommandResult);
        });
    });

    afterAll(async () => {
        await Promise.all([firstModule.close(), secondModule.close()]);
    });
});
