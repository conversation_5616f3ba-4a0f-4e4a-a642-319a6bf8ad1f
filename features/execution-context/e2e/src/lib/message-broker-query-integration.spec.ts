import { ExecutionContext, ExecutionContextModule } from '@cms/execution-context';
import { MessageBroker, MessageBrokerModule, OnQuery, QueryHandler } from '@cms/message-broker';
import { deserialize } from '@cms/serialization';
import { Injectable, Module, ModuleMetadata } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { BaseQuery, QueryResult, UUIDFactory } from '@tronius/shared-common';
import { NestShutdownModule } from 'nest-shutdown-module';
import { RabbitMqConfig, RabbitMqMessageBrokerModule } from 'rabbit-mq-message-broker';

class TestQuery extends BaseQuery {
    static create(): TestQuery {
        return deserialize(TestQuery, {});
    }
}

class TestQueryResult extends QueryResult {
    static create(correlationId: string): TestQueryResult {
        return deserialize(TestQueryResult, { correlationId });
    }
}

const queryExecutionContextAddition = 'queryExecutionContextAddition';

@Injectable()
@OnQuery(TestQuery, TestQueryResult)
class TestQueryHandler implements QueryHandler<TestQuery, TestQueryResult> {
    constructor(private readonly executionContext: ExecutionContext) {}

    handle(query: TestQuery): TestQueryResult {
        this.executionContext.set(queryExecutionContextAddition, queryExecutionContextAddition);

        return TestQueryResult.create(query.correlationId);
    }
}

@Module({
    providers: [TestQueryHandler]
})
class QueryHandlingModule {}

describe('Message-broker-query-integration', () => {
    let firstModule: TestingModule;
    let secondModule: TestingModule;

    beforeAll(async () => {
        const brokerPrefix = UUIDFactory.create();

        const commonImports: ModuleMetadata['imports'] = [
            ConfigModule.forRoot({ isGlobal: true }),
            ExecutionContextModule.forRoot(),
            NestShutdownModule,
            MessageBrokerModule.forRootAsync({
                imports: [
                    RabbitMqMessageBrokerModule.forRootAsync({
                        imports: [],
                        inject: [ConfigService],
                        useFactory: (configService: ConfigService): RabbitMqConfig => {
                            return RabbitMqConfig.create({
                                /*  eslint-disable @typescript-eslint/no-non-null-assertion */
                                hostname: configService.get<string>('RABBIT_MQ_HOSTNAME')!,
                                port: configService.get<number>('RABBIT_MQ_PORT')!,
                                username: configService.get<string>('RABBIT_MQ_USERNAME')!,
                                password: configService.get<string>('RABBIT_MQ_PASSWORD')!,
                                // Bigger timeout for debugging
                                rpcTimeoutInMs: 60_000_000,
                                ssl: configService.get<boolean>('RABBIT_MQ_SSL')!,
                                autoDeleteOnConnectionClose: true,
                                prefix: brokerPrefix
                                /*  eslint-enable @typescript-eslint/no-non-null-assertion */
                            });
                        }
                    })
                ]
            })
        ];

        firstModule = await Test.createTestingModule({
            imports: [
                MessageBrokerModule.forRootAsync({
                    imports: [...commonImports]
                })
            ]
        }).compile();

        await firstModule.init();

        secondModule = await Test.createTestingModule({
            imports: [...commonImports, QueryHandlingModule]
        }).compile();

        await secondModule.init();
    });

    it('should pass the execution context to the query handler', async () => {
        const firstModulesMessageBroker = firstModule.get(MessageBroker);

        const initialContext = {
            executionId: '1',
            username: 'firstUser'
        };

        const firstModulesExecutionContext = firstModule.get(ExecutionContext);
        const secondModulesExecutionContext = secondModule.get(ExecutionContext);
        const secondModulesExecutionContextInvocationSpy = jest.spyOn(secondModulesExecutionContext, 'run');

        await firstModulesExecutionContext.run(initialContext, async () => {
            const testQuery = TestQuery.create();

            await firstModulesMessageBroker.query(testQuery, TestQueryResult);

            const expectedInvocationContext = {
                ...initialContext,
                query: TestQuery.name,
                correlationId: testQuery.correlationId
            };

            const [[queryHandlerInvocationContextPayload]] = secondModulesExecutionContextInvocationSpy.mock.calls;
            expect(queryHandlerInvocationContextPayload).toMatchObject(expectedInvocationContext);

            const executionContextAfterQueryResult = firstModulesExecutionContext.serialize();
            const expectedContextAfterQueryResult = {
                ...initialContext,
                [queryExecutionContextAddition]: queryExecutionContextAddition
            };
            expect(executionContextAfterQueryResult).toMatchObject(expectedContextAfterQueryResult);
        });
    });

    afterAll(async () => {
        await Promise.all([firstModule.close(), secondModule.close()]);
    });
});
