import { GetWinReconciliationDailyReportQuery, GetWinReconciliationDailyReportQueryResult } from '@cms/machine-metrics';
import { OnQuery, QueryHandler } from '@cms/message-broker';
import { Injectable } from '@nestjs/common';

import { DailyWinReconciliationReportService } from '../services';

@OnQuery(GetWinReconciliationDailyReportQuery, GetWinReconciliationDailyReportQueryResult)
@Injectable()
export class GetWinReconciliationDailyReportQueryHandler
    implements QueryHandler<GetWinReconciliationDailyReportQuery, GetWinReconciliationDailyReportQueryResult>
{
    constructor(private readonly dailyWinReconciliationReportService: DailyWinReconciliationReportService) {}

    async handle({
        correlationId,
        fromGamingDay,
        toGamingDay,
        type
    }: GetWinReconciliationDailyReportQuery): Promise<GetWinReconciliationDailyReportQueryResult> {
        const reportItems = await this.dailyWinReconciliationReportService.list({
            fromGamingDay,
            toGamingDay,
            type
        });

        return GetWinReconciliationDailyReportQueryResult.create({ correlationId, reportItems });
    }
}
