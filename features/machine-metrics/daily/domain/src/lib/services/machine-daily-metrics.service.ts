import { ListDailyMetricsProps, MachineDailyMetrics, MachineDailyMetricsUpsertedEvent } from '@cms/machine-metrics';
import { MessageBroker } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import { OperationalStatus } from '@tronius/shared-domain';

import { MachineDailyMetricsRepositoryPort } from '../ports';

/**
 * Service for managing machine daily metrics
 */
@Injectable()
export class MachineDailyMetricsService {
    private readonly logger = new Logger(MachineDailyMetricsService.name);

    constructor(
        private readonly repository: MachineDailyMetricsRepositoryPort,
        private readonly messageBroker: MessageBroker
    ) {}

    async list(props: ListDailyMetricsProps): Promise<MachineDailyMetrics[]> {
        return this.repository.list(props);
    }

    /**
     * Upsert machine daily metrics
     * This method will create a new entry if it doesn't exist, or update an existing one
     * @param metrics The machine daily metrics to upsert
     */
    async upsert(metrics: MachineDailyMetrics): Promise<void> {
        const { machineId, gamingDay, machineOperationalStatus } = metrics;

        // Get the existing metrics before upserting
        const existingMetrics = await this.getByMachineIdGamingDayAndOperationalStatus(machineId, gamingDay, machineOperationalStatus);

        // Upsert the machine daily metrics
        await this.repository.upsert(metrics);

        this.logger.log(`Upserted machine daily metrics for machine ${machineId} for gaming day ${gamingDay}`);

        // Publish the event with old and new data
        await this.messageBroker.publish(
            MachineDailyMetricsUpsertedEvent,
            MachineDailyMetricsUpsertedEvent.create({
                machineId,
                gamingDay,
                previousDailyMetricsData: existingMetrics,
                dailyMetricsData: metrics
            })
        );
    }

    /**
     * Get machine daily metrics by machine ID and gaming day, grouped by operational status
     * @param machineId The machine ID
     * @param gamingDay The gaming day in YYYY-MM-DD format
     * @returns Object with keys as OperationalStatus and values as MachineDailyMetrics or null
     */
    async getByMachineIdAndGamingDay(machineId: string, gamingDay: string): Promise<Record<OperationalStatus, MachineDailyMetrics | null>> {
        return this.repository.getByMachineIdAndGamingDay(machineId, gamingDay);
    }

    /**
     * Get a specific machine daily metrics entry by machine ID, gaming day, and operational status
     * @param machineId The machine ID
     * @param gamingDay The gaming day in YYYY-MM-DD format
     * @param operationalStatus The operational status
     * @returns The machine daily metrics entry or null if not found
     */
    async getByMachineIdGamingDayAndOperationalStatus(
        machineId: string,
        gamingDay: string,
        operationalStatus: OperationalStatus
    ): Promise<MachineDailyMetrics | null> {
        return this.repository.getByMachineIdGamingDayAndOperationalStatus(machineId, gamingDay, operationalStatus);
    }

    /**
     * Get the latest machine daily metrics entry for a machine before a specific gaming day
     * This will return the entry with the greatest endMetersCollectedAt and startMetersCollectedAt
     * @param machineId The machine ID
     * @param beforeGamingDay The gaming day before which to find metrics
     * @returns The latest machine daily metrics entry or null if not found
     */
    async getLatestBeforeGamingDay(machineId: string, beforeGamingDay: string): Promise<MachineDailyMetrics | null> {
        return this.repository.getLatestBeforeGamingDay(machineId, beforeGamingDay);
    }
}
