import {
    MachineReconciliationReportUtil,
    MachineWinReconciliationReportItem,
    MachineWinReconciliationReportMetrics,
    WinReconciliationDailyRequest
} from '@cms/machine-metrics';
import { MessageBroker } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import { NotFoundError } from '@tronius/shared-common';
import { GetAllMachineProfilesQuery, GetAllMachineProfilesQueryResult, MachineProfileListItem } from '@tronius/shared-domain';
import groupBy from 'lodash/groupBy';

import { MachineDailyMetricsService } from './machine-daily-metrics.service';

/**
 * Service for managing machine daily metrics
 */
@Injectable()
export class DailyWinReconciliationReportService {
    private readonly logger = new Logger(DailyWinReconciliationReportService.name);

    constructor(
        private readonly machineDailyMetricsService: MachineDailyMetricsService,
        private readonly messageBroker: MessageBroker
    ) {}

    async list(request: WinReconciliationDailyRequest): Promise<MachineWinReconciliationReportItem[]> {
        const { type, ...listProps } = request;

        const [machinesDailyMetrics, { machineProfiles }] = await Promise.all([
            this.machineDailyMetricsService.list(listProps),
            this.messageBroker.query(GetAllMachineProfilesQuery.create(), GetAllMachineProfilesQueryResult)
        ]);
        const machineById = new Map<string, MachineProfileListItem>(machineProfiles.map((machine) => [machine.id, machine]));

        if (machinesDailyMetrics.length === 0) {
            return [];
        }

        const reports: MachineWinReconciliationReportItem[] = [];
        const machinesDailyMetricsGroupedByMachineId = groupBy(machinesDailyMetrics, (machineDailyMetric) => machineDailyMetric.machineId);

        Object.entries(machinesDailyMetricsGroupedByMachineId).forEach(([machineId, dailyMetrics]) => {
            const machine = machineById.get(machineId);
            if (!machine) {
                throw new NotFoundError({
                    message: `Machine with id ${machineId} has metrics but no machine profile`,
                    params: { machineId }
                });
            }

            const summedDailyMetrics = dailyMetrics.reduce<MachineWinReconciliationReportMetrics>((acc, currentDailyMetrics) => {
                const { metrics } = currentDailyMetrics;
                const currentMetrics = MachineReconciliationReportUtil.extractReportMetricsFromAllMetricsByType(type, metrics);
                if (Object.keys(acc).length === 0) {
                    return currentMetrics;
                }
                // Merge metrics by adding values for the same keys
                Object.entries(currentMetrics).forEach(([key, value]) => {
                    if (value === null) {
                        return;
                    }
                    const metricKey = key as keyof MachineWinReconciliationReportMetrics;
                    const existingValue = acc[metricKey];
                    acc[metricKey] = existingValue ? existingValue + value : value;
                });
                return acc;
            }, {});
            reports.push(MachineWinReconciliationReportItem.create({ machine, metrics: summedDailyMetrics }));
        });

        return reports;
    }
}
