/* eslint-disable @typescript-eslint/naming-convention */
import {
    MachineDailyMetrics,
    MachineMetricCode,
    MachineReconciliationReportUtil,
    ReconciliationReportMetrics,
    WinReconciliationReportType
} from '@cms/machine-metrics';
import { MessageBroker } from '@cms/message-broker';
import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundError, UUIDFactory } from '@tronius/shared-common';

import { DailyWinReconciliationReportService } from './daily-win-reconciliation-report.service';
import { MachineDailyMetricsService } from './machine-daily-metrics.service';

describe('DailyWinReconciliationReportService', () => {
    let service: DailyWinReconciliationReportService;
    let messageBroker: MessageBroker;
    let dailyService: MachineDailyMetricsService;

    const generateRandomMetrics = (): Partial<Record<MachineMetricCode, number>> => {
        const metrics: Partial<Record<MachineMetricCode, number>> = {};
        for (const code in MachineMetricCode) {
            if (Object.prototype.hasOwnProperty.call(MachineMetricCode, code)) {
                metrics[MachineMetricCode[code as keyof typeof MachineMetricCode]] = Math.floor(Math.random() * 10) + 1;
            }
        }
        return metrics;
    };

    const mockMachineProfile = {
        id: UUIDFactory.create(),
        location: 'mock-location',
        assetNumber: 1,
        denomination: 0.01,
        manufacturerId: UUIDFactory.create(),
        manufacturerName: 'mock-manufacturer',
        machineGameTypeId: UUIDFactory.create(),
        machineGameTypeName: 'mock-machine-game-type'
    };

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                DailyWinReconciliationReportService,
                {
                    provide: MachineDailyMetricsService,
                    useValue: {
                        list: jest.fn()
                    }
                },
                {
                    provide: MessageBroker,
                    useValue: {
                        query: jest.fn()
                    }
                }
            ]
        }).compile();

        service = module.get<DailyWinReconciliationReportService>(DailyWinReconciliationReportService);
        dailyService = module.get<MachineDailyMetricsService>(MachineDailyMetricsService);
        messageBroker = module.get<MessageBroker>(MessageBroker);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('should create reconciliation report', () => {
        it('should add up machine metrics to create a report excluding and including promo', () => {
            // Arrange
            const machine = mockMachineProfile;

            const randomMetrics1 = generateRandomMetrics();
            const randomMetrics2 = generateRandomMetrics();
            const expectedMetrics = [
                {
                    machineId: machine.id,
                    metrics: randomMetrics1
                } as MachineDailyMetrics,
                {
                    machineId: machine.id,
                    metrics: randomMetrics2
                } as MachineDailyMetrics
            ];

            (dailyService.list as jest.Mock).mockResolvedValue(expectedMetrics);
            (messageBroker.query as jest.Mock).mockResolvedValue({ machineProfiles: [machine] });

            // Act
            [WinReconciliationReportType.ExcludePromo, WinReconciliationReportType.IncludePromo].forEach((type) => {
                const fieldsToMetricCodeMap = MachineReconciliationReportUtil.reportMetricsToMetricCodeMapper(type);
                service
                    .list({
                        fromGamingDay: '2023-05-15',
                        toGamingDay: '2023-05-16',
                        type
                    })
                    .then(([report]) => {
                        // Assert
                        expect(dailyService.list).toHaveBeenCalledWith({ fromGamingDay: '2023-05-15', toGamingDay: '2023-05-16' });
                        expect(report.metrics).toBeDefined();
                        expect(Object.keys(report.metrics).length).toBe(Object.keys(fieldsToMetricCodeMap).length);

                        Object.entries(report.metrics).forEach(([reportMetric, calculatedSum]) => {
                            const metricCode = fieldsToMetricCodeMap[reportMetric as ReconciliationReportMetrics];
                            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                            const actualSum = randomMetrics1[metricCode!]! + randomMetrics2[metricCode!]!;
                            expect(calculatedSum).toBe(actualSum);
                        });
                    });
            });
        });

        it('should add up machine metrics to create a report including promo', async () => {
            // Arrange
            const machine = mockMachineProfile;

            const randomMetrics1 = generateRandomMetrics();
            const randomMetrics2 = generateRandomMetrics();
            const expectedMetrics = [
                {
                    machineId: machine.id,
                    metrics: randomMetrics1
                } as MachineDailyMetrics,
                {
                    machineId: machine.id,
                    metrics: randomMetrics2
                } as MachineDailyMetrics
            ];

            const fieldsToMetricCodeMap = MachineReconciliationReportUtil.reportMetricsToMetricCodeMapper(
                WinReconciliationReportType.IncludePromo
            );

            const nullReportFieldsSet = Object.entries(fieldsToMetricCodeMap).reduce<Set<ReconciliationReportMetrics>>(
                (acc, [key, value]) => {
                    if (value === null) {
                        acc.add(key as ReconciliationReportMetrics);
                    }
                    return acc;
                },
                new Set()
            );

            (dailyService.list as jest.Mock).mockResolvedValue(expectedMetrics);
            (messageBroker.query as jest.Mock).mockResolvedValue({ machineProfiles: [machine] });

            // Act
            const [report] = await service.list({
                fromGamingDay: '2023-05-15',
                toGamingDay: '2023-05-16',
                type: WinReconciliationReportType.IncludePromo
            });

            // Assert
            expect(dailyService.list).toHaveBeenCalledWith({ fromGamingDay: '2023-05-15', toGamingDay: '2023-05-16' });
            expect(report.metrics).toBeDefined();
            expect(Object.keys(report.metrics).length).toBe(Object.keys(fieldsToMetricCodeMap).length);

            Object.entries(report.metrics).forEach(([reportMetric, calculatedSum]) => {
                if (nullReportFieldsSet.has(reportMetric as ReconciliationReportMetrics)) {
                    expect(calculatedSum).toBeNull();
                    return;
                }
                const metricCode = fieldsToMetricCodeMap[reportMetric as ReconciliationReportMetrics];
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                const actualSum = randomMetrics1[metricCode!]! + randomMetrics2[metricCode!]!;
                expect(calculatedSum).toBe(actualSum);
            });
        });
    });

    describe('should not create report', () => {
        it('should not create report if no machine was found', async () => {
            // Arrange
            (dailyService.list as jest.Mock).mockResolvedValue([
                {
                    machineId: mockMachineProfile.id,
                    metrics: generateRandomMetrics()
                } as MachineDailyMetrics
            ]);
            (messageBroker.query as jest.Mock).mockResolvedValue({ machineProfiles: [] });

            // Act & Assert
            await expect(
                service.list({
                    fromGamingDay: '2023-05-15',
                    toGamingDay: '2023-05-16',
                    type: WinReconciliationReportType.IncludePromo
                })
            ).rejects.toThrow(NotFoundError);
        });
    });
});
