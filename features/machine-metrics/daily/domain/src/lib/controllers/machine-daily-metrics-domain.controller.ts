import { MachineWinReconciliationReportItem } from '@cms/machine-metrics';
import { CmsPermissions } from '@cms/server-permissions';
import { Controller, Get, Query } from '@nestjs/common';
import { ApiOAuth2, ApiTags } from '@nestjs/swagger';
import { CmsAction } from '@tronius/shared-domain';

import { WinReconciliationDailyRequestDto } from '../dto';
import { DailyWinReconciliationReportService } from '../services';

@ApiTags('Machine Daily Metrics')
@ApiOAuth2([])
@Controller('machine-daily-metrics')
export class MachineDailyMetricsController {
    constructor(private readonly service: DailyWinReconciliationReportService) {}

    @Get('/reconciliation-report')
    @CmsPermissions(CmsAction.MachineMetricsList)
    async getReconciliationReport(@Query() query: WinReconciliationDailyRequestDto): Promise<MachineWinReconciliationReportItem[]> {
        return this.service.list(query);
    }
}
