import { WinReconciliationDailyRequest, WinReconciliationReportType } from '@cms/machine-metrics';
import { ApiProperty } from '@nestjs/swagger';

export class WinReconciliationDailyRequestDto extends WinReconciliationDailyRequest {
    @ApiProperty({
        description: 'Initial gaming day of the report data',
        type: 'string',
        required: true,
        example: '2025-01-30'
    })
    override fromGamingDay!: string;

    @ApiProperty({
        description: 'Final gaming day of the report data',
        type: 'string',
        required: true,
        example: '2025-01-30'
    })
    override toGamingDay!: string;

    @ApiProperty({
        description: 'Type of the metrics for the report',
        type: 'string',
        required: true,
        example: WinReconciliationReportType.ExcludePromo
    })
    override type!: WinReconciliationReportType;
}
