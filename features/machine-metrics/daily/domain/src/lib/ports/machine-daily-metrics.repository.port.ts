import { ListDailyMetricsProps, MachineDailyMetrics } from '@cms/machine-metrics';
import { OperationalStatus } from '@tronius/shared-domain';

/**
 * Repository port for machine daily metrics operations
 * This interface defines the contract for interacting with machine daily metrics data
 */
export abstract class MachineDailyMetricsRepositoryPort {
    /**
     * List machine daily metrics entries based on the provided query
     * @param query The query parameters
     * @returns Array of MachineDailyMetrics entries
     */
    abstract list(query: ListDailyMetricsProps): Promise<MachineDailyMetrics[]>;

    /**
     * Get machine daily metrics entries by machine ID and gaming day, grouped by operational status
     * @param machineId The machine ID
     * @param gamingDay The gaming day in YYYY-MM-DD format
     * @returns Object with keys as OperationalStatus and values as MachineDailyMetrics
     */
    abstract getByMachineIdAndGamingDay(machineId: string, gamingDay: string): Promise<Record<OperationalStatus, MachineDailyMetrics>>;

    /**
     * Get a specific machine daily metrics entry by machine ID, gaming day, and operational status
     * @param machineId The machine ID
     * @param gamingDay The gaming day in YYYY-MM-DD format
     * @param operationalStatus The operational status
     * @returns The machine daily metrics entry or null if not found
     */
    abstract getByMachineIdGamingDayAndOperationalStatus(
        machineId: string,
        gamingDay: string,
        operationalStatus: OperationalStatus
    ): Promise<MachineDailyMetrics | null>;

    /**
     * Get the latest machine daily metrics entry for a machine before a specific gaming day
     * This will return the entry with the greatest endMetersCollectedAt and startMetersCollectedAt
     * @param machineId The machine ID
     * @param beforeGamingDay The gaming day before which to find metrics
     * @returns The latest machine daily metrics entry or null if not found
     */
    abstract getLatestBeforeGamingDay(machineId: string, beforeGamingDay: string): Promise<MachineDailyMetrics | null>;

    /**
     * Upsert a new machine daily metrics entry
     * @param metrics The machine daily metrics to create
     */
    abstract upsert(metrics: MachineDailyMetrics): Promise<void>;
}
