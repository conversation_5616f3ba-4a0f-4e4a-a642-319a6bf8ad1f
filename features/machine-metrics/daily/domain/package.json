{"name": "@cms/machine-daily-metrics-domain", "version": "0.0.1", "private": true, "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"@nestjs/testing": "^10.3.3", "reflect-metadata": "^0.2.1"}, "peerDependencies": {"@cms/cash-operations": "file:../../../../features/cash-operations/shared", "@cms/casino-operator": "file:../../../../features/casino-operator/shared", "@cms/job-queue": "file:../../../../libs/backend/job-queue", "@cms/machine-funds-transfers": "file:../../../../features/machine-funds-transfers/shared", "@cms/machine-handpays": "file:../../../../features/machine-handpays/shared", "@cms/machine-metrics": "file:../../../../features/machine-metrics/shared", "@cms/message-broker": "file:../../../../libs/backend/message-broker", "@cms/server-permissions": "file:../../../../libs/backend/server-permissions", "@cms/tickets": "file:../../../../features/tickets/shared", "@nestjs/common": "^10.3.3", "@nestjs/swagger": "^7.2.1", "@tronius/shared-common": "file:../../../../libs/shared/common", "@tronius/shared-domain": "file:../../../../libs/shared/domain", "date-fns": "^3.3.1", "lodash": "^4.17.21"}}