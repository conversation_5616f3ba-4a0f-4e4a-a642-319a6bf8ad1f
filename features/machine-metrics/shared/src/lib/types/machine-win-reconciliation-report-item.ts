import { deserialize } from '@cms/serialization';
import { MachineProfileListItem } from '@tronius/shared-domain';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsObject, ValidateNested } from 'class-validator';

import { ReconciliationReportMetrics } from './reconciliation-report-metrics.enum';

export type MachineWinReconciliationReportMetrics = Partial<Record<ReconciliationReportMetrics, number | null>>;

export interface MachineWinReconciliationReportItemProps {
    machine: MachineProfileListItem;
    metrics: MachineWinReconciliationReportMetrics;
}

export class MachineWinReconciliationReportItem implements MachineWinReconciliationReportItemProps {
    @Type(() => MachineProfileListItem)
    @ValidateNested()
    @IsNotEmpty()
    machine!: MachineProfileListItem;

    // TODO: validate report fields
    @IsObject()
    @IsNotEmpty()
    metrics!: MachineWinReconciliationReportMetrics;

    static create(props: MachineWinReconciliationReportItemProps): MachineWinReconciliationReportItem {
        return deserialize(MachineWinReconciliationReportItem, props);
    }
}
