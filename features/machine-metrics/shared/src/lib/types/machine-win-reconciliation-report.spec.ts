import { UUIDFactory } from '@tronius/shared-common';
import { MachineProfileListItem } from '@tronius/shared-domain';
import { validate } from 'class-validator';

import { MachineWinReconciliationReportItem, MachineWinReconciliationReportItemProps } from './machine-win-reconciliation-report-item';
import { ReconciliationReportMetrics } from './reconciliation-report-metrics.enum';

describe('MachineWinReconciliationReportItem', () => {
    // Common test data
    const machineId = UUIDFactory.create();
    const validMachine: MachineProfileListItem = {
        id: machineId,
        assetNumber: 12345,
        location: 'Test Location',
        denomination: 0.01,
        manufacturerId: UUIDFactory.create(),
        manufacturerName: 'Test Manufacturer',
        machineGameTypeId: UUIDFactory.create(),
        machineGameTypeName: 'Test Game Type',
        rtp: null
    };

    // Valid metrics with all required fields
    const validMetrics: Partial<Record<ReconciliationReportMetrics, number>> = {
        [ReconciliationReportMetrics.AccountingTotalWin]: 1000,
        [ReconciliationReportMetrics.CashRevenue]: 500,
        [ReconciliationReportMetrics.TicketRevenue]: 200,
        [ReconciliationReportMetrics.TransferRevenue]: 300,
        [ReconciliationReportMetrics.AccountingCreditsIn]: 1000,
        [ReconciliationReportMetrics.TotalRevenue]: 1000,
        [ReconciliationReportMetrics.TicketExpenses]: 100,
        [ReconciliationReportMetrics.TransferExpenses]: 150,
        [ReconciliationReportMetrics.AccountingHandpays]: 50,
        [ReconciliationReportMetrics.AccountingCreditsOut]: 300,
        [ReconciliationReportMetrics.AccountingTotalOut]: 300,
        [ReconciliationReportMetrics.PlayTotalWin]: 700,
        [ReconciliationReportMetrics.Turnover]: 1000,
        [ReconciliationReportMetrics.PaidToMachine]: 200,
        [ReconciliationReportMetrics.Jackpots]: 50,
        [ReconciliationReportMetrics.Progressives]: 50,
        [ReconciliationReportMetrics.FinanceTotalWin]: 700,
        [ReconciliationReportMetrics.FinanceCashIn]: 200,
        [ReconciliationReportMetrics.FinanceTicketsIn]: 200,
        [ReconciliationReportMetrics.FinanceTransfersIn]: 300,
        [ReconciliationReportMetrics.FinanceCreditsIn]: 300,
        [ReconciliationReportMetrics.FinanceTotalIn]: 1000,
        [ReconciliationReportMetrics.FinanceTicketsOut]: 100,
        [ReconciliationReportMetrics.FinanceTransfersOut]: 150,
        [ReconciliationReportMetrics.FinanceHandpays]: 50,
        [ReconciliationReportMetrics.FinanceCreditsOut]: 300,
        [ReconciliationReportMetrics.FinanceTotalOut]: 300
    };

    describe('validation', () => {
        it('should pass validation with valid data', async () => {
            // Arrange
            const reportData = {
                machine: validMachine,
                metrics: validMetrics
            };

            // Act
            const report = MachineWinReconciliationReportItem.create(reportData);
            const errors = await validate(report);

            // Assert
            expect(errors.length).toBe(0);
            expect(report.machine).toEqual(validMachine);
            expect(report.metrics).toEqual(validMetrics);
        });

        it('should fail validation when machine is missing', () => {
            // Arrange
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            const reportData = {
                metrics: validMetrics
            } as unknown as MachineWinReconciliationReportItemProps; // Type assertion to bypass TS errors

            // Act & Assert
            expect(() => MachineWinReconciliationReportItem.create(reportData)).toThrow();
        });

        it('should fail validation when metrics is missing', () => {
            // Arrange
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            const reportData = {
                machine: validMachine
            } as unknown as MachineWinReconciliationReportItemProps; // Type assertion to bypass TS errors

            // Act & Assert
            expect(() => MachineWinReconciliationReportItem.create(reportData)).toThrow();
        });

        it('should fail validation when metrics is not an object', () => {
            // Arrange
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            const reportData = {
                machine: validMachine,
                // Using type assertion to test invalid input
                metrics: 'not an object'
            } as unknown as MachineWinReconciliationReportItemProps;

            // Act & Assert
            expect(() => MachineWinReconciliationReportItem.create(reportData)).toThrow();
        });

        it('should validation when metrics are missing', () => {
            // Arrange
            const incompleteMetrics = {
                [ReconciliationReportMetrics.AccountingTotalWin]: 1000,
                [ReconciliationReportMetrics.CashRevenue]: 500
                // Missing other required fields
            };

            const reportData = {
                machine: validMachine,
                metrics: incompleteMetrics
            };

            // Act
            const report = MachineWinReconciliationReportItem.create(reportData);

            // Assert
            expect(report).toBeDefined();
            expect(report.metrics).toEqual(incompleteMetrics);
        });
    });

    describe('deserialization', () => {
        it('should correctly deserialize from plain object', () => {
            // Arrange
            const reportData = {
                machine: validMachine,
                metrics: validMetrics
            };

            // Act
            const report = MachineWinReconciliationReportItem.create(reportData);

            // Assert
            expect(report).toBeInstanceOf(MachineWinReconciliationReportItem);
            expect(report.machine).toEqual(validMachine);
            expect(report.metrics).toEqual(validMetrics);
        });

        it('should handle partial metrics during deserialization', () => {
            // Arrange
            const partialMetrics = {
                [ReconciliationReportMetrics.AccountingTotalWin]: 1000,
                [ReconciliationReportMetrics.CashRevenue]: 500
                // Missing other fields, but this is just for deserialization testing
            };

            const reportData = {
                machine: validMachine,
                metrics: partialMetrics
            };

            // Act
            const report = MachineWinReconciliationReportItem.create(reportData);

            // Assert
            expect(report).toBeInstanceOf(MachineWinReconciliationReportItem);
            expect(report.machine).toEqual(validMachine);
            expect(report.metrics).toEqual(partialMetrics);
            // Note: This would fail validation, but deserialization should still work
        });
    });
});
