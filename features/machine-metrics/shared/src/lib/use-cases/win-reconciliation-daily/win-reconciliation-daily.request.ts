import { deserialize } from '@cms/serialization';
import { IsGamingDay, IsGreaterThanOrEqualTo } from '@cms/validation';
import { IsEnum } from 'class-validator';

import { ListDailyMetricsProps, WinReconciliationReportType } from '../../types';

export interface WinReconciliationDailyRequestProps extends ListDailyMetricsProps {
    type: WinReconciliationReportType;
}

export class WinReconciliationDailyRequest implements WinReconciliationDailyRequestProps {
    @IsGamingDay()
    readonly fromGamingDay!: string;

    @IsGamingDay()
    @IsGreaterThanOrEqualTo('fromGamingDay')
    readonly toGamingDay!: string;

    @IsEnum(WinReconciliationReportType)
    readonly type!: WinReconciliationReportType;

    static create(props: WinReconciliationDailyRequestProps): WinReconciliationDailyRequest {
        return deserialize(WinReconciliationDailyRequest, props);
    }
}
