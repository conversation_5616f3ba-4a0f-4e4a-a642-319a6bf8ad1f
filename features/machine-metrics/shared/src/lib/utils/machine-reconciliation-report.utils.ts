import {
    MachineHourlyMetricCode,
    MachineMetricCode,
    MachineWinReconciliationReportMetrics,
    ReconciliationReportMetrics,
    WinReconciliationReportType
} from '../types';

export class MachineReconciliationReportUtil {
    static reportMetricsToMetricCodeMapper(
        type: WinReconciliationReportType
    ): Record<ReconciliationReportMetrics, MachineHourlyMetricCode | null> {
        let fieldsToMetricCodeMap = {};
        switch (type) {
            case WinReconciliationReportType.IncludePromo:
                fieldsToMetricCodeMap = {
                    [ReconciliationReportMetrics.AccountingTotalWin]: MachineMetricCode.TotalWinIncPromoMAT100,
                    [ReconciliationReportMetrics.CashRevenue]: MachineMetricCode.CashInMA001,
                    [ReconciliationReportMetrics.TicketRevenue]: MachineMetricCode.TotalTicketInMAT002,
                    [ReconciliationReportMetrics.TransferRevenue]: MachineMetricCode.TotalTransfersOutMAT003,
                    [ReconciliationReportMetrics.AccountingCreditsIn]: MachineMetricCode.StartTotalCreditsMALT001,
                    [ReconciliationReportMetrics.TotalRevenue]: MachineMetricCode.TotalRevenueMAT010,
                    [ReconciliationReportMetrics.TicketExpenses]: MachineMetricCode.TotalTicketsOutMAT012,
                    [ReconciliationReportMetrics.TransferExpenses]: MachineMetricCode.TotalTransfersOutMAT013,
                    [ReconciliationReportMetrics.AccountingHandpays]: MachineMetricCode.HandpaysAuthorizedMA018,
                    [ReconciliationReportMetrics.AccountingCreditsOut]: MachineMetricCode.EndTotalCreditsMALT002,
                    [ReconciliationReportMetrics.AccountingTotalOut]: MachineMetricCode.TotalExpensesMAT020,
                    [ReconciliationReportMetrics.PlayTotalWin]: MachineMetricCode.WinIncPromoMPT100,
                    [ReconciliationReportMetrics.Turnover]: MachineMetricCode.TurnoverIncPromoMPT001,
                    [ReconciliationReportMetrics.PaidToMachine]: MachineMetricCode.PaytablePayoutsToMachineMP005,
                    [ReconciliationReportMetrics.Jackpots]: MachineMetricCode.JackpotHandpaysMP011,
                    [ReconciliationReportMetrics.Progressives]: MachineMetricCode.WinExProgressivesExPromoMP020,
                    [ReconciliationReportMetrics.FinanceTotalWin]: MachineMetricCode.MachineWinIncPromoMFT100,
                    [ReconciliationReportMetrics.FinanceCashIn]: MachineMetricCode.BillsInMF001,
                    [ReconciliationReportMetrics.FinanceTicketsIn]: MachineMetricCode.TotalTicketsInMFT002,
                    [ReconciliationReportMetrics.FinanceTransfersIn]: MachineMetricCode.TotalTransfersInMFT003,
                    [ReconciliationReportMetrics.FinanceCreditsIn]: MachineMetricCode.StartTotalCreditsMALT001,
                    [ReconciliationReportMetrics.FinanceTotalIn]: MachineMetricCode.TotalInMFT10,
                    [ReconciliationReportMetrics.FinanceTicketsOut]: MachineMetricCode.TotalTicketsOutMFT012,
                    [ReconciliationReportMetrics.FinanceTransfersOut]: MachineMetricCode.TotalTransfersOutMFT013,
                    [ReconciliationReportMetrics.FinanceHandpays]: MachineMetricCode.TotalHandpaysMF018,
                    [ReconciliationReportMetrics.FinanceCreditsOut]: MachineMetricCode.EndTotalCreditsMALT002,
                    [ReconciliationReportMetrics.FinanceTotalOut]: MachineMetricCode.TotalOutMFT20
                };
                break;
            case WinReconciliationReportType.ExcludePromo:
                fieldsToMetricCodeMap = {
                    [ReconciliationReportMetrics.AccountingTotalWin]: MachineMetricCode.MachineWinExPromoMA100,
                    [ReconciliationReportMetrics.CashRevenue]: MachineMetricCode.CashInMA001,
                    [ReconciliationReportMetrics.TicketRevenue]: MachineMetricCode.CashTicketsInMA002,
                    [ReconciliationReportMetrics.TransferRevenue]: MachineMetricCode.CashTransfersInMA003,
                    [ReconciliationReportMetrics.AccountingCreditsIn]: MachineMetricCode.StartCreditsMAL001,
                    [ReconciliationReportMetrics.TotalRevenue]: MachineMetricCode.MachineRevenueExPromoMA010,
                    [ReconciliationReportMetrics.TicketExpenses]: MachineMetricCode.CashTicketsOutMA012,
                    [ReconciliationReportMetrics.TransferExpenses]: MachineMetricCode.CashTransfersOutMA013,
                    [ReconciliationReportMetrics.AccountingHandpays]: MachineMetricCode.HandpaysAuthorizedMA018,
                    [ReconciliationReportMetrics.AccountingCreditsOut]: MachineMetricCode.EndCreditsMAL002,
                    [ReconciliationReportMetrics.AccountingTotalOut]: MachineMetricCode.MachineExpensesExPromoMA020,
                    [ReconciliationReportMetrics.PlayTotalWin]: MachineMetricCode.WinExPromoMP100,
                    [ReconciliationReportMetrics.Turnover]: MachineMetricCode.TurnoverExPromoMP001,
                    [ReconciliationReportMetrics.PaidToMachine]: MachineMetricCode.PaytablePayoutsToMachineMP005,
                    [ReconciliationReportMetrics.Jackpots]: MachineMetricCode.JackpotHandpaysMP011,
                    [ReconciliationReportMetrics.Progressives]: MachineMetricCode.WinExProgressivesExPromoMP020,
                    [ReconciliationReportMetrics.FinanceTotalWin]: MachineMetricCode.MachineWinExPromoMF100,
                    [ReconciliationReportMetrics.FinanceCashIn]: MachineMetricCode.BillsInMF001,
                    [ReconciliationReportMetrics.FinanceTicketsIn]: MachineMetricCode.TicketsInMF002,
                    [ReconciliationReportMetrics.FinanceTransfersIn]: MachineMetricCode.TransfersInMF003,
                    [ReconciliationReportMetrics.FinanceCreditsIn]: MachineMetricCode.StartCreditsMAL001,
                    [ReconciliationReportMetrics.FinanceTotalIn]: MachineMetricCode.FundsInMF010,
                    [ReconciliationReportMetrics.FinanceTicketsOut]: MachineMetricCode.TicketsOutMF012,
                    [ReconciliationReportMetrics.FinanceTransfersOut]: MachineMetricCode.TransfersOutMF013,
                    [ReconciliationReportMetrics.FinanceHandpays]: MachineMetricCode.TotalHandpaysMF018,
                    [ReconciliationReportMetrics.FinanceCreditsOut]: MachineMetricCode.EndCreditsMAL002,
                    [ReconciliationReportMetrics.FinanceTotalOut]: MachineMetricCode.FundsOutMF020
                };
                break;
            case WinReconciliationReportType.OnlyPromo:
                fieldsToMetricCodeMap = {
                    [ReconciliationReportMetrics.AccountingTotalWin]: MachineMetricCode.NetPromoMAP100,
                    [ReconciliationReportMetrics.CashRevenue]: null,
                    [ReconciliationReportMetrics.TicketRevenue]: MachineMetricCode.PromoTicketsInMAP002,
                    [ReconciliationReportMetrics.TransferRevenue]: MachineMetricCode.PromoTransfersInMAP003,
                    [ReconciliationReportMetrics.AccountingCreditsIn]: MachineMetricCode.StartPromoCreditsMALP001,
                    [ReconciliationReportMetrics.TotalRevenue]: MachineMetricCode.PromoRevenueMAP010,
                    [ReconciliationReportMetrics.TicketExpenses]: MachineMetricCode.PromoTicketsOutMAP012,
                    [ReconciliationReportMetrics.TransferExpenses]: MachineMetricCode.PromoTransfersOutMAP013,
                    [ReconciliationReportMetrics.AccountingHandpays]: null,
                    [ReconciliationReportMetrics.AccountingCreditsOut]: MachineMetricCode.EndPromoCreditsMALP002,
                    [ReconciliationReportMetrics.AccountingTotalOut]: MachineMetricCode.PromoExpensesMAP020,
                    [ReconciliationReportMetrics.PlayTotalWin]: MachineMetricCode.PromoTurnoverMPP001,
                    [ReconciliationReportMetrics.Turnover]: MachineMetricCode.PromoTurnoverMPP001,
                    [ReconciliationReportMetrics.PaidToMachine]: null,
                    [ReconciliationReportMetrics.Jackpots]: null,
                    [ReconciliationReportMetrics.Progressives]: null,
                    [ReconciliationReportMetrics.FinanceTotalWin]: MachineMetricCode.NetPromoMFP100,
                    [ReconciliationReportMetrics.FinanceCashIn]: null,
                    [ReconciliationReportMetrics.FinanceTicketsIn]: MachineMetricCode.PromoTicketsInMFP002,
                    [ReconciliationReportMetrics.FinanceTransfersIn]: MachineMetricCode.PromoTransfersInMFP003,
                    [ReconciliationReportMetrics.FinanceCreditsIn]: MachineMetricCode.StartPromoCreditsMALP001,
                    [ReconciliationReportMetrics.FinanceTotalIn]: MachineMetricCode.TotalPromoInMFP010,
                    [ReconciliationReportMetrics.FinanceTicketsOut]: MachineMetricCode.PromoTicketsOutMFP012,
                    [ReconciliationReportMetrics.FinanceTransfersOut]: MachineMetricCode.PromoTransfersOutMFP013,
                    [ReconciliationReportMetrics.FinanceHandpays]: null,
                    [ReconciliationReportMetrics.FinanceCreditsOut]: MachineMetricCode.EndPromoCreditsMALP002,
                    [ReconciliationReportMetrics.FinanceTotalOut]: MachineMetricCode.TotalPromoOutMFP020
                };
                break;
            default:
                break;
        }
        return fieldsToMetricCodeMap as Record<ReconciliationReportMetrics, MachineMetricCode | null>;
    }

    /**
     * Extracts the report metrics from the given metrics based on the specified report type
     * This function will return the full list of metrics needed for machine win reconciliation report.
     * The metrics which were not provided will be set to `null` in the output.
     * @param type The type of the report
     * @param metrics The metrics to extract from
     * @returns MachineWinReconciliationReportMetrics object with values set to `null` for metrics that were not provided.
     */
    static extractReportMetricsFromAllMetricsByType(
        type: WinReconciliationReportType,
        metrics: Partial<Record<MachineHourlyMetricCode, number>>
    ): MachineWinReconciliationReportMetrics {
        const reportMetricsToMetricCodes = MachineReconciliationReportUtil.reportMetricsToMetricCodeMapper(type);
        const output: MachineWinReconciliationReportMetrics = {};

        for (const [metric, metricCode] of Object.entries(reportMetricsToMetricCodes)) {
            if (metricCode === null) {
                output[metric as ReconciliationReportMetrics] = null;
                continue;
            }
            output[metric as ReconciliationReportMetrics] = metricCode in metrics ? metrics[metricCode] : null;
        }

        return output;
    }
}
