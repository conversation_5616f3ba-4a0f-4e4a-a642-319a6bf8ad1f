import { deserialize } from '@cms/serialization';
import { MaxTimeDifferenceInHours } from '@cms/validation';
import { BaseQuery } from '@tronius/shared-common';
import { Type } from 'class-transformer';
import { IsDate, IsEnum } from 'class-validator';

import { WinReconciliationReportType } from '../types';
import { WinReconciliationHourlyRequestProps } from '../use-cases';

export class GetWinReconciliationHourlyReportQuery extends BaseQuery implements WinReconciliationHourlyRequestProps {
    @IsDate()
    @Type(() => Date)
    readonly from!: Date;

    @IsDate()
    @Type(() => Date)
    // eslint-disable-next-line @typescript-eslint/no-magic-numbers
    @MaxTimeDifferenceInHours('from', 24)
    readonly to!: Date;

    @IsEnum(WinReconciliationReportType)
    readonly type!: WinReconciliationReportType;

    static create(props: WinReconciliationHourlyRequestProps): GetWinReconciliationHourlyReportQuery {
        return deserialize(GetWinReconciliationHourlyReportQuery, props);
    }
}
