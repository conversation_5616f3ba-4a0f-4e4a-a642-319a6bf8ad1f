import { UUIDFactory } from '@tronius/shared-common';

import { GetWinReconciliationHourlyReportQuery } from './get-win-reconciliation-hourly-report.query';
import { WinReconciliationReportType } from '../types';
import { WinReconciliationHourlyRequestProps } from '../use-cases';

const validProps = [
    {
        machineId: UUIDFactory.create(),
        from: new Date('2025-05-15T10:00:00Z'),
        to: new Date('2025-05-15T12:00:00Z'), // 2 hours later
        type: WinReconciliationReportType.IncludePromo
    },
    {
        machineId: UUIDFactory.create(),
        from: '2025-05-15T10:00:00Z',
        to: '2025-05-15T12:00:00Z', // 2 hours later
        type: WinReconciliationReportType.ExcludePromo
    }
];

const invalidProps = [
    { machineId: UUIDFactory.create() },
    { from: new Date() },
    { to: new Date() },
    // Missing type
    {
        machineId: UUIDFactory.create(),
        from: new Date('2025-05-14T10:00:00Z'),
        to: new Date('2025-05-14T12:00:00Z')
    },
    // More than 24hr apart
    {
        machineId: UUIDFactory.create(),
        from: new Date('2025-05-14T10:00:00Z'),
        to: new Date('2025-05-15T12:00:00Z'),
        type: WinReconciliationReportType.IncludePromo
    }
];

describe('GetWinReconciliationHourlyReportQuery', () => {
    it.each(validProps)('should deserialize valid query props', (valid) => {
        // When
        const result = GetWinReconciliationHourlyReportQuery.create(valid as unknown as WinReconciliationHourlyRequestProps);

        // Then
        expect(result).toBeInstanceOf(GetWinReconciliationHourlyReportQuery);
    });

    it.each(invalidProps)('should throw for invalid query props', (invalid) => {
        // Then
        expect(() => {
            GetWinReconciliationHourlyReportQuery.create(invalid as unknown as WinReconciliationHourlyRequestProps);
        }).toThrow();
    });
});
