import { deserialize } from '@cms/serialization';

import {
    GetWinReconciliationReportQueryResult,
    GetWinReconciliationReportQueryResultProps
} from './get-win-reconciliation-report.query.result';

export class GetWinReconciliationHourlyReportQueryResult extends GetWinReconciliationReportQueryResult {
    static override create(props: GetWinReconciliationReportQueryResultProps): GetWinReconciliationHourlyReportQueryResult {
        return deserialize(GetWinReconciliationHourlyReportQueryResult, props);
    }
}
