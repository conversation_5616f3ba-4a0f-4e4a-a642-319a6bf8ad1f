import { GetWinReconciliationDailyReportQuery } from './get-win-reconciliation-daily-report.query';
import { WinReconciliationReportType } from '../types';
import { WinReconciliationDailyRequestProps } from '../use-cases';

const validProps = [
    {
        fromGamingDay: '2025-05-15',
        toGamingDay: '2025-05-15',
        type: WinReconciliationReportType.IncludePromo
    }
];

const invalidProps = [
    {}, // Empty object
    { fromGamingDay: '2025-05-15' }, // Missing toGamingDay and type
    { toGamingDay: '2025-05-15' }, // Missing fromGamingDay and type
    { type: WinReconciliationReportType.IncludePromo }, // Missing gaming days
    { fromGamingDay: '2025-05-15T10:00:00Z', toGamingDay: '2025-05-15', type: WinReconciliationReportType.IncludePromo }, // Invalid fromGamingDay format
    { fromGamingDay: '2025-05-15', toGamingDay: '2025-05-15T10:00:00Z', type: WinReconciliationReportType.IncludePromo }, // Invalid toGamingDay format
    { fromGamingDay: 'invalid-date', toGamingDay: '2025-05-15', type: WinReconciliationReportType.IncludePromo }, // Invalid fromGamingDay
    { fromGamingDay: '2025-05-16', toGamingDay: '2025-05-15', type: WinReconciliationReportType.IncludePromo } // ToGamingDay before fromGamingDay
];

describe('GetWinReconciliationDailyReportQuery', () => {
    it.each(validProps)('should deserialize valid query props', (valid) => {
        // When
        const result = GetWinReconciliationDailyReportQuery.create(valid as unknown as WinReconciliationDailyRequestProps);

        // Then
        expect(result).toBeInstanceOf(GetWinReconciliationDailyReportQuery);
    });

    it.each(invalidProps)('should throw for invalid query props', (invalid) => {
        // Then
        expect(() => {
            GetWinReconciliationDailyReportQuery.create(invalid as unknown as WinReconciliationDailyRequestProps);
        }).toThrow();
    });
});
