import { deserialize } from '@cms/serialization';
import { QueryResult, QueryResultProps } from '@tronius/shared-common';

import { MachineWinReconciliationReportItem } from '../types';

export interface GetWinReconciliationReportQueryResultProps extends QueryResultProps {
    reportItems: MachineWinReconciliationReportItem[];
}

export abstract class GetWinReconciliationReportQueryResult extends QueryResult implements GetWinReconciliationReportQueryResultProps {
    reportItems!: MachineWinReconciliationReportItem[];

    static create(props: GetWinReconciliationReportQueryResultProps): GetWinReconciliationReportQueryResult {
        return deserialize(GetWinReconciliationReportQueryResult, props);
    }
}
