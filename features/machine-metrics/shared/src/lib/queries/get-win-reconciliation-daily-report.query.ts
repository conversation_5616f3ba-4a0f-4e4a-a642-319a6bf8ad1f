import { deserialize } from '@cms/serialization';
import { IsGamingDay, IsGreaterThanOrEqualTo } from '@cms/validation';
import { BaseQuery } from '@tronius/shared-common';
import { IsEnum } from 'class-validator';

import { WinReconciliationReportType } from '../types';
import { WinReconciliationDailyRequestProps } from '../use-cases';

export class GetWinReconciliationDailyReportQuery extends BaseQuery implements WinReconciliationDailyRequestProps {
    @IsGamingDay()
    readonly fromGamingDay!: string;

    @IsGamingDay()
    @IsGreaterThanOrEqualTo('fromGamingDay')
    readonly toGamingDay!: string;

    @IsEnum(WinReconciliationReportType)
    readonly type!: WinReconciliationReportType;

    static create(props: WinReconciliationDailyRequestProps): GetWinReconciliationDailyReportQuery {
        return deserialize(GetWinReconciliationDailyReportQuery, props);
    }
}
