import { UUIDFactory } from '@tronius/shared-common';
import { MachineProfileListItem } from '@tronius/shared-domain';

import { ReconciliationReportMetrics } from '../types';
import {
    GetWinReconciliationReportQueryResult,
    GetWinReconciliationReportQueryResultProps
} from './get-win-reconciliation-report.query.result';

const validMachine: MachineProfileListItem = {
    id: UUIDFactory.create(),
    location: 'Test Location',
    assetNumber: 123,
    denomination: 1,
    manufacturerId: UUIDFactory.create(),
    manufacturerName: 'Test Manufacturer',
    machineGameTypeId: UUIDFactory.create(),
    machineGameTypeName: 'Test Game Type'
};

const validMetrics = {
    [ReconciliationReportMetrics.AccountingTotalWin]: 1000,
    [ReconciliationReportMetrics.CashRevenue]: 500,
    [ReconciliationReportMetrics.TicketRevenue]: 300,
    [ReconciliationReportMetrics.TotalRevenue]: 800
};

const validProps: GetWinReconciliationReportQueryResultProps = {
    correlationId: UUIDFactory.create(),
    reportItems: [
        {
            machine: validMachine,
            metrics: validMetrics
        }
    ]
};

class TestGetWinReconciliationReportQueryResult extends GetWinReconciliationReportQueryResult {
    // Concrete implementation for testing
}

describe('GetWinReconciliationReportQueryResult', () => {
    it('should deserialize valid query result props', () => {
        // When
        const result = TestGetWinReconciliationReportQueryResult.create(validProps);

        // Then
        expect(result).toBeInstanceOf(GetWinReconciliationReportQueryResult);
        expect(result.correlationId).toBe(validProps.correlationId);
        expect(result.reportItems).toHaveLength(1);
        expect(result.reportItems[0].machine.id).toBe(validMachine.id);
        expect(result.reportItems[0].metrics).toEqual(validMetrics);
    });

    it('should deserialize with multiple report items', () => {
        // Given
        const propsWithMultipleItems = {
            ...validProps,
            reportItems: [
                {
                    machine: validMachine,
                    metrics: validMetrics
                },
                {
                    machine: {
                        ...validMachine,
                        id: UUIDFactory.create(),
                        location: 'Second Location'
                    },
                    metrics: {
                        [ReconciliationReportMetrics.AccountingTotalWin]: 2000,
                        [ReconciliationReportMetrics.TotalRevenue]: 1500
                    }
                }
            ]
        };

        // When
        const result = TestGetWinReconciliationReportQueryResult.create(propsWithMultipleItems);

        // Then
        expect(result).toBeInstanceOf(GetWinReconciliationReportQueryResult);
        expect(result.reportItems).toHaveLength(2);
        expect(result.reportItems[0].machine.location).toBe('Test Location');
        expect(result.reportItems[1].machine.location).toBe('Second Location');
    });

    it('should deserialize with empty report items', () => {
        // Given
        const propsWithEmptyItems = {
            correlationId: UUIDFactory.create(),
            reportItems: []
        };

        // When
        const result = TestGetWinReconciliationReportQueryResult.create(propsWithEmptyItems);

        // Then
        expect(result).toBeInstanceOf(GetWinReconciliationReportQueryResult);
        expect(result.reportItems).toHaveLength(0);
    });

    describe('validation', () => {
        // Since we're testing an abstract class, we'll test the validation indirectly
        // by checking the structure of the class and its properties

        it('should require correlationId', () => {
            // Check that the class extends QueryResult which requires correlationId
            const result = TestGetWinReconciliationReportQueryResult.create(validProps);

            // Verify that correlationId is properly set
            expect(result.correlationId).toBe(validProps.correlationId);

            // Verify that the class has the right inheritance
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            expect(Object.getPrototypeOf(GetWinReconciliationReportQueryResult.prototype).constructor.name).toBe('QueryResult');
        });

        it('should validate reportItems as an array', () => {
            // Create an instance to check the property
            const result = TestGetWinReconciliationReportQueryResult.create(validProps);

            // Check that reportItems is an array
            expect(Array.isArray(result.reportItems)).toBe(true);
        });

        it('should properly map report items', () => {
            // Test that the create method properly maps the report items
            const result = TestGetWinReconciliationReportQueryResult.create(validProps);

            // Verify that the report items are instances of MachineWinReconciliationReportItem
            expect(result.reportItems[0]).toHaveProperty('machine');
            expect(result.reportItems[0]).toHaveProperty('metrics');

            // Verify that the machine properties are mapped correctly
            expect(result.reportItems[0].machine.id).toBe(validMachine.id);
            expect(result.reportItems[0].machine.location).toBe(validMachine.location);

            // Verify that the metrics are mapped correctly
            expect(result.reportItems[0].metrics).toEqual(validMetrics);
        });
    });
});
