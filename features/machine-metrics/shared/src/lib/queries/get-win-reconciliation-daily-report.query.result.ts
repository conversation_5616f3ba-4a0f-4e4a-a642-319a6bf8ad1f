import { deserialize } from '@cms/serialization';

import {
    GetWinReconciliationReportQueryResult,
    GetWinReconciliationReportQueryResultProps
} from './get-win-reconciliation-report.query.result';

export class GetWinReconciliationDailyReportQueryResult extends GetWinReconciliationReportQueryResult {
    static override create(props: GetWinReconciliationReportQueryResultProps): GetWinReconciliationDailyReportQueryResult {
        return deserialize(GetWinReconciliationDailyReportQueryResult, props);
    }
}
