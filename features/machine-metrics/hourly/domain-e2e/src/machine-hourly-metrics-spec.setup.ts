import { BullQueueProviderAdapter, BullmqQueueModule } from '@cms/bullmq-queue';
import { GetCashOperationsMetricsForPeriodQuery, GetCashOperationsMetricsForPeriodQueryResult } from '@cms/cash-operations';
import { InMemoryMessageBrokerModule } from '@cms/in-memory-message-broker';
import { JobQueueModule } from '@cms/job-queue';
import {
    GetMachineFundsTransferMetricsForPeriodQuery,
    GetMachineFundsTransferMetricsForPeriodQueryResult
} from '@cms/machine-funds-transfers';
import { GetMachineHandpayMetricsForPeriodQuery } from '@cms/machine-handpays';
import { MachineHourlyMetricsDomainModule } from '@cms/machine-hourly-metrics-domain';
import { MessageBroker, MessageBrokerModule, UnsubscribeHandlerMethod } from '@cms/message-broker';
import { GetTicketsMetricsForPeriodQuery, GetTicketsMetricsForPeriodQueryResult } from '@cms/tickets';
import { INestApplication } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Test } from '@nestjs/testing';
import { BaseErrorFilter, TgExceptionFilter, TgValidationPipe } from '@tronius/backend-common';
import { CmsDatabaseModule, MachineHourlyMetricsOrmEntity } from '@tronius/backend-database';
import {
    GetMachineCurrentByIdQuery,
    GetMachineMeterSnapshotsForDailyOrHourlyContextQuery,
    GetMachineProfileByIdQuery,
    OperationalStatus,
    SasMeterCode
} from '@tronius/shared-domain';
import { EntityManager, Repository } from 'typeorm';

export interface TestContext {
    app: INestApplication;
    messageBroker: MessageBroker;
    machineHourlyMetricsRepository: Repository<MachineHourlyMetricsOrmEntity>;
    unsubscribeQueries: UnsubscribeHandlerMethod[];
}

export const setupTestModule = async (): Promise<TestContext> => {
    const testModule = await Test.createTestingModule({
        imports: [
            ConfigModule.forRoot({ isGlobal: true }),
            MessageBrokerModule.forRootAsync({
                imports: [InMemoryMessageBrokerModule.forRoot({ awaitHandlerExecution: true, maxListenersPerEvent: 100 })]
            }),
            CmsDatabaseModule.forRootAsync(),
            JobQueueModule.forRootAsync({
                imports: [BullmqQueueModule.forE2E()],
                inject: [BullQueueProviderAdapter],
                useFactory: (bullQueueProvider: BullQueueProviderAdapter) => bullQueueProvider
            }),
            MachineHourlyMetricsDomainModule
        ]
    }).compile();

    const app = testModule.createNestApplication();
    app.useGlobalFilters(new TgExceptionFilter(), new BaseErrorFilter());
    app.useGlobalPipes(new TgValidationPipe());
    await app.init();

    const messageBroker = testModule.get(MessageBroker);
    const machineHourlyMetricsRepository = testModule.get(EntityManager).getRepository(MachineHourlyMetricsOrmEntity);

    // Register mock query handlers
    const unsubscribeQueries: UnsubscribeHandlerMethod[] = [];

    // Mock the query method
    const originalQuery = messageBroker.query;
    messageBroker.query = jest.fn().mockImplementation(async (query: any, resultClass: any) => {
        if (query instanceof GetMachineMeterSnapshotsForDailyOrHourlyContextQuery) {
            // Create mock meter collections
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

            return {
                correlationId: query.correlationId,
                lastOperationalStatusBeforePreviousCollection: OperationalStatus.Live,
                previousSnapshotCollection: {
                    collectedAt: oneHourAgo,
                    meters: {
                        [SasMeterCode.SAS0000]: 1000, // Coin in
                        [SasMeterCode.SAS0001]: 800, // Coin out
                        [SasMeterCode.SAS0002]: 200, // Jackpot
                        [SasMeterCode.SAS0003]: 100, // Games played
                        [SasMeterCode.SAS0004]: 50, // Games won
                        [SasMeterCode.SAS0005]: 10, // Games lost
                        [SasMeterCode.SAS0006]: 5, // Hand pays
                        [SasMeterCode.SAS0007]: 2, // Canceled credits
                        [SasMeterCode.SAS0008]: 1, // Hopper fills
                        [SasMeterCode.SAS0009]: 0, // Cashable in
                        [SasMeterCode.SAS0010]: 0 // Cashable out
                    }
                },
                operationalStatusChangeSnapshotCollections: [],
                currentSnapshotCollection: {
                    collectedAt: now,
                    meters: {
                        [SasMeterCode.SAS0000]: 1500, // Coin in
                        [SasMeterCode.SAS0001]: 1200, // Coin out
                        [SasMeterCode.SAS0002]: 300, // Jackpot
                        [SasMeterCode.SAS0003]: 150, // Games played
                        [SasMeterCode.SAS0004]: 75, // Games won
                        [SasMeterCode.SAS0005]: 15, // Games lost
                        [SasMeterCode.SAS0006]: 8, // Hand pays
                        [SasMeterCode.SAS0007]: 3, // Canceled credits
                        [SasMeterCode.SAS0008]: 2, // Hopper fills
                        [SasMeterCode.SAS0009]: 0, // Cashable in
                        [SasMeterCode.SAS0010]: 0 // Cashable out
                    }
                }
            };
        }

        if (query instanceof GetMachineProfileByIdQuery) {
            return {
                correlationId: query.correlationId,
                machineProfile: {
                    id: query.machineId,
                    location: 'Test Location',
                    denomination: 0.01,
                    assetNumber: 123, // Changed to number
                    manufacturerId: 'MANUF-001',
                    games: 'Game 1, Game 2' // Changed to string
                }
            };
        }

        if (query instanceof GetMachineCurrentByIdQuery) {
            return {
                correlationId: query.correlationId,
                machineCurrent: {
                    id: query.machineId,
                    currentOperationalStatus: OperationalStatus.Live,
                    status: 'ONLINE',
                    sessionState: 'IDLE',
                    session: null,
                    intendedOperationalStatus: OperationalStatus.Live,
                    // Add other required properties
                    lastSeenAt: new Date(),
                    lastMetersCollectedAt: new Date(),
                    lastOperationalStatusChangedAt: new Date(),
                    lastSessionEndedAt: null,
                    lastSessionStartedAt: null,
                    lastTicketPrintedAt: null,
                    lastHandpayAt: null,
                    lastCardInsertedAt: null,
                    lastCardRemovedAt: null,
                    lastBillInsertedAt: null,
                    lastTransferInAt: null,
                    lastTransferOutAt: null
                }
            };
        }

        // Mock cash operations metrics query
        if (query instanceof GetCashOperationsMetricsForPeriodQuery) {
            return GetCashOperationsMetricsForPeriodQueryResult.create({
                correlationId: query.correlationId,
                acceptedAmount: 1000,
                issuedAmount: 0
            });
        }

        // Mock tickets metrics query
        if (query instanceof GetTicketsMetricsForPeriodQuery) {
            return GetTicketsMetricsForPeriodQueryResult.create({
                correlationId: query.correlationId,
                printedCashAmount: 200,
                redeemedCashAmount: 150,
                printedPromoRestrictedAmount: 300,
                redeemedPromoRestrictedAmount: 250
            });
        }

        // Mock handpay metrics query
        if (query instanceof GetMachineHandpayMetricsForPeriodQuery) {
            return {
                correlationId: query.correlationId,
                metricsByType: [],
                totalMetrics: {
                    authorizedAmount: 750,
                    paidAmount: 700,
                    pendingAmount: 50,
                    voidedAmount: 0,
                    reversedAmount: 0,
                    totalAccounting: 750
                }
            };
        }

        // Mock machine funds transfer metrics query
        if (query instanceof GetMachineFundsTransferMetricsForPeriodQuery) {
            return GetMachineFundsTransferMetricsForPeriodQueryResult.create({
                correlationId: query.correlationId,
                metrics: {
                    cashableAmountToMachine: 400,
                    cashableAmountFromMachine: 300,
                    restrictedAmountToMachine: 200,
                    restrictedAmountFromMachine: 100,
                    cashableAmountToMachineAdjustment: 0,
                    cashableAmountFromMachineAdjustment: 0,
                    restrictedAmountToMachineAdjustment: 0,
                    restrictedAmountFromMachineAdjustment: 0
                }
            });
        }

        // Fall back to original implementation for other queries
        return originalQuery.call(messageBroker, query, resultClass);
    });

    return {
        app,
        messageBroker,
        machineHourlyMetricsRepository,
        unsubscribeQueries
    };
};

export const cleanupTestModule = async (context: TestContext): Promise<void> => {
    await Promise.all(context.unsubscribeQueries.map(async (unsubscribe) => context.messageBroker.unsubscribeQueryHandler(unsubscribe)));
    await context.app.close();
};
