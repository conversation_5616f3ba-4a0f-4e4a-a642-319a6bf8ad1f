import { GetWinReconciliationHourlyReportQuery, GetWinReconciliationHourlyReportQueryResult } from '@cms/machine-metrics';
import { OnQuery, QueryHandler } from '@cms/message-broker';
import { Injectable } from '@nestjs/common';

import { WinReconciliationReportService } from '../services';

@OnQuery(GetWinReconciliationHourlyReportQuery, GetWinReconciliationHourlyReportQueryResult)
@Injectable()
export class GetWinReconciliationHourlyReportQueryHandler
    implements QueryHandler<GetWinReconciliationHourlyReportQuery, GetWinReconciliationHourlyReportQueryResult>
{
    constructor(private readonly winReconciliationReportService: WinReconciliationReportService) {}

    async handle({
        correlationId,
        from,
        to,
        type
    }: GetWinReconciliationHourlyReportQuery): Promise<GetWinReconciliationHourlyReportQueryResult> {
        const reportItems = await this.winReconciliationReportService.list({
            from,
            to,
            type
        });

        return GetWinReconciliationHourlyReportQueryResult.create({ correlationId, reportItems });
    }
}
