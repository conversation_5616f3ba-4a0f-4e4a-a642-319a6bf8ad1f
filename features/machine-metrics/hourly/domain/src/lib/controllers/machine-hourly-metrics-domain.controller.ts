import { MachineWinReconciliationReportItem } from '@cms/machine-metrics';
import { CmsPermissions } from '@cms/server-permissions';
import { Controller, Get, Query } from '@nestjs/common';
import { ApiOAuth2, ApiTags } from '@nestjs/swagger';
import { CmsAction } from '@tronius/shared-domain';

import { WinReconciliationHourlyRequestDto } from '../dto';
import { WinReconciliationReportService } from '../services';

@ApiTags('Machine Hourly Metrics')
@ApiOAuth2([])
@Controller('machine-hourly-metrics')
export class MachineHourlyMetricsController {
    constructor(private readonly service: WinReconciliationReportService) {}

    @Get('/reconciliation-report')
    @CmsPermissions(CmsAction.MachineMetricsList)
    async getReconciliationReport(@Query() query: WinReconciliationHourlyRequestDto): Promise<MachineWinReconciliationReportItem[]> {
        return this.service.list(query);
    }
}
