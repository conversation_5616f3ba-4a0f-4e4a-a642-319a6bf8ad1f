import {
    MachineHourlyMetricCode,
    MachineReconciliationReportUtil,
    MachineWinReconciliationReportItem,
    MachineWinReconciliationReportMetrics,
    ReconciliationReportMetrics,
    WinReconciliationHourlyRequest,
    WinReconciliationReportType
} from '@cms/machine-metrics';
import { MessageBroker } from '@cms/message-broker';
import { Injectable, Logger } from '@nestjs/common';
import { NotFoundError } from '@tronius/shared-common';
import { GetAllMachineProfilesQuery, GetAllMachineProfilesQueryResult, MachineProfileListItem } from '@tronius/shared-domain';

import { MachineHourlyMetricsService } from './machine-hourly-metrics.service';

/**
 * Service responsible for hourly win reconciliation report generation
 */
@Injectable()
export class WinReconciliationReportService {
    private readonly logger = new Logger(WinReconciliationReportService.name);

    constructor(
        private readonly machineHourlyMetricsService: MachineHourlyMetricsService,
        private readonly messageBroker: MessageBroker
    ) {}

    async list(request: WinReconciliationHourlyRequest): Promise<MachineWinReconciliationReportItem[]> {
        const { type, ...listProps } = request;

        const [machinesHourlyMetrics, { machineProfiles }] = await Promise.all([
            this.machineHourlyMetricsService.list(listProps),
            this.messageBroker.query(GetAllMachineProfilesQuery.create(), GetAllMachineProfilesQueryResult)
        ]);
        const machineById = new Map<string, MachineProfileListItem>(machineProfiles.map((machine) => [machine.id, machine]));

        if (machinesHourlyMetrics.length === 0) {
            return [];
        }

        const machineIds: Set<string> = new Set();
        const reportMetricsByMachineId = machinesHourlyMetrics.reduce<Record<string, MachineWinReconciliationReportMetrics>>(
            (acc, machineHourlyMetric) => {
                machineIds.add(machineHourlyMetric.machineId);
                const newMetrics = this.extractReportMetricsFromAllMetricsByType(type, machineHourlyMetric.metrics);

                // If the machine ID already exists in the accumulator, merge and sum the metrics
                if (machineHourlyMetric.machineId in acc) {
                    const existingMetrics = acc[machineHourlyMetric.machineId];

                    // Merge metrics by adding values for the same keys
                    Object.entries(newMetrics).forEach(([key, value]) => {
                        if (value === null) {
                            return;
                        }
                        const metricKey = key as ReconciliationReportMetrics;
                        const existingValue = existingMetrics[metricKey];
                        existingMetrics[metricKey] = existingValue ? existingValue + value : value;
                    });
                } else {
                    acc[machineHourlyMetric.machineId] = newMetrics;
                }

                return acc;
            },
            {}
        );

        return Array.from(machineIds).map((machineId) => {
            const machine = machineById.get(machineId);
            const metrics = reportMetricsByMachineId[machineId];
            if (!machine) {
                throw new NotFoundError({
                    message: `Machine with id ${machineId} has metrics but no machine profile`,
                    params: { machineId, metrics }
                });
            }
            return MachineWinReconciliationReportItem.create({
                machine,
                metrics
            });
        });
    }

    private extractReportMetricsFromAllMetricsByType(
        type: WinReconciliationReportType,
        metrics: Partial<Record<MachineHourlyMetricCode, number>>
    ): MachineWinReconciliationReportMetrics {
        const reportMetricsToMetricCodes = MachineReconciliationReportUtil.reportMetricsToMetricCodeMapper(type);
        const output: MachineWinReconciliationReportMetrics = {};

        for (const [metric, metricCode] of Object.entries(reportMetricsToMetricCodes)) {
            if (metricCode === null) {
                output[metric as ReconciliationReportMetrics] = null;
                continue;
            }
            output[metric as ReconciliationReportMetrics] = metricCode in metrics ? metrics[metricCode] : null;
        }

        return output;
    }
}
