import { JobQueueModule } from '@cms/job-queue';
import { UpsertHourlyMetricsJob } from '@cms/machine-metrics';
import { Module } from '@nestjs/common';

import { MachineHourlyMetricsController } from './controllers';
import {
    GetWinReconciliationHourlyReportQueryHandler,
    ScheduleNextHourOnMetricsUpsertedHandler,
    UpsertHourlyMetricsOnSnapshotsAddedHandler
} from './handlers';
import { ExtraDependenciesProvider, MachineMetersProvider, MachineProvider } from './providers';
import { MachineHourlyMetricsService, WinReconciliationReportService } from './services';
import { UpsertHourlyMetricsWorker } from './workers';

@Module({
    imports: [JobQueueModule.forJob(UpsertHourlyMetricsJob)],
    controllers: [MachineHourlyMetricsController],
    providers: [
        MachineMetersProvider,
        WinReconciliationReportService,
        MachineProvider,
        ExtraDependenciesProvider,
        MachineHourlyMetricsService,
        UpsertHourlyMetricsWorker,
        UpsertHourlyMetricsOnSnapshotsAddedHandler,
        ScheduleNextHourOnMetricsUpsertedHandler,
        GetWinReconciliationHourlyReportQueryHandler
    ]
})
export class MachineHourlyMetricsDomainModule {}
