{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2021", "module": "esnext", "lib": ["es2022", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "strict": true, "baseUrl": ".", "paths": {"@cms/address": ["features/address/shared/src/index.ts"], "@cms/address-domain": ["features/address/domain/src/index.ts"], "@cms/assets": ["features/assets/shared/src/index.ts"], "@cms/assets-domain": ["features/assets/domain/src/index.ts"], "@cms/bullmq-queue": ["libs/backend/bullmq-queue/src/index.ts"], "@cms/bullmq-queue-e2e": ["libs/backend/bullmq-queue-e2e/src/index.ts"], "@cms/card-user": ["libs/backend/card-user/src/index.ts"], "@cms/carded-machine-session-wallet-transfers": ["features/machine-funds-transfers/carded-machine-session-wallet-transfers/src/index.ts"], "@cms/carded-machine-session-wallet-transfers-e2e": ["features/machine-funds-transfers/carded-machine-session-wallet-transfers-e2e/src/index.ts"], "@cms/cash-operations": ["features/cash-operations/shared/src/index.ts"], "@cms/cash-operations-domain": ["features/cash-operations/domain/src/index.ts"], "@cms/casino-operator": ["features/casino-operator/shared/src/index.ts"], "@cms/casino-operator-domain": ["features/casino-operator/domain/src/index.ts"], "@cms/chip-operations": ["features/chip-operations/shared/src/index.ts"], "@cms/chip-operations-domain": ["features/chip-operations/domain/src/index.ts"], "@cms/client-permissions": ["libs/frontend/permissions/src/index.ts"], "@cms/cms-cookies": ["libs/backend/cms-cookies/src/index.ts"], "@cms/cms-device-user": ["libs/backend/cms-device-user/src/index.ts"], "@cms/cms-keycloak-connect": ["libs/backend/cms-keycloak-connect/src/index.ts"], "@cms/cms-users": ["libs/backend/cms-users/src/index.ts"], "@cms/country": ["features/country/shared/src/index.ts"], "@cms/country-domain": ["features/country/domain/src/index.ts"], "@cms/customer-metrics": ["features/customer-metrics/shared/src/index.ts"], "@cms/customer-metrics-domain": ["features/customer-metrics/domain/src/index.ts"], "@cms/customer-metrics-domain-e2e": ["features/customer-metrics/domain-e2e/src/index.ts"], "@cms/customer-statement": ["features/customer-statement/shared/src/index.ts"], "@cms/customer-statement-domain": ["features/customer-statement/domain/src/index.ts"], "@cms/customer-statement-domain-e2e": ["features/customer-statement/domain-e2e/src/index.ts"], "@cms/customer-suspensions": ["features/customer-suspensions/shared/src/index.ts"], "@cms/customer-suspensions-domain": ["features/customer-suspensions/domain/src/index.ts"], "@cms/customer-transactions": ["libs/backend/customer-transactions/src/index.ts"], "@cms/customer-transactions-e2e": ["libs/backend/customer-transactions-e2e/src/index.ts"], "@cms/customers": ["features/customers/shared/src/index.ts"], "@cms/customers-api": ["features/customers/api/src/index.ts"], "@cms/customers-api-shared": ["features/customers/api-shared/src/index.ts"], "@cms/customers-domain": ["features/customers/domain/src/index.ts"], "@cms/customers-io-domain": ["features/integrations/customers-io/domain/src/index.ts"], "@cms/denominations": ["features/denominations/shared/src/index.ts"], "@cms/denominations-domain": ["features/denominations/domain/src/index.ts"], "@cms/device-firmware": ["features/device-firmware/shared/src/index.ts"], "@cms/device-firmware-domain": ["features/device-firmware/domain/src/index.ts"], "@cms/elastic-search": ["libs/backend/elastic-search/src/index.ts"], "@cms/entity-audit": ["features/entity-audit/shared/src/index.ts"], "@cms/entity-audit-domain": ["features/entity-audit/domain/src/index.ts"], "@cms/eslint-config": ["libs/eslint/src/index.js"], "@cms/event-logs-domain": ["features/event-logs/domain/src/index.ts"], "@cms/execution-context": ["features/execution-context/execution-context/src/index.ts"], "@cms/execution-context-e2e": ["features/execution-context/e2e/src/index.ts"], "@cms/fe-cashier": ["libs/frontend/cashier/src/index.ts"], "@cms/fe-components": ["libs/frontend/components/src/index.ts"], "@cms/fe-customers": ["libs/frontend/customers/src/index.ts"], "@cms/fe-home": ["libs/frontend/home/<USER>/index.ts"], "@cms/in-memory-message-broker": ["libs/backend/in-memory-message-broker/src/index.ts"], "@cms/job-queue": ["libs/backend/job-queue/src/index.ts"], "@cms/keycloak-admin-client": ["libs/backend/keycloak-admin-client/src/index.ts"], "@cms/kyc": ["features/kyc/shared/src/index.ts"], "@cms/kyc-domain": ["features/kyc/domain/src/index.ts"], "@cms/logger": ["libs/backend/infrastructure/logging/src/index.ts"], "@cms/loyalty": ["features/loyalty/shared/src/index.ts"], "@cms/loyalty-domain": ["features/loyalty/domain/src/index.ts"], "@cms/machine-clearance": ["features/machine-clearance/shared/src/index.ts"], "@cms/machine-clearance-domain": ["features/machine-clearance/domain/src/index.ts"], "@cms/machine-daily-metrics-domain": ["features/machine-metrics/daily/domain/src/index.ts"], "@cms/machine-daily-metrics-domain-e2e": ["features/machine-metrics/daily/domain-e2e/src/index.ts"], "@cms/machine-funds-transfers": ["features/machine-funds-transfers/shared/src/index.ts"], "@cms/machine-funds-transfers-domain": ["features/machine-funds-transfers/domain/src/index.ts"], "@cms/machine-funds-transfers-domain-e2e": ["features/machine-funds-transfers/domain-e2e/src/index.ts"], "@cms/machine-game-types": ["features/machine-game-types/shared/src/index.ts"], "@cms/machine-game-types-domain": ["features/machine-game-types/domain/src/index.ts"], "@cms/machine-game-types-domain-e2e": ["features/machine-game-types/domain-e2e/src/index.ts"], "@cms/machine-handpays": ["features/machine-handpays/shared/src/index.ts"], "@cms/machine-handpays-domain": ["features/machine-handpays/domain/src/index.ts"], "@cms/machine-hourly-metrics-domain": ["features/machine-metrics/hourly/domain/src/index.ts"], "@cms/machine-hourly-metrics-domain-e2e": ["features/machine-metrics/hourly/domain-e2e/src/index.ts"], "@cms/machine-manufacturers-domain": ["features/machine-manufacturers/domain/src/index.ts"], "@cms/machine-meters-domain": ["features/machine-meters/domain/src/index.ts"], "@cms/machine-metrics": ["features/machine-metrics/shared/src/index.ts"], "@cms/machine-metrics-domain": ["features/machine-metrics/domain/src/index.ts"], "@cms/machine-sessions": ["features/machine-sessions/shared/src/index.ts"], "@cms/machine-sessions-api": ["features/machine-sessions/api/src/index.ts"], "@cms/machine-sessions-domain": ["features/machine-sessions/domain/src/index.ts"], "@cms/machine-sessions-domain-e2e": ["features/machine-sessions/domain-e2e/src/index.ts"], "@cms/med-wss": ["features/med/wss/src/index.ts"], "@cms/message-broker": ["libs/backend/message-broker/src/index.ts"], "@cms/minio-object-storage": ["libs/backend/minio-object-storage/src/index.ts"], "@cms/ng-client-authentication": ["features/authentication/ng-client-authentication/src/index.ts"], "@cms/observed-assets": ["libs/backend/observed-assets/src/index.ts"], "@cms/platform-services-overview": ["features/platform-services-overview/shared/src/index.ts"], "@cms/platform-services-overview-domain": ["features/platform-services-overview/domain/src/index.ts"], "@cms/portal-database": ["libs/backend/infrastructure/portal-database/src/index.ts"], "@cms/portal-poc-server": ["libs/backend/portal-poc-server/src/index.ts"], "@cms/prettier-config": ["libs/prettier/src/index.js"], "@cms/serialization": ["libs/shared/serialization/src/index.ts"], "@cms/server-authentication": ["libs/backend/cms-server-authentication/src/index.ts"], "@cms/server-jwt": ["features/jwt/server-jwt/src/index.ts"], "@cms/server-permissions": ["libs/backend/server-permissions/src/index.ts"], "@cms/stylelint-config": ["libs/stylelint/src/index.js"], "@cms/suspension": ["libs/backend/suspension/src/index.ts"], "@cms/temporary-meter-report": ["libs/backend/temporary-meter-report/src/index.ts"], "@cms/ticket-printers-domain": ["features/ticket-printers/domain/src/index.ts"], "@cms/ticket-printers-shared": ["features/ticket-printers/shared/src/index.ts"], "@cms/ticket-printers-wss": ["features/ticket-printers/wss/src/index.ts"], "@cms/tickets": ["features/tickets/shared/src/index.ts"], "@cms/tickets-domain": ["features/tickets/domain/src/index.ts"], "@cms/tickets-domain-e2e": ["features/tickets/domain-e2e/src/index.ts"], "@cms/transaction-points-domain": ["features/transaction-points/domain/src/index.ts"], "@cms/typeorm": ["features/typeorm/src/index.ts"], "@cms/typeorm-e2e": ["features/typeorm-e2e/src/index.ts"], "@cms/upload-repository-adapter": ["libs/backend/upload-repository-adapter/src/index.ts"], "@cms/uploads": ["features/uploads/shared/src/index.ts"], "@cms/uploads-domain": ["features/uploads/domain/src/index.ts"], "@cms/validation": ["libs/shared/validation/src/index.ts"], "@cms/wallets": ["features/wallets/shared/src/index.ts"], "@cms/wallets-domain": ["features/wallets/domain/src/index.ts"], "@cms/wallets-domain-e2e": ["features/wallets/domain-e2e/src/index.ts"], "@tronius/backend-common": ["libs/backend/common/src/index.ts"], "@tronius/backend-database": ["libs/backend/database/src/index.ts"], "@tronius/frontend-common": ["libs/frontend/common/src/index.ts"], "@tronius/frontend-ui": ["libs/frontend/ui/src/index.ts"], "@tronius/shared-common": ["libs/shared/common/src/index.ts"], "@tronius/shared-domain": ["libs/shared/domain/src/index.ts"], "health-check": ["libs/backend/health-check/src/index.ts"], "infrastructure/database-factory": ["libs/backend/infrastructure/database-factory/src/index.ts"], "nest-shutdown-module": ["libs/backend/nest/shutdown-module/src/index.ts"], "rabbit-mq-message-broker": ["libs/backend/infrastructure/rabbit-mq-message-broker/src/index.ts"], "rabbit-mq-message-broker-e2e": ["libs/backend/infrastructure/rabbit-mq-message-broker-e2e/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}