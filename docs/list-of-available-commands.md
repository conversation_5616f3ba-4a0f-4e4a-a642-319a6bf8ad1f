# List of available commands

This document lists all available commands in the system that are available for use by the various services and applications. Developers
should always specify and list their commands on each pull request. This resource helps to keep track of all available commands and their
respective command results for easy reference by other developers.

## Machine commands

- **UpdateMachineStatusCommand**: Updates the status of a machine
    - [Command](../libs/shared/domain/src/lib/models/machine/commands/update-machine-status.command.ts)
    - No specific result
- **UpdateMachineLockStateStatusCommand**: Updates the lock state status of a machine
    - [Command](../libs/shared/domain/src/lib/models/machine/commands/update-machine-lock-state-status.command.ts)
    - No specific result
- **SetMachinesLastBetCommand**: Sets the last bet information for a machine
    - [Command](../libs/shared/domain/src/lib/models/machine/commands/set-machines-last-bet.command.ts)
    - No specific result
- **SetMachineSerialAndSasVersionCommand**: Sets the serial number and SAS version of a machine
    - [Command](../libs/shared/domain/src/lib/models/machine/commands/set-machine-serial-and-sas-version.command.ts)
    - No specific result
- **SetMachineConnectionStatusCommand**: Sets the connection status of a machine
    - [Command](../libs/shared/domain/src/lib/models/machine/commands/set-machine-connection-status.command.ts)
    - No specific result
- **MarkAllConnectedMachinesAsDisconnectedCommand**: Marks all connected machines as disconnected
    - [Command](../libs/shared/domain/src/lib/models/machine/commands/mark-all-connected-machines-as-disconnected.command.ts)
    - No specific result
- **AssignMachineToMedDeviceCommand**: Assigns a machine to a MED device
    - [Command](../libs/shared/domain/src/lib/models/machine/commands/assign-machine-to-med-device.command.ts)
    - No specific result
- **MachineRebootMedCommand**: Sends a reboot request to a specific machine.
    - [Command](../libs/shared/domain/src/lib/models/machine/commands/machine-reboot-med.command.ts)
    - No specific result
- **MachineRestartMedAppCommand**: Sends a restart MED application request to a specific machine
    - [Command](../libs/shared/domain/src/lib/models/machine/commands/machine-restart-med-app.command.ts)
    - No specific result

## Machine Game Type commands

- **CreateMachineGameTypeCommand**: Creates a new machine game type
    - [Command](../features/machine-game-types/shared/src/lib/commands/create-machine-game-type.command.ts)
    - [Result](../features/machine-game-types/shared/src/lib/commands/create-machine-game-type.command.result.ts)
- **UpdateMachineGameTypeCommand**: Updates an existing machine game type
    - [Command](../features/machine-game-types/shared/src/lib/commands/update-machine-game-type.command.ts)
    - [Result](../features/machine-game-types/shared/src/lib/commands/update-machine-game-type.command.result.ts)

## Event Log commands

- **CreateEventLogsCommandHandler**: Creates one event log
    - [Command](../libs/shared/domain/src/lib/models/event/commands/create-event-log.command.ts)
    - No specific result
- **CreateEventLogsCommandHandler**: Creates multiple event logs
    - [Command](../libs/shared/domain/src/lib/models/event/commands/create-event-log.command.ts)
    - No specific result

## Upload commands

- **GenerateUploadUrlCommand**: Generates a presigned URL for uploading an object
    - [Command](../features/uploads/shared/src/lib/commands/generate-upload-url.command.ts)
    - [Result](../features/uploads/shared/src/lib/commands/generate-upload-url.command.result.ts)
- **MarkUploadAsNotUsedCommand**: Marks an upload as not used
    - [Command](../features/uploads/shared/src/lib/commands/mark-upload-as-not-used.command.ts)
    - No specific result
- **MarkUploadAsInUseCommand**: Marks an upload as in use
    - [Command](../features/uploads/shared/src/lib/commands/mark-upload-as-in-use.command.ts)
    - No specific result

## Cash Operation commands

- **RecordCashOperationCommand**: Records a cash operation

    - [Command](../features/cash-operations/shared/src/lib/commands/record-cash-operation.command.ts)
    - No specific result

## Chip Operation commands

- **RecordChipOperationCommand**: Records a chip operation
    - [Command](../features/chip-operations/shared/src/lib/commands/record-chip-operation.command.ts)
    - No specific result

## Ticket commands

- **CreateTicketCommand**: Creates a ticket
    - [Command](../features/tickets/shared/src/lib/commands/create-ticket.command.ts)
    - [Result](../features/tickets/shared/src/lib/commands/create-ticket.command.result.ts)
- **RedeemTicketCommand**: Redeems a ticket
    - [Command](../features/tickets/shared/src/lib/commands/redeem-ticket.command.ts)
    - [Result](../features/tickets/shared/src/lib/commands/redeem-ticket.command.result.ts)
- **LockTicketCommand**: Locks a ticket
    - [Command](../features/tickets/shared/src/lib/commands/lock-ticket.command.ts)
    - [Result](../features/tickets/shared/src/lib/commands/lock-ticket.command.result.ts)

## Machine Funds Transfer commands

- **CreateMachineFundsTransferCommand**: Creates a machine funds transfer
    - [Command](../features/machine-funds-transfers/shared/src/commands/create-machine-funds-transfer.command.ts)
    - [Result](../features/machine-funds-transfers/shared/src/commands/create-machine-funds-transfer.command.result.ts)
- **ManuallyCompleteMachineFundsTransferCommand**: Manually completes a pending machine funds transfer
    - [Command](../features/machine-funds-transfers/shared/src/commands/manually-complete-machine-funds-transfer.command.ts)
    - [Result](../features/machine-funds-transfers/shared/src/commands/manually-complete-machine-funds-transfer.command.result.ts)

## Printer commands

- **UpdatePrinterStatusCommand**: Updates the status of a printer
    - [Command](../libs/shared/domain/src/lib/models/printer/commands/update-printer-status.command.ts)
    - No specific result
- **SetPrinterConnectionStatusByDeviceIdCommand**: Sets the connection status of a printer by device ID
    - [Command](../libs/shared/domain/src/lib/models/printer/commands/set-printer-connection-status-by-device-id.command.ts)
    - No specific result
- **PrintTicketCommand**: Prints a ticket on a ticket printer
    - [Command](../libs/shared/domain/src/lib/models/printer/commands/print-ticket.command.ts)
    - No specific result
- **MarkAllConnectedPrintersAsDisconnectedCommand**: Marks all connected printers as disconnected
    - [Command](../libs/shared/domain/src/lib/models/printer/commands/mark-all-connected-printers-as-disconnected.command.ts)
    - No specific result
- **GetNextSequenceNumberForPrinterCommand**: Gets the next sequence number for a printer
    - [Command](../libs/shared/domain/src/lib/models/printer/commands/get-next-ticket-sequence-number-for-printer.command.ts)
    - [Result](../libs/shared/domain/src/lib/models/printer/commands/get-next-ticket-sequence-number-for-printer.command.result.ts)

## Device commands

- **GenerateDevicePackageDownloadLinkCommand**: Generates a download link for a device package
    - [Command](../libs/shared/domain/src/lib/models/device/commands/generate-device-package-download-link.command.ts)
    - [Result](../libs/shared/domain/src/lib/models/device/commands/generate-device-package-download-link.command.ts)
- **UpdateDeviceCurrentStateCommand**: Updates the current state of a device
    - [Command](../libs/shared/domain/src/lib/models/device/commands/update-device-current-state.command.ts)
    - No specific result

## Customer commands

- **IssueCustomerCardCommand**: Issues a customer card
    - [Command](../libs/shared/domain/src/lib/models/customer/commands/issue-customer-card.command.ts)
    - No specific result
- **RegisterCustomerCommand**: Registers a new customer
    - [Command](../features/customers/shared/src/lib/commands/register-customer.command.ts)
    - [Results](../features/customers/shared/src/lib/commands/register-customer.command.result.ts)

## Handpay commands

- **PayHandpayCommand**: Processes a handpay
    - [Command](../features/machine-handpays/shared/src/lib/commands/pay-handpay.command.ts)
    - [Result](../features/machine-handpays/shared/src/lib/commands/pay-handpay.command.ts)
- **AuthorizeHandpayCommand**: Authorizes a handpay
    - [Command](../features/machine-handpays/shared/src/lib/commands/authorize-handpay.command.ts)
    - No specific result
- **HandpayResetCommand**: Resets a handpay
    - [Command](../features/machine-handpays/shared/src/lib/commands/handpay-reset.command.ts)
    - No specific result

## Wallet commands

- **ProcessCashWalletOperationCommand**: Processes a cash wallet operation
    - [Command](../features/wallets/shared/src/lib/commands/process-cash-wallet-operation.command.ts)
    - No specific result
- **ProcessFreePlayWalletOperationCommand**: Processes a free play wallet operation
    - [Command](../features/wallets/shared/src/lib/commands/process-free-play-wallet-operation.command.ts)
    - No specific result
- **ReserveCashWalletFundsCommand**: Reserves funds in a cash wallet
    - [Command](../features/wallets/shared/src/lib/commands/reserve-cash-wallet-funds.command.ts)
    - [Result](../features/wallets/shared/src/lib/commands/reserve-wallet-funds-command-result.ts)
- **ReserveFreePlayWalletFundsCommand**: Reserves funds in a free play wallet
    - [Command](../features/wallets/shared/src/lib/commands/reserve-free-play-wallet-funds.command.ts)
    - [Result](../features/wallets/shared/src/lib/commands/reserve-wallet-funds-command-result.ts)
- **ReleaseWalletFundsCommand**: Releases reserved funds in a wallet
    - [Command](../features/wallets/shared/src/lib/commands/release-wallet-funds.command.ts)
    - No specific result

## CMS User

- **CreateCmsUserCommand**: Creates a new CMS user
    - [Command](../libs/backend/cms-users/src/lib/commands/create-cms-user.command.ts)
    - No specific result
- **UpdateCmsUserCommand**: Updates an existing CMS user
    - [Command](../libs/backend/cms-users/src/lib/commands/update-cms-user.command.ts)
    - No specific result
- **ToggleCmsUserEnabledFlagCommand**: Toggles the enabled flag of a CMS user
    - [Command](../libs/backend/cms-users/src/lib/commands/toggle-cms-users-enabled-flag.command.ts)
    - No specific result

## Loyalty commands

- **CreateMachineEarningRuleCommand**: Creates a new machine earning rule
    - [Command](../features/loyalty/shared/src/lib/commands/create-machine-earning-rule.command.ts)
    - [Result](../features/loyalty/shared/src/lib/commands/create-machine-earning-rule.command.result.ts)
- **UpdateMachineEarningRuleCommand**: Updates an existing machine earning rule
    - [Command](../features/loyalty/shared/src/lib/commands/update-machine-earning-rule.command.ts)
    - [Result](../features/loyalty/shared/src/lib/commands/update-machine-earning-rule.command.result.ts)
