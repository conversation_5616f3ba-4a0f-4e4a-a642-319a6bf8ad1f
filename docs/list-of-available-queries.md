# List of available queries

This document lists all available queries in the system that are available for use by the various services and applications. Developers
should always specify and list their queries on each pull request. This resource helps to keep track of all available queries and their
respective query results for easy reference by other developers.

## Casino Property queries

- _Get Gaming Day query_: Returns the gaming day string for a given date.
    - [Query](../libs/shared/domain/src/lib/models/casino/queries/get-gaming-day.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/casino/queries/get-gaming-day.query.result.ts)
- _Get Casino Settings query_: Returns the casino settings
    - [Query](../libs/shared/domain/src/lib/models/casino/queries/get-casino-settings.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/casino/queries/get-casino-settings.query.result.ts)
- _Get Gaming Day Data query_: Returns the gaming day with extended information for a given date.
    - [Query](../libs/shared/domain/src/lib/models/casino/queries/get-gaming-day-data.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/casino/queries/get-gaming-day-data.query.result.ts)
- _Get Start Of Day For Gaming Day query_: Returns the start of day Date object for a given gaming day.
    - [Query](../features/casino-operator/shared/src/lib/queries/get-start-of-day-for-gaming-day.query.ts)
    - [Result](../features/casino-operator/shared/src/lib/queries/get-start-of-day-for-gaming-day.query.result.ts)

## Machine Metrics queries

- **GetWinReconciliationDailyReportQuery** : Returns win reconciliation report items for a specific date range (daily)
    - [Query](../features/machine-metrics/shared/src/lib/queries/get-win-reconciliation-daily-report.query.ts)
    - [Result](../features/machine-metrics/shared/src/lib/queries/get-win-reconciliation-daily-report.query.result.ts)
- **GetWinReconciliationHourlyReportQuery** : Returns win reconciliation report items for a specific time range (hourly)
    - [Query](../features/machine-metrics/shared/src/lib/queries/get-win-reconciliation-hourly-report.query.ts)
    - [Result](../features/machine-metrics/shared/src/lib/queries/get-win-reconciliation-hourly-report.query.result.ts)

## Machine queries

- **GetAllMachineIdsQuery** : Returns all machine ids
    - [Query](../libs/shared/domain/src/lib/models/machine/queries/get-all-machine-ids.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/machine/queries/get-all-machine-ids.query.result.ts)
- **GetAllMachineProfilesQuery** : Returns all machine profiles
    - [Query](../libs/shared/domain/src/lib/models/machine/queries/get-all-machine-profiles.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/machine/queries/get-all-machine-profiles.query.result.ts)
- **GetConnectionStatusOfAllMachinesQuery** : Returns the connection status of all machines
    - [Query](../libs/shared/domain/src/lib/models/machine/queries/get-connection-status-of-all-machines.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/machine/queries/get-connection-status-of-all-machines.query.result.ts)
- **GetMachineByAssetNumberQuery** : Returns the machine by asset number -
    - [Query](../libs/shared/domain/src/lib/models/machine/queries/get-machine-by-asset-number.query.ts) -
    - [Result](../libs/shared/domain/src/lib/models/machine/queries/get-machine-by-asset-number.query.result.ts)
- **GetMachineCurrentByIdQuery**: Returns the current machine by id
    - [Query](../libs/shared/domain/src/lib/models/machine/queries/get-machine-current-by-id.query.ts)
    - [Result](./libs/shared/domain/src/lib/models/machine/queries/get-machine-current-by-id.query.ts)
- **GetMachineCurrentByDeviceIdQuery**: Returns the current machine by device id
    - [Query](../libs/shared/domain/src/lib/models/machine/queries/get-machine-current-by-device-id.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/machine/queries/get-machine-current-by-device-id.query.ts)
- **GetMachineProfileListItemsByIdsQuery**: Returns a list of machine profile list items by their IDs
    - [Query](../libs/shared/domain/src/lib/models/machine/queries/get-machine-profile-list-items-by-ids.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/machine/queries/get-machine-profile-list-items-by-ids.query.result.ts)

## Machine Game Type queries

- **GetMachineGameTypeByIdQuery**: Returns a machine game type by its ID
    - [Query](../features/machine-game-types/shared/src/lib/queries/get-machine-game-type-by-id.query.ts)
    - [Result](../features/machine-game-types/shared/src/lib/queries/get-machine-game-type-by-id.query.result.ts)
- **GetMachineGameTypeByNameQuery**: Returns a machine game type by its name
    - [Query](../features/machine-game-types/shared/src/lib/queries/get-machine-game-type-by-name.query.ts)
    - [Result](../features/machine-game-types/shared/src/lib/queries/get-machine-game-type-by-name.query.result.ts)
- **GetAllMachineGameTypesQuery**: Returns all machine game types
    - [Query](../features/machine-game-types/shared/src/lib/queries/get-all-machine-game-types.query.ts)
    - [Result](../features/machine-game-types/shared/src/lib/queries/get-all-machine-game-types.query.result.ts)

## Machine Session queries

- **GetActiveMachineSessionQuery**: Returns the active session for a given machine ID
    - [Query](../features/machine-sessions/shared/src/queries/get-active-machine-session.query.ts)
    - [Result](../features/machine-sessions/shared/src/queries/get-active-machine-session.query.result.ts)
- **GetMachineSessionQuery**: Returns a specific machine session by ID
    - [Query](../features/machine-sessions/shared/src/queries/get-machine-session.query.ts)
    - [Result](../features/machine-sessions/shared/src/queries/get-machine-session.query.result.ts)
- **ListMachineSessionsQuery**: Returns a paginated list of machine sessions
    - [Query](../features/machine-sessions/shared/src/queries/list-machine-sessions.query.ts)
    - [Result](../features/machine-sessions/shared/src/queries/list-machine-sessions.query.result.ts)

## Finance queries

- **GetDenominationByTypeAndValueQuery**: Returns a denomination by its asset type and value
    - [Query](../libs/shared/domain/src/lib/models/finance/queries/get-denomination-by-type-and-value.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/finance/queries/get-denomination-by-type-and-value.query.result.ts)

## Upload queries

- **GetUploadUrlQuery**: Returns a presigned URL for accessing a uploaded object
    - [Query](../features/uploads/shared/src/lib/queries/get-upload-url.query.ts)
    - [Result](../features/uploads/shared/src/lib/queries/get-upload-url.query.result.ts)
- **GetUploadByObjectKeyQuery**: Returns the upload details by object key
    - [Query](../features/uploads/shared/src/lib/queries/get-upload-by-object-key.query.ts)
    - [Result](../features/uploads/shared/src/lib/queries/get-upload-by-object-key.query.result.ts)
- **GetUploadByIdQuery**: Returns the upload details by upload ID
    - [Query](../features/uploads/shared/src/lib/queries/get-upload-by-id.query.ts)
    - [Result](../features/uploads/shared/src/lib/queries/get-upload-by-id.query.result.ts)
- **GetUploadUrlsQuery**: Returns presigned URLs for multiple uploads by their IDs or object keys
    - [Query](../features/uploads/shared/src/lib/queries/get-upload-urls.query.ts)
    - [Result](../features/uploads/shared/src/lib/queries/get-upload-urls.query.result.ts)

## Cash Operation queries

- **CanRecordCashOperationQuery**: Checks if a cash operation can be recorded

    - [Query](../features/cash-operations/shared/src/lib/queries/can-record-cash-operation.query.ts)
    - [Result](../features/cash-operations/shared/src/lib/queries/can-record-cash-operation.query.result.ts)

- **GetCashOperationsMetricsForPeriodQuery**: Returns metrics for cash operations within a specified time period

    - [Query](../features/cash-operations/shared/src/lib/queries/get-cash-operations-metrics-for-period.query.ts)
    - [Result](../features/cash-operations/shared/src/lib/queries/get-cash-operations-metrics-for-period.query.result.ts)

- **GetCashOperationsMetricsForGamingDayQuery**: Returns the accepted amount for cash operations for a specific gaming day and machine
    - [Query](../features/cash-operations/shared/src/lib/queries/get-cash-operations-metrics-for-gaming-day.query.ts)
    - [Result](../features/cash-operations/shared/src/lib/queries/get-cash-operations-metrics-for-gaming-day.query.result.ts)

## Chips Operation queries

- **CanRecordChipOperationQuery**: Checks if a chip operation can be recorded
    - [Query](../features/chip-operations/shared/src/lib/queries/can-record-chip-operation.query.ts)
    - [Result](../features/chip-operations/shared/src/lib/queries/can-record-chip-operation.query.result.ts)

## Transaction Point queries

- **GetTransactionPointByIdQuery**: Returns the transaction point by ID or throws an error if not found
    - [Query](../libs/shared/domain/src/lib/models/transaction-point/queries/get-transaction-point-by-id.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/transaction-point/queries/get-transaction-point-by-id.query.result.ts)
- **GetOpenTransactionPointByIdQuery**: Returns the open transaction point by ID or throws an error if not found (or if it is not open)
    - [Query](../libs/shared/domain/src/lib/models/transaction-point/queries/get-open-transaction-point-by-id.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/transaction-point/queries/get-open-transaction-point-by-id.query.result.ts)

## Printer queries

- **GetPrinterByIdQuery**: Returns the printer by ID or throws an error if not found
    - [Query](../libs/shared/domain/src/lib/models/printer/queries/get-printer-by-id.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/printer/queries/get-printer-by-id.query.ts)
- **GetPrinterByDeviceIdQuery**: Returns the printer by device ID or throws an error if not found
    - [Query](../libs/shared/domain/src/lib/models/printer/queries/get-printer-by-device-id.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/printer/queries/get-printer-by-device-id.query.ts)
- **GetOperationalPrinterByIdQuery**: Returns the operational printer by ID or throws an error if not found (or if it is not operational)
    - [Query](../libs/shared/domain/src/lib/models/printer/queries/get-operational-printer-by-id.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/printer/queries/get-operational-printer-by-id.query.ts)
- **CanScheduleTicketPrintQuery**: Checks if a ticket print can be scheduled on a particular printer
    - [Query](../libs/shared/domain/src/lib/models/printer/queries/can-schedule-ticket-print.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/printer/queries/can-schedule-ticket-print.query.ts)

## Ticket queries

- **CanCreateTicketQuery**: Checks if a ticket can be created or throws an error if not
    - [Query](../libs/shared/domain/src/lib/models/ticket/queries/can-create-ticket.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/ticket/queries/can-create-ticket.query.result.ts)
- **CanRedeemTicketQuery**: Checks if a ticket can be redeemed or throws an error if not
    - [Query](../libs/shared/domain/src/lib/models/ticket/queries/can-redeem-ticket.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/ticket/queries/can-redeem-ticket.query.result.ts)
- **GetTicketsMetricsForPeriodQuery**: Returns metrics for tickets within a specified time period
    - [Query](../features/tickets/shared/src/lib/queries/get-tickets-metrics-for-period.query.ts)
    - [Result](../features/tickets/shared/src/lib/queries/get-tickets-metrics-for-period.query.result.ts)
- **GetTicketsMetricsForGamingDayQuery**: Returns metrics for tickets for a specified gaming day
    - [Query](../features/tickets/shared/src/lib/queries/get-tickets-metrics-for-gaming-day.query.ts)
    - [Result](../features/tickets/shared/src/lib/queries/get-tickets-metrics-for-gaming-day.query.result.ts)

## Customer queries

- **CustomerHotSearchQuery**: Searches for customers based on a query string
    - [Query](../features/customers/shared/src/lib/queries/customer-hot-search.query.ts)
    - [Result](../features/customers/shared/src/lib/queries/customer-hot-search.query.ts)
- **CustomerParamsSearchQuery**: Searches for customers based on parameters
    - [Query](../features/customers/shared/src/lib/queries/customer-params-search.query.ts)
    - [Result](../features/customers/shared/src/lib/queries/customer-params-search.query.ts)
- **GetCustomerByCardDataQuery**: Returns the customer profile and associated card by card data
    - [Query](../features/customers/shared/src/lib/queries/get-customer-by-card-data.query.ts)
    - [Result](../features/customers/shared/src/lib/queries/get-customer-by-card-data.query.result.ts)
- **GetCustomerByIdQuery**: Returns the customer profile by ID
    - [Query](../features/customers/shared/src/lib/queries/get-customer-by-id.query.ts)
    - [Result](../features/customers/shared/src/lib/queries/get-customer-by-id.query.ts)
- **GetCustomerListItemsByIdsQuery**: Returns a list of customers list items by their IDs
    - [Query](../features/customers/shared/src/lib/queries/get-customer-list-items-by-ids.query.ts)
    - [Result](../features/customers/shared/src/lib/queries/get-customer-list-items-by-ids.query.result.ts)
- **GetCardByCustomerIdQuery**: Returns card by customer ID
    - [Query](../features/customers/shared/src/lib/queries/get-card-by-customer-id.query.ts)
    - [Result](../features/customers/shared/src/lib/queries/get-card-by-customer-id.query.result.ts)

## Handpay queries

- **GetPayableHandpaysQuery**: Returns a list of payable handpays
    - [Query](../libs/shared/domain/src/lib/models/handpay/queries/get-payable-handpays.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/handpay/queries/get-payable-handpays.query.ts)
- **GetPayableHandpayCountQuery**: Returns the count of payable handpays
    - [Query](../libs/shared/domain/src/lib/models/handpay/queries/get-payable-handpay-count.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/handpay/queries/get-payable-handpay-count.query.ts)
- **CanPayHandpayQuery**: Checks if a handpay can be paid
    - [Query](../libs/shared/domain/src/lib/models/handpay/queries/can-pay-handpay.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/handpay/queries/can-pay-handpay.query.ts)
- **GetMachineHandpayMetricsForPeriodQuery**: Returns machine handpays metrics for a given period (maximum 24hr)
    - [Query](../features/machine-handpays/shared/src/lib/queries/get-machine-handpay-metrics-for-period.query.ts)
    - [Result](../features/machine-handpays/shared/src/lib/queries/get-machine-handpay-metrics-for-period.query.result.ts)
- **GetMachineHandpayMetricsForGamingDayQuery**: Returns machine handpays metrics for a given gaming day
    - [Query](../features/machine-handpays/shared/src/lib/queries/get-machine-handpay-metrics-for-gaming-day.query.ts)
    - [Result](../features/machine-handpays/shared/src/lib/queries/get-machine-handpay-metrics-for-gaming-day.query.result.ts)

## Meter queries

- **GetCurrentMachineMetersByMachineIdsQuery**: Returns current meter readings for specified machines and meter codes
    - [Query](../libs/shared/domain/src/lib/models/meter/queries/get-current-machine-meters-by-machine-ids.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/meter/queries/get-current-machine-meters-by-machine-ids.query.result.ts)
- **GetMachineMeterSnapshotsForDailyOrHourlyContextQuery**: Returns meter snapshots for a machine in daily or hourly context
    - [Query](../libs/shared/domain/src/lib/models/meter/queries/get-machine-meter-snapshots-for-daily-or-hourly-context.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/meter/queries/get-machine-meter-snapshots-for-daily-or-hourly-context.query.result.ts)
- **GetMachineMeterIntervalsForClearanceQuery**: Returns meter intervals for machine clearance
    - [Query](../libs/shared/domain/src/lib/models/meter/queries/get-machine-meter-intervals-for-clearance.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/meter/queries/get-machine-meter-intervals-for-clearance.query.result.ts)

## Wallet queries

- **CanProcessCashWalletOperationQuery**: Checks if a cash wallet operation can be processed
    - [Query](../features/wallets/shared/src/lib/queries/can-process-cash-wallet-operation.query.ts)
    - [Result](../features/wallets/shared/src/lib/queries/can-process-cash-wallet-operation.query.result.ts)
- **CanProcessFreePlayWalletOperationQuery**: Checks if a free play wallet operation can be processed
    - [Query](../features/wallets/shared/src/lib/queries/can-process-free-play-wallet-operation.query.ts)
    - [Result](../features/wallets/shared/src/lib/queries/can-process-free-play-wallet-operation.query.result.ts)
- **GetCustomersCashWalletQuery**: Returns the customer's cash wallet for a particular currency
    - [Query](../features/wallets/shared/src/lib/queries/get-customers-cash-wallet.query.ts)
    - [Result](../features/wallets/shared/src/lib/queries/get-customers-cash-wallet.query.result.ts)
- **GetCustomersFreePlayWalletQuery**: Returns the customer's free play wallet for a particular currency
    - [Query](../features/wallets/shared/src/lib/queries/get-customers-free-play-wallet.query.ts)
    - [Result](../features/wallets/shared/src/lib/queries/get-customers-free-play-wallet.query.result.ts)

## Device queries

- **GetDeviceByDeviceNameQuery**: Returns a device by its device name or throws an error if not found
    - [Query](../libs/shared/domain/src/lib/models/device/queries/get-device-by-device-name.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/device/queries/get-device-by-device-name.query.ts)

## Platform Services Overview queries

- **GetPlatformServicesQuery**: Returns all platform services with their modules and version information
    - [Query](../features/platform-services-overview/shared/src/lib/queries/get-platform-services.query.ts)
    - [Result](../features/platform-services-overview/shared/src/lib/queries/get-platform-services.query.result.ts)

## Customer Statement queries

- **GetCustomerStatementGamingDaysQuery**: Retrieves customer statement gaming days for a specified customer within a date range.

    - [Query](../libs/shared/domain/src/lib/models/customer-statement/queries/get-customer-statement-gaming-days.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/customer-statement/queries/get-customer-statement-gaming-days.query.result.ts)

- **GetCustomerStatementItemsByGamingDayQuery**: Retrieves detailed customer statement items for a specified customer and gaming day.
    - [Query](../libs/shared/domain/src/lib/models/customer-statement/queries/get-customer-statement-items-by-gaming-day.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/customer-statement/queries/get-customer-statement-items-by-gaming-day.query.result.ts)

## Manufacturer queries

- **GetAllMachineManufacturersQuery**: Return all machine manufacturers

    - [Query](../libs/shared/domain/src/lib/models/manufacturer/queries/get-all-machine-manufacturers.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/manufacturer/queries/get-all-machine-manufacturers.query.result.ts)

- **GetManufacturerByIdQuery**: Returns the manufacturer by ID or throws an error if not found
    - [Query](../libs/shared/domain/src/lib/models/manufacturer/queries/get-manufacturer-by-id.query.ts)
    - [Result](../libs/shared/domain/src/lib/models/manufacturer/queries/get-manufacturer-by-id.query.result.ts)

## Loyalty queries

- **GetAllMachineEarningRulesQuery**: Returns all machine earning rules
    - [Query](../features/loyalty/shared/src/lib/queries/get-all-machine-earning-rules.query.ts)
    - [Result](../features/loyalty/shared/src/lib/queries/get-all-machine-earning-rules.query.result.ts)
- **GetAllEnabledMachineEarningRulesQuery**: Returns all enabled machine earning rules
    - [Query](../features/loyalty/shared/src/lib/queries/get-all-enabled-machine-earning-rules.query.ts)
    - [Result](../features/loyalty/shared/src/lib/queries/get-all-enabled-machine-earning-rules.query.result.ts)
- **GetMachineEarningRuleByIdQuery**: Returns a specific machine earning rule by ID or throws an error if not found
    - [Query](../features/loyalty/shared/src/lib/queries/get-machine-earning-rule-by-id.query.ts)
    - [Result](../features/loyalty/shared/src/lib/queries/get-machine-earning-rule-by-id.query.result.ts)

## Entity Audit queries

- **ListEntityAuditQuery**: Lists entity audit entries
    - [Query](../features/entity-audit/shared/src/lib/queries/list-entity-audit.query.ts)
    - [Result](../features/entity-audit/shared/src/lib/queries/list-entity-audit.query.result.ts)
