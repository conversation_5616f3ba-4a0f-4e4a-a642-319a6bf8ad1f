import { ExecutionContext } from '@cms/execution-context';
import { deserialize } from '@cms/serialization';
import { Injectable, Logger, Optional } from '@nestjs/common';
import { Job, waitFor } from '@tronius/shared-common';

import { jobClassConstructorNameProvider } from './constants';
import { QueueConfig } from './queue.config';
import { QueueProviderPort } from './queue.provider.port';
import { QueueService } from './queue.service';
import { JobClassConstructor } from './types';

@Injectable()
export class QueueManager {
    protected runningJobProcessors = 0;

    private readonly logger = new Logger(QueueManager.name);

    constructor(
        private readonly queueProviderPort: QueueProviderPort,
        @Optional()
        private readonly executionContext?: ExecutionContext
    ) {}

    async registerQueue(queueName: string, queueConfig: QueueConfig): Promise<void> {
        this.logger.debug(`Registering [queue]:"${queueName}"`);
        await this.queueProviderPort.registerQueue(queueName, queueConfig);
    }

    async registerWorker(
        jobClassConstructor: JobClassConstructor<Job>,
        jobProcessor: (job: Job) => Promise<void> | void,
        queueName?: string
    ): Promise<void> {
        const jobClassName = jobClassConstructorNameProvider(jobClassConstructor);

        const wrappedJobProcessor = async (serializedJobData: unknown): Promise<void> => {
            try {
                this.runningJobProcessors++;

                const deserializedJobData = deserialize(jobClassConstructor, serializedJobData);

                if (this.executionContext) {
                    const { executionContext = {}, jobId } = deserializedJobData;
                    const jobExecutionContext = {
                        ...executionContext,
                        job: queueName || jobClassName,
                        jobId
                    };

                    await this.executionContext.run(jobExecutionContext, async () => {
                        const jobProcessingStart = Date.now();
                        this.logger.debug(`Processing [job]:"${jobClassName}", [jobId]:"${(serializedJobData as Job).jobId}"`);

                        await jobProcessor(deserializedJobData);

                        const jobProcessingDuration = Date.now() - jobProcessingStart;
                        this.executionContext?.set('duration', jobProcessingDuration);

                        this.logger.debug(
                            `Processed [job]:"${jobClassName}", [jobId]:"${(serializedJobData as Job).jobId}" in ${jobProcessingDuration}ms`
                        );
                        this.executionContext?.clear('duration');
                    });
                    return;
                }

                await jobProcessor(deserializedJobData);
            } catch (error) {
                this.logger.error(`Failed to process [job]:"${jobClassName}", [jobId]:"${(serializedJobData as Job).jobId}"`, error);
                throw error;
            } finally {
                this.runningJobProcessors--;
            }
        };

        const resultingQueueName = queueName || jobClassName;

        this.logger.debug(`Registering [worker]:"${jobClassName}" for [queue]:"${resultingQueueName}"`);

        await this.queueProviderPort.registerWorker(resultingQueueName, wrappedJobProcessor);
    }

    provideQueueService<T extends Job>(jobClassConstructor: JobClassConstructor<T>, queueName?: string): QueueService<T> {
        const jobClassName = jobClassConstructorNameProvider(jobClassConstructor);
        const resultingQueueName = queueName || jobClassName;

        return new QueueService(jobClassConstructor, this.queueProviderPort, this.executionContext, resultingQueueName);
    }

    protected async beforeApplicationShutdown(_signal?: string): Promise<void> {
        if (!this.runningJobProcessors) {
            return;
        }
        const gracePeriodIntervalForRunningJobProcessors = 100;

        while (this.runningJobProcessors > 0) {
            this.logger.debug(`Waiting for ${this.runningJobProcessors} job processors to finish`);
            await waitFor(gracePeriodIntervalForRunningJobProcessors);
        }
    }
}
