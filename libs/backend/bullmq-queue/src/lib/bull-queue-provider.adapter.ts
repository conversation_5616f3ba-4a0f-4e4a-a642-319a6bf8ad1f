import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { JobOptions, JobState, QueueConfig, QueueProviderPort } from '@cms/job-queue';
import { Inject, Injectable, OnModuleDestroy, Optional } from '@nestjs/common';
import { DateTimeUtils, InternalServerError } from '@tronius/shared-common';
import { ConnectionOptions as BullConnectionOptions, Queue as BullQueue, Worker as BullWorker, Job, Processor } from 'bullmq';

import { BullBoardConfig } from './bull-board.config';
import { BullJobStateMapper } from './bull-job-state.mapper';
import { BullRedisConnectionConfig } from './bull-redis-connection.config';
import { bullBoardInstanceInjectionToken } from './constants';
import { bullQueuesMapInjectionToken } from './constants.exported';

@Injectable()
export class BullQueueProviderAdapter extends QueueProviderPort implements OnModuleDestroy {
    @Inject(bullQueuesMapInjectionToken) private readonly queuesMap!: Map<string, BullQueue<Job<unknown>>>;

    private readonly queueConfigMap: Map<string, QueueConfig> = new Map();

    private readonly workers: BullWorker[] = [];

    constructor(
        private readonly bullRedisConnectionOption: BullRedisConnectionConfig,
        private readonly bullBoardConfig: BullBoardConfig,
        @Optional() @Inject(bullBoardInstanceInjectionToken) private readonly bullBoardInstance?: ReturnType<typeof createBullBoard>
    ) {
        super();
    }

    private get connection(): BullConnectionOptions {
        const { host, password, port, tls } = this.bullRedisConnectionOption;
        return {
            host,
            password,
            port,
            enableOfflineQueue: false,
            tls: tls ? {} : undefined
        };
    }

    async onModuleDestroy(): Promise<void> {
        const closeWorkerPromises = this.workers.map(async (worker) => {
            await worker.close();
        });

        await Promise.all(closeWorkerPromises);

        const { destroyQueuesOnShutdown } = this.bullRedisConnectionOption;

        if (!destroyQueuesOnShutdown) {
            const closeQueuesPromises = Array.from(this.queuesMap.values()).map(async (queue) => {
                const queueIsAlreadyClosing = queue.closing;
                if (!queueIsAlreadyClosing) {
                    await queue.close();
                }
            });

            await Promise.all(closeQueuesPromises);
            return;
        }

        const registeredQueues = Array.from(this.queuesMap.keys());
        const deleteQueuePromises = registeredQueues.map(async (queueName) => {
            const queue = this.provideBullQueueOrThrow(queueName);
            const queueIsAlreadyClosing = queue.closing;

            if (!queueIsAlreadyClosing) {
                await queue.obliterate({ force: true });
            }
        });

        await Promise.all(deleteQueuePromises);
    }

    override async pauseQueue(queueName: string): Promise<void> {
        const prefixedQueueName = this.provideQueueName(queueName);

        const queue = this.provideBullQueueOrThrow(prefixedQueueName);
        const queueIsPaused = await queue.isPaused();
        if (queueIsPaused) {
            return;
        }
        await queue.pause();
    }

    override async resumeQueue(queueName: string): Promise<void> {
        const prefixedQueueName = this.provideQueueName(queueName);

        const queue = this.provideBullQueueOrThrow(prefixedQueueName);
        const queueIsPaused = await queue.isPaused();
        if (!queueIsPaused) {
            return;
        }
        await queue.resume();
    }

    override async addJob(queueName: string, jobId: string, serializedJobPayload: unknown, jobOptions?: JobOptions): Promise<void> {
        const prefixedQueueName = this.provideQueueName(queueName);

        const queue = this.provideBullQueueOrThrow(prefixedQueueName);

        await queue.add(prefixedQueueName, serializedJobPayload, {
            jobId,
            delay: jobOptions?.jobDelayInMs
        });
    }

    override async getJobById(queueName: string, jobId: string): Promise<unknown> {
        const prefixedQueueName = this.provideQueueName(queueName);

        const queue = this.provideBullQueueOrThrow(prefixedQueueName);
        const bullJob = await queue.getJob(jobId);
        if (!bullJob) {
            return null;
        }

        return bullJob.data;
    }

    override async removeJob(queueName: string, jobId: string): Promise<void> {
        const prefixedQueueName = this.provideQueueName(queueName);

        const queue = this.provideBullQueueOrThrow(prefixedQueueName);
        await queue.remove(jobId);
    }

    override async getJobStateById(queueName: string, jobId: string): Promise<JobState | null> {
        const prefixedQueueName = this.provideQueueName(queueName);

        const queue = this.provideBullQueueOrThrow(prefixedQueueName);
        const bullJob = await queue.getJob(jobId);
        if (!bullJob) {
            return null;
        }

        const bullState = await bullJob.getState();

        return BullJobStateMapper.mapBullJobState(bullState);
    }

    override async registerQueue(queueName: string, queueOptions: QueueConfig): Promise<void> {
        const { maxRetries, removeCompletedJobInHours, removeFailedJobInHours, retryDelayInMs, maxWorkers } = queueOptions;

        const removeCompletedJobInSeconds = DateTimeUtils.hoursToSeconds(removeCompletedJobInHours);
        const removeFailedJobInSeconds = DateTimeUtils.hoursToSeconds(removeFailedJobInHours);

        const prefixedQueueName = this.provideQueueName(queueName);

        const bullQueue = new BullQueue<Job<unknown>>(prefixedQueueName, {
            connection: this.connection,
            defaultJobOptions: {
                attempts: maxRetries,
                removeOnComplete: {
                    age: removeCompletedJobInSeconds
                },
                removeOnFail: {
                    age: removeFailedJobInSeconds
                },
                // eslint-disable-next-line spellcheck/spell-checker
                backoff: {
                    type: 'exponential',
                    delay: retryDelayInMs
                }
            }
        });

        await bullQueue.setGlobalConcurrency(maxWorkers);

        this.queuesMap.set(prefixedQueueName, bullQueue);

        this.queueConfigMap.set(prefixedQueueName, queueOptions);

        if (this.bullBoardConfig.bullBoardConfigured && this.bullBoardInstance) {
            this.bullBoardInstance.addQueue(new BullMQAdapter(bullQueue));
        }

        return Promise.resolve();
    }

    override async registerWorker(queueName: string, jobProcessor: (serializedJobData: unknown) => Promise<void>): Promise<void> {
        const prefixedQueueName = this.provideQueueName(queueName);

        const bullJobProcessor: Processor<unknown, unknown, string> = async ({ data: jobData }) => {
            await jobProcessor(jobData);
        };

        const queueConfig = this.queueConfigMap.get(prefixedQueueName);
        if (!queueConfig) {
            throw new InternalServerError({ message: `Queue config not found for [queue]:"${queueName}"` });
        }

        const { maxWorkers } = queueConfig;

        const bullWorker = new BullWorker(prefixedQueueName, bullJobProcessor, {
            connection: this.connection,
            concurrency: maxWorkers
        });

        this.workers.push(bullWorker);

        return Promise.resolve();
    }

    private provideBullQueueOrThrow(queueName: string): BullQueue<Job<unknown>> {
        const queue = this.queuesMap.get(queueName);
        if (!queue) {
            throw new InternalServerError({ message: `Bull Queue not found for [queue]:"${queueName}"` });
        }

        return queue;
    }

    private provideQueueName(queueName: string): string {
        if (!this.bullRedisConnectionOption.queuePrefix) {
            return queueName;
        }

        return `${this.bullRedisConnectionOption.queuePrefix}-${queueName}`;
    }
}
