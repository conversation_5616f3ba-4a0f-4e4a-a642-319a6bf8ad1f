import { TransformBoolean, TransformNumber, deserialize } from '@cms/serialization';
import { IsNotEmptyString, IsPortNumber } from '@cms/validation';
import { IsBoolean, IsOptional, IsString, IsUrl } from 'class-validator';

interface BullRedisConnectionConfigProps {
    host: string;
    port: number;
    password?: string;
    tls?: boolean;
    queuePrefix?: string;
    destroyQueuesOnShutdown?: boolean;
}

export class BullRedisConnectionConfig implements BullRedisConnectionConfigProps {
    // eslint-disable-next-line @typescript-eslint/naming-convention
    @IsUrl({ require_tld: false, require_host: true })
    readonly host!: string;

    @IsPortNumber()
    @TransformNumber()
    readonly port!: number;

    @IsNotEmptyString()
    @IsOptional()
    readonly password?: string;

    @IsBoolean()
    @TransformBoolean()
    readonly tls: boolean = false;

    @IsOptional()
    @IsString()
    readonly queuePrefix?: string;

    @IsBoolean()
    @TransformBoolean()
    readonly destroyQueuesOnShutdown: boolean = false;

    protected constructor() {
        //
    }

    static create(props: BullRedisConnectionConfigProps): BullRedisConnectionConfig {
        return deserialize(BullRedisConnectionConfig, props);
    }
}
