import { createBullBoard } from '@bull-board/api';
import { ExpressAdapter } from '@bull-board/express';
import { DynamicModule, Inject, MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ApplicationConfig } from '@nestjs/core';
import { UUIDFactory } from '@tronius/shared-common';
import basicAuth from 'express-basic-auth';

import { BullBoardConfig } from './bull-board.config';
import { BullQueueProviderAdapter } from './bull-queue-provider.adapter';
import { BullRedisConnectionConfig } from './bull-redis-connection.config';
import { bullBoardInstanceInjectionToken, bullBoardServerInjectionToken } from './constants';
import { bullQueuesMapInjectionToken } from './constants.exported';

@Module({})
export class BullmqQueueModule implements NestModule {
    constructor(
        private readonly applicationConfig: ApplicationConfig,
        private readonly bullBoardConfig: BullBoardConfig,
        @Inject(bullBoardServerInjectionToken) private readonly expressAdapter: ExpressAdapter
    ) {}

    configure(consumer: MiddlewareConsumer): void {
        const { bullBoardConfigured, apiRoute, username, userPassword } = this.bullBoardConfig;
        if (!bullBoardConfigured) {
            return;
        }

        const addForwardSlash = (path: string): string => {
            return path.startsWith('/') || path === '' ? path : `/${path}`;
        };
        const prefix = addForwardSlash(this.applicationConfig.getGlobalPrefix() + apiRoute);
        this.expressAdapter.setBasePath(prefix);

        consumer
            .apply(
                basicAuth({
                    users: { [username]: userPassword },
                    challenge: true
                })
            )
            .forRoutes(`${apiRoute}`);

        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
        consumer.apply(this.expressAdapter.getRouter()).forRoutes(apiRoute);
    }

    static forE2E(queuePrefix = UUIDFactory.create(), destroyQueuesOnShutdown = true): DynamicModule {
        return this.register(queuePrefix, destroyQueuesOnShutdown);
    }

    static register(queuePrefix = '', destroyQueuesOnShutdown = false): DynamicModule {
        return {
            module: BullmqQueueModule,
            imports: [ConfigModule.forRoot()],
            providers: [
                {
                    provide: BullRedisConnectionConfig,
                    inject: [ConfigService],
                    useFactory: (configService: ConfigService) => {
                        return BullRedisConnectionConfig.create({
                            /* eslint-disable @typescript-eslint/no-non-null-assertion */
                            host: configService.get<string>('REDIS_HOST')!,
                            port: configService.get<number>('REDIS_PORT')!,
                            password: configService.get<string>('REDIS_PASSWORD'),
                            tls: configService.get<boolean>('REDIS_TLS'),
                            queuePrefix,
                            destroyQueuesOnShutdown
                            /* eslint-enable @typescript-eslint/no-non-null-assertion */
                        });
                    }
                },
                {
                    provide: bullQueuesMapInjectionToken,
                    useValue: new Map()
                },
                {
                    provide: BullBoardConfig,
                    inject: [ConfigService],
                    useFactory: (configService: ConfigService): BullBoardConfig => {
                        return BullBoardConfig.create({
                            /* eslint-disable @typescript-eslint/no-non-null-assertion */
                            apiRoute: configService.get<string>('BULL_BOARD_API_ROUTE')!,
                            username: configService.get<string>('BULL_BOARD_USER')!,
                            userPassword: configService.get<string>('BULL_BOARD_PASSWORD')!
                            /* eslint-enable @typescript-eslint/no-non-null-assertion */
                        });
                    }
                },
                BullQueueProviderAdapter,
                {
                    provide: bullBoardServerInjectionToken,
                    inject: [BullBoardConfig],
                    useFactory: ({ bullBoardConfigured }: BullBoardConfig): ExpressAdapter | null => {
                        if (!bullBoardConfigured) {
                            return null;
                        }

                        return new ExpressAdapter();
                    }
                },
                {
                    inject: [bullBoardServerInjectionToken],
                    provide: bullBoardInstanceInjectionToken,
                    useFactory: (serverAdapter?: ExpressAdapter): unknown => {
                        if (!serverAdapter) {
                            return null;
                        }

                        return createBullBoard({
                            queues: [],
                            serverAdapter
                        });
                    }
                }
            ],
            exports: [BullQueueProviderAdapter]
        };
    }
}
