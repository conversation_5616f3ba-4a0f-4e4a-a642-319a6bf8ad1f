import { BeforeApplicationShutdown, Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { DiscoveryService, Reflector } from '@nestjs/core';
import { BaseError, BaseQuery, ErrorUtils, InternalServerError, QueryResult, waitFor } from '@tronius/shared-common';

import { queryHandlerClassConstructorMetaDataKey, queryResultClassConstructorMetaDataKey } from './constants';
import { MessageBroker } from './message.broker';
import {
    QueryClassConstructor,
    QueryHandler,
    QueryHandlerMethod,
    QueryResultClassConstructor,
    UnsubscribeHandlerMethod
} from './message.broker.types';

@Injectable()
export class QueryHandlerLoader implements OnModuleInit, OnModuleDestroy, BeforeApplicationShutdown {
    private readonly queryHandlerUnsubscribeMethods: UnsubscribeHandlerMethod[] = [];

    private readonly logger: Logger = new Logger(QueryHandlerLoader.name);

    private runningQueryHandlers = 0;

    constructor(
        private readonly discoveryService: DiscoveryService,
        private readonly reflector: Reflector,
        private readonly messageBroker: MessageBroker
    ) {}

    async beforeApplicationShutdown(_signal?: string): Promise<void> {
        await this.waitForHandlersToFinish();
    }

    async onModuleDestroy(): Promise<void> {
        if (!this.queryHandlerUnsubscribeMethods.length) {
            return;
        }
        this.logger.debug(`Unsubscribing ${this.queryHandlerUnsubscribeMethods.length} query handlers`);
        await Promise.all(this.queryHandlerUnsubscribeMethods);
    }

    async onModuleInit(): Promise<void> {
        await this.loadQueryHandlers();
    }

    private async waitForHandlersToFinish(): Promise<void> {
        if (!this.runningQueryHandlers) {
            return;
        }

        this.logger.debug(`Waiting for ${this.runningQueryHandlers} command handlers to finish`);

        const waitTimeInMs = 2_000;

        await waitFor(waitTimeInMs);
        await this.waitForHandlersToFinish();
    }

    private async loadQueryHandlers(): Promise<void> {
        const queryHandlers = this.getEventHandlers();
        if (!queryHandlers.length) {
            return;
        }

        const queryHandlersMap = new Map<string, QueryHandler<BaseQuery, QueryResult>>();
        const queryClassConstructorsMap = new Map<string, QueryClassConstructor<BaseQuery>>();
        const queryResultClassConstructorsMap = new Map<string, QueryResultClassConstructor<QueryResult>>();

        for (const { queryClassConstructor, queryResultClassConstructor, queryHandlerInstance } of queryHandlers) {
            const queryName = queryClassConstructor.name;

            if (queryHandlersMap.has(queryName)) {
                throw new InternalServerError({ message: `Encountered duplicate Query handler for query:"${queryName}"` });
            }

            queryHandlersMap.set(queryName, queryHandlerInstance);
            queryClassConstructorsMap.set(queryName, queryClassConstructor);
            queryResultClassConstructorsMap.set(queryName, queryResultClassConstructor);
        }
        const logBaseError = (error: BaseError, queryName: string): void => {
            // eslint-disable-next-line @typescript-eslint/no-magic-numbers
            const shouldLogError = error.statusCode >= 500;
            if (shouldLogError) {
                this.logger.error(`Error handling "query":"${queryName}" "error":"${ErrorUtils.errorToString(error)}"`, error);
                return;
            }

            this.logger.warn(`Error handling "query":"${queryName}" "error":"${ErrorUtils.errorToString(error)}"`, error);
        };

        const registerQueryHandlersPromises: Array<ReturnType<typeof this.messageBroker.registerQueryHandler>> = [];

        for (const queryName of queryHandlersMap.keys()) {
            /* eslint-disable @typescript-eslint/no-non-null-assertion */
            const queryHandlerInstance = queryHandlersMap.get(queryName)!;
            const queryClassConstructor = queryClassConstructorsMap.get(queryName)!;
            const queryResultClassConstructor = queryResultClassConstructorsMap.get(queryName)!;

            const wrappedQueryHandler: QueryHandlerMethod<BaseQuery, QueryResult> = async (query: BaseQuery): Promise<QueryResult> => {
                if (!(query instanceof BaseQuery)) {
                    throw new InternalServerError({ message: 'Query parameter is not an instance of BaseQuery' });
                }

                if (!(query instanceof queryClassConstructor)) {
                    throw new InternalServerError({ message: `Invalid query instance for [query]:"${queryName}"` });
                }

                try {
                    this.runningQueryHandlers++;
                    const result = await queryHandlerInstance.handle(query);

                    if (!(result instanceof queryResultClassConstructor)) {
                        throw new InternalServerError({ message: `Invalid query result instance for [query]:"${queryName}"` });
                    }

                    return result;
                } catch (error) {
                    if (error instanceof BaseError) {
                        logBaseError(error, queryName);
                        throw error;
                    }

                    this.logger.error(`Error handling [query]:"${queryName}", [error]:${ErrorUtils.errorToString(error)}`, error);

                    if (error instanceof Error) {
                        throw new InternalServerError({ message: error.message, params: { queryName } });
                    }

                    throw new InternalServerError({
                        message: `Unknown error while handling [query]:"${queryName}"`,
                        params: { queryName }
                    });
                } finally {
                    this.runningQueryHandlers--;
                }
            };

            registerQueryHandlersPromises.push(
                this.messageBroker.registerQueryHandler(queryClassConstructor, queryResultClassConstructor, wrappedQueryHandler)
            );

            /* eslint-enable @typescript-eslint/no-non-null-assertion */
        }

        this.logger.debug(`Registering ${registerQueryHandlersPromises.length} query handlers`);
        const unsubscribeHandlers = await Promise.all(registerQueryHandlersPromises);
        this.queryHandlerUnsubscribeMethods.push(...unsubscribeHandlers);
    }

    private getEventHandlers(): Array<{
        queryClassConstructor: QueryClassConstructor<BaseQuery>;
        queryResultClassConstructor: QueryResultClassConstructor<QueryResult>;
        queryHandlerInstance: QueryHandler<BaseQuery, QueryResult>;
    }> {
        const queryHandlerInstanceWrappers = this.discoveryService.getProviders().filter(({ metatype, isAlias }) => {
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
            if (!metatype) {
                return false;
            }

            if (isAlias) {
                return false;
            }

            return !!this.reflector.get(queryHandlerClassConstructorMetaDataKey, metatype);
        });

        return queryHandlerInstanceWrappers.map(({ metatype, instance }) => {
            const queryClassConstructor = this.reflector.get<QueryClassConstructor<BaseQuery>>(
                queryHandlerClassConstructorMetaDataKey,
                metatype
            );
            const queryResultClassConstructor = this.reflector.get<QueryResultClassConstructor<QueryResult>>(
                queryResultClassConstructorMetaDataKey,
                metatype
            );

            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
            if (!queryResultClassConstructor) {
                throw new InternalServerError({ message: `Query handler ${metatype.name} does not have a query result class constructor` });
            }

            const queryHandlerInstance = instance as QueryHandler<BaseQuery, QueryResult>;

            return { queryClassConstructor, queryResultClassConstructor, queryHandlerInstance };
        });
    }
}
