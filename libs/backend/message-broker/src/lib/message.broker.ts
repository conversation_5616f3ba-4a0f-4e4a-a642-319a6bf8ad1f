/* eslint-disable no-param-reassign */
import { ExecutionContext } from '@cms/execution-context';
import { Injectable, Logger, Optional } from '@nestjs/common';
import { BaseCommand, BaseEvent, BaseQuery, CommandResult, NewableOrStaticCreateOrMap, QueryResult, waitFor } from '@tronius/shared-common';

import { MessageBrokerPort } from './message.broker.port';
import {
    CommandClassConstructor,
    CommandHandlerMethod,
    CommandResultClassConstructor,
    EventClassConstructor,
    EventHandlerMethod,
    QueryClassConstructor,
    QueryHandlerMethod,
    QueryResultClassConstructor,
    UnsubscribeHandlerMethod
} from './message.broker.types';

@Injectable()
export class MessageBroker implements MessageBrokerPort {
    protected readonly logger = new Logger(MessageBroker.name);

    protected runningRpcCalls = 0;

    constructor(
        private readonly messageBrokerPort: MessageBrokerPort,
        @Optional() private readonly executionContext?: ExecutionContext
    ) {}

    publish<T extends BaseEvent>(classConstructor: EventClassConstructor<T>, event: T): Promise<void> | void {
        if (this.executionContext) {
            event.executionContext = this.executionContext.serialize();
        }

        return this.messageBrokerPort.publish(classConstructor, event);
    }

    subscribe<T extends BaseEvent>(
        classConstructor: EventClassConstructor<T>,
        eventHandler: EventHandlerMethod<T>,
        eventHandlerName: string
    ): PromiseLike<UnsubscribeHandlerMethod> | UnsubscribeHandlerMethod {
        if (!this.executionContext) {
            return this.messageBrokerPort.subscribe(classConstructor, eventHandler, eventHandlerName);
        }

        const eventHandlerWithExecutionContext = async (event: T): Promise<void> => {
            const { executionContext = {} } = event;

            // @ts-expect-error false positive
            await this.executionContext.run(
                {
                    ...executionContext,
                    event: event.constructor.name,
                    eventId: event.eventId,
                    eventHandlerName
                },

                async () => {
                    const eventHandlingStart = Date.now();
                    this.logger.debug(
                        `Handling "event":"${event.constructor.name}" "eventId":"${event.eventId}" "eventHandlerName":"${eventHandlerName}"`
                    );
                    await eventHandler(event);

                    const eventHandlingDuration = Date.now() - eventHandlingStart;
                    this.executionContext?.set('duration', eventHandlingDuration);
                    this.logger.debug(
                        `Handled "event":"${event.constructor.name}" "eventId":"${event.eventId}" "eventHandlerName":"${eventHandlerName}" "duration":"${eventHandlingDuration}ms"`
                    );
                    this.executionContext?.clear('duration');
                }
            );
        };

        return this.messageBrokerPort.subscribe(classConstructor, eventHandlerWithExecutionContext, eventHandlerName);
    }

    unsubscribe(unsubscribe: UnsubscribeHandlerMethod): Promise<void> | void {
        return this.messageBrokerPort.unsubscribe(unsubscribe);
    }

    registerQueryHandler<Q extends BaseQuery, R extends QueryResult>(
        queryClassConstructor: QueryClassConstructor<Q>,
        queryResultClassConstructor: QueryResultClassConstructor<R>,
        handler: QueryHandlerMethod<Q, R>
    ): PromiseLike<UnsubscribeHandlerMethod> | UnsubscribeHandlerMethod {
        if (!this.executionContext) {
            return this.messageBrokerPort.registerQueryHandler(queryClassConstructor, queryResultClassConstructor, handler);
        }

        const handlerWithExecutionContext = async (query: Q): Promise<R> => {
            const { executionContext = {}, correlationId } = query;

            // @ts-expect-error false positive
            return this.executionContext.run({ ...executionContext, query: query.constructor.name, correlationId }, async () => {
                const queryHandlingStart = Date.now();
                this.logger.debug(`Handling "query":"${query.constructor.name}" "correlationId":"${correlationId}"`);

                const queryResult = await handler(query);

                const contextAfterQueryInvocation = this.executionContext?.serialize() || {};

                const queryHandlingDuration = Date.now() - queryHandlingStart;
                this.executionContext?.set('duration', queryHandlingDuration);
                this.logger.debug(
                    `Handled "query":"${query.constructor.name}" "correlationId":"${correlationId}" "duration":"${queryHandlingDuration}ms"`
                );
                this.executionContext?.clear('duration');

                delete contextAfterQueryInvocation.query;
                delete contextAfterQueryInvocation.correlationId;

                queryResult.executionContext = contextAfterQueryInvocation;

                return queryResult;
            });
        };

        return this.messageBrokerPort.registerQueryHandler(queryClassConstructor, queryResultClassConstructor, handlerWithExecutionContext);
    }

    registerCommandHandler<C extends BaseCommand, R extends CommandResult>(
        commandClassConstructor: CommandClassConstructor<C>,
        commandResultClassConstructor: CommandResultClassConstructor<R>,
        handler: CommandHandlerMethod<C, R>
    ): PromiseLike<UnsubscribeHandlerMethod> | UnsubscribeHandlerMethod {
        if (!this.executionContext) {
            return this.messageBrokerPort.registerCommandHandler(commandClassConstructor, commandResultClassConstructor, handler);
        }

        const handlerWithExecutionContext = async (command: C): Promise<R> => {
            const { executionContext = {}, correlationId } = command;
            this.logger.debug(`Handling "command":"${command.constructor.name}" "correlationId":"${correlationId}"`);
            // @ts-expect-error false positive
            return this.executionContext.run({ ...executionContext, command: command.constructor.name, correlationId }, async () => {
                const commandHandlingStart = Date.now();
                this.logger.debug(`Handling "command":"${command.constructor.name}" "correlationId":"${correlationId}"`);

                const commandHandlerResult = await handler(command);

                const contextAfterCommandInvocation = this.executionContext?.serialize() || {};

                const commandHandlingDuration = Date.now() - commandHandlingStart;
                this.executionContext?.set('duration', commandHandlingDuration);
                this.logger.debug(
                    `Handled "command":"${command.constructor.name}" "correlationId":"${correlationId}" "duration":"${commandHandlingDuration}ms"`
                );
                this.executionContext?.clear('duration');

                delete contextAfterCommandInvocation.command;
                delete contextAfterCommandInvocation.correlationId;

                commandHandlerResult.executionContext = contextAfterCommandInvocation;

                return commandHandlerResult;
            });
        };

        return this.messageBrokerPort.registerCommandHandler(
            commandClassConstructor,
            commandResultClassConstructor,
            handlerWithExecutionContext
        );
    }

    unsubscribeQueryHandler(unsubscribe: UnsubscribeHandlerMethod): Promise<void> | void {
        return this.messageBrokerPort.unsubscribeQueryHandler(unsubscribe);
    }

    unsubscribeCommandHandler(unsubscribe: UnsubscribeHandlerMethod): Promise<void> | void {
        return this.messageBrokerPort.unsubscribeCommandHandler(unsubscribe);
    }

    async query<R extends QueryResult, Q extends BaseQuery = BaseQuery>(
        query: Q,
        queryResultClassConstructor: NewableOrStaticCreateOrMap<R>
    ): Promise<R> {
        try {
            this.runningRpcCalls++;
            query.executionContext = this.executionContext?.serialize() || {};

            const { correlationId } = query;
            const queryName = query.constructor.name;

            this.logger.debug(`Invoking "query":"${query.constructor.name}" "correlationId":"${correlationId}"`);

            const queryStartTimestamp = Date.now();
            const queryResult = await this.messageBrokerPort.query(query, queryResultClassConstructor);

            const { executionContext: queryResultExecutionContext = {} } = queryResult;

            this.executionContext?.assign(queryResultExecutionContext);

            const queryEndTimestamp = Date.now();
            const duration = queryEndTimestamp - queryStartTimestamp;

            this.executionContext?.set('duration', duration);
            this.logger.debug(`Finished "query":"${queryName}" "correlationId":"${correlationId}" "duration":"${duration}ms"`);
            this.executionContext?.clear('duration');

            return queryResult;
        } finally {
            this.runningRpcCalls--;
        }
    }

    async command<R extends CommandResult = CommandResult, C extends BaseCommand = BaseCommand>(
        command: C,
        commandResultClassConstructor?: CommandResultClassConstructor<R>
    ): Promise<R> {
        try {
            this.runningRpcCalls++;
            command.executionContext = this.executionContext?.serialize() || {};
            const { correlationId } = command;
            const commandName = command.constructor.name;
            this.executionContext?.assign({ correlationId, command: commandName });
            this.logger.debug(`Invoking "command":"${command.constructor.name}" "correlationId":"${correlationId}"`);

            const commandStartTimestamp = Date.now();
            const commandResult = await this.messageBrokerPort.command(command, commandResultClassConstructor);

            const { executionContext: commandResultExecutionContext = {} } = commandResult;

            this.executionContext?.assign(commandResultExecutionContext);

            const commandEndTimestamp = Date.now();
            const duration = commandEndTimestamp - commandStartTimestamp;

            this.executionContext?.set('duration', duration);
            this.logger.debug(`Finished "command":"${commandName}" "correlationId":"${correlationId}" "duration":"${duration}ms"`);
            this.executionContext?.clear('duration');

            return commandResult;
        } finally {
            this.runningRpcCalls--;
        }
    }

    protected async beforeApplicationShutdown(): Promise<void> {
        const gracePeriodForRunningOperationsInMs = 100;

        while (this.runningRpcCalls > 0) {
            this.logger.debug(`Waiting for "runningRpcCalls":"${this.runningRpcCalls}" RPC calls to finish`);
            await waitFor(gracePeriodForRunningOperationsInMs);
        }
    }
}
