import { BeforeApplicationShutdown, Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { DiscoveryService, Reflector } from '@nestjs/core';
import { BaseCommand, BaseError, CommandResult, ErrorUtils, InternalServerError, waitFor } from '@tronius/shared-common';

import { commandHandlerClassConstructorMetaDataKey, commandResultClassConstructorMetaDataKey } from './constants';
import { MessageBroker } from './message.broker';
import {
    CommandClassConstructor,
    CommandHandler,
    CommandHandlerMethod,
    CommandResultClassConstructor,
    UnsubscribeHandlerMethod
} from './message.broker.types';

@Injectable()
export class CommandHandlerLoader implements OnModuleInit, OnModuleDestroy, BeforeApplicationShutdown {
    private readonly commandHandlerUnsubscribeMethods: UnsubscribeHandlerMethod[] = [];

    private readonly logger: Logger = new Logger(CommandHandlerLoader.name);

    private runningCommandHandlers = 0;

    constructor(
        private readonly discoveryService: DiscoveryService,
        private readonly reflector: Reflector,
        private readonly messageBroker: MessageBroker
    ) {}

    async beforeApplicationShutdown(_signal?: string): Promise<void> {
        await this.waitForRunningHandlersToFinish();
    }

    async onModuleDestroy(): Promise<void> {
        if (!this.commandHandlerUnsubscribeMethods.length) {
            return;
        }

        this.logger.debug(`Unsubscribing ${this.commandHandlerUnsubscribeMethods.length} command handlers`);
        await Promise.all(this.commandHandlerUnsubscribeMethods);
    }

    async onModuleInit(): Promise<void> {
        await this.loadCommandHandlers();
    }

    private async waitForRunningHandlersToFinish(): Promise<void> {
        if (!this.runningCommandHandlers) {
            return;
        }

        this.logger.debug(`Waiting for ${this.runningCommandHandlers} command handlers to finish`);

        const waitTimeInMs = 2_000;

        await waitFor(waitTimeInMs);
        await this.waitForRunningHandlersToFinish();
    }

    private async loadCommandHandlers(): Promise<void> {
        const commandHandlers = this.getCommandHandlers();
        if (!commandHandlers.length) {
            return;
        }

        const commandHandlersMap = new Map<string, CommandHandler<BaseCommand, CommandResult>>();
        const commandClassConstructorsMap = new Map<string, CommandClassConstructor<BaseCommand>>();
        const commandResultClassConstructorsMap = new Map<string, CommandResultClassConstructor<CommandResult>>();

        for (const { commandClassConstructor, commandResultClassConstructor, commandHandlerInstance } of commandHandlers) {
            const commandName = commandClassConstructor.name;

            if (commandHandlersMap.has(commandName)) {
                throw new InternalServerError({ message: `Encountered duplicate Command handler for command:"${commandName}"` });
            }

            commandHandlersMap.set(commandName, commandHandlerInstance);
            commandClassConstructorsMap.set(commandName, commandClassConstructor);
            commandResultClassConstructorsMap.set(commandName, commandResultClassConstructor);
        }

        const registerCommandHandlersPromises: Array<ReturnType<typeof this.messageBroker.registerCommandHandler>> = [];

        const logBaseError = (error: BaseError, commandName: string): void => {
            // eslint-disable-next-line @typescript-eslint/no-magic-numbers
            const shouldLogError = error.statusCode >= 500;
            if (shouldLogError) {
                this.logger.error(`Error handling "command":"${commandName}" "error":"${ErrorUtils.errorToString(error)}"`, error);
                return;
            }

            this.logger.warn(`Error handling "command":"${commandName}" "error":"${ErrorUtils.errorToString(error)}"`, error);
        };

        for (const commandName of commandHandlersMap.keys()) {
            /* eslint-disable @typescript-eslint/no-non-null-assertion */
            const commandHandlerInstance = commandHandlersMap.get(commandName)!;
            const commandClassConstructor = commandClassConstructorsMap.get(commandName)!;
            const commandResultClassConstructor = commandResultClassConstructorsMap.get(commandName)!;

            const wrappedCommandHandler: CommandHandlerMethod<BaseCommand, CommandResult> = async (
                command: BaseCommand
            ): Promise<CommandResult> => {
                if (!(command instanceof BaseCommand)) {
                    throw new InternalServerError({ message: 'Command parameter is not an instance of BaseCommand' });
                }

                if (!(command instanceof commandClassConstructor)) {
                    throw new InternalServerError({ message: `Invalid command instance for [command]:"${commandName}"` });
                }

                try {
                    this.runningCommandHandlers++;
                    const result = await commandHandlerInstance.handle(command);

                    if (!(result instanceof commandResultClassConstructor)) {
                        throw new InternalServerError({ message: `Invalid Command result instance for [command]:"${commandName}"` });
                    }

                    return result;
                } catch (error) {
                    if (error instanceof BaseError) {
                        logBaseError(error, commandName);
                        throw error;
                    }

                    this.logger.error(`Error handling [command]:"${commandName}", [error]:${ErrorUtils.errorToString(error)}`, error);
                    if (error instanceof Error) {
                        throw new InternalServerError({ message: error.message, params: { commandName } });
                    }

                    throw new InternalServerError({
                        message: `Unknown error while handling [command]:"${commandName}"`,
                        params: { commandName }
                    });
                } finally {
                    this.runningCommandHandlers--;
                }
            };

            registerCommandHandlersPromises.push(
                this.messageBroker.registerCommandHandler(commandClassConstructor, commandResultClassConstructor, wrappedCommandHandler)
            );

            /* eslint-enable @typescript-eslint/no-non-null-assertion */
        }

        this.logger.debug(`Registering ${registerCommandHandlersPromises.length} command handlers`);
        const unsubscribeHandlers = await Promise.all(registerCommandHandlersPromises);
        this.commandHandlerUnsubscribeMethods.push(...unsubscribeHandlers);
    }

    private getCommandHandlers(): Array<{
        commandClassConstructor: CommandClassConstructor<BaseCommand>;
        commandResultClassConstructor: CommandResultClassConstructor<CommandResult>;
        commandHandlerInstance: CommandHandler<BaseCommand, CommandResult>;
    }> {
        const commandHandlerInstanceWrappers = this.discoveryService.getProviders().filter(({ metatype, isAlias }) => {
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
            if (!metatype) {
                return false;
            }

            if (isAlias) {
                return false;
            }

            return !!this.reflector.get(commandHandlerClassConstructorMetaDataKey, metatype);
        });

        return commandHandlerInstanceWrappers.map(({ metatype, instance }) => {
            const commandClassConstructor = this.reflector.get<CommandClassConstructor<BaseCommand>>(
                commandHandlerClassConstructorMetaDataKey,
                metatype
            );
            const commandResultClassConstructor = this.reflector.get<CommandResultClassConstructor<CommandResult>>(
                commandResultClassConstructorMetaDataKey,
                metatype
            );

            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
            if (!commandResultClassConstructor) {
                throw new InternalServerError({
                    message: `Command handler ${metatype.name} does not have a command result class constructor`
                });
            }

            const commandHandlerInstance = instance as CommandHandler<BaseCommand, CommandResult>;

            return { commandClassConstructor, commandResultClassConstructor, commandHandlerInstance };
        });
    }
}
