import { ReservationStatus, WalletFundReservationProps } from '@cms/wallets';
import { ProfitCenterType } from '@tronius/shared-common';
import { Type } from 'class-transformer';
import { Column, Entity, Index, PrimaryColumn } from 'typeorm';

@Entity({ name: 'wallet_fund_reservation' })
@Index(['walletId', 'status'])
export class WalletFundReservationOrmEntity implements WalletFundReservationProps {
    @PrimaryColumn({ type: 'uuid' })
    id!: string;

    @Column({ type: 'uuid', nullable: false })
    @Index()
    walletId!: string;

    @Column({ type: 'numeric', nullable: false })
    amountInWalletCurrency!: number;

    @Column({ type: 'varchar', nullable: false })
    status!: ReservationStatus;

    @Column({ type: 'timestamp', nullable: false })
    @Type(() => Date)
    reservedAt!: Date;

    @Column({ type: 'varchar', nullable: false })
    username!: string;

    @Column({ type: 'varchar', nullable: false })
    profitCenterType!: ProfitCenterType;

    @Column({ type: 'varchar', nullable: false })
    profitCenterId!: string;
}
