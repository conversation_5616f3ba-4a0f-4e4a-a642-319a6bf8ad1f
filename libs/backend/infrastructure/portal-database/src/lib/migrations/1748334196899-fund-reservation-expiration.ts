import { MigrationInterface, QueryRunner } from 'typeorm';

export class FundReservationExpiration1748334196899 implements MigrationInterface {
    name = 'FundReservationExpiration1748334196899';

    async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DELETE from "wallet_fund_reservation" WHERE "status" = \'Expired\'');
        await queryRunner.query('ALTER TABLE "wallet_fund_reservation" DROP COLUMN "reservation_expiration"');
    }

    async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "wallet_fund_reservation" ADD "reservation_expiration" TIMESTAMP NOT NULL');
    }
}
