import {
    CashWallet,
    FreePlayWallet,
    ReservationStatus,
    WalletFundReservation,
    WalletOperation,
    WalletOperationType,
    WalletType
} from '@cms/wallets';
import { WalletRepositoryPort } from '@cms/wallets-domain';
import { Test, TestingModule } from '@nestjs/testing';
import { Currency, ProfitCenterType, UUIDFactory } from '@tronius/shared-common';
import { EntityManager, Repository } from 'typeorm';

import { WalletFundReservationOrmEntity, WalletOperationOrmEntity, WalletOrmEntity } from '../entities';
import { CmsPortalDatabase } from '../portal-database.module';
import { WalletRepositoryAdapter } from './wallet.repository.adapter';

describe('WalletRepositoryAdapter database integration', () => {
    let testingModule: TestingModule;
    let walletRepository: WalletRepositoryPort;
    let walletTypeOrmRepository: Repository<WalletOrmEntity>;
    let walletOperationTypeOrmRepository: Repository<WalletOperationOrmEntity>;
    let walletFundReservationTypeOrmRepository: Repository<WalletFundReservationOrmEntity>;

    beforeAll(async () => {
        const module = await Test.createTestingModule({
            imports: [CmsPortalDatabase.forRoot()],
            providers: []
        }).compile();

        testingModule = await module.init();

        const entityManager = testingModule.get(EntityManager);

        walletRepository = testingModule.get(WalletRepositoryPort);
        walletTypeOrmRepository = entityManager.getRepository(WalletOrmEntity);
        walletOperationTypeOrmRepository = entityManager.getRepository(WalletOperationOrmEntity);
        walletFundReservationTypeOrmRepository = entityManager.getRepository(WalletFundReservationOrmEntity);
    });

    it('should provide the repository adapter', () => {
        expect(walletRepository).toBeDefined();
        expect(walletRepository).toBeInstanceOf(WalletRepositoryAdapter);
    });

    it('should save and retrieve a cash wallet', async () => {
        const cashWallet = CashWallet.map({
            id: UUIDFactory.create(),
            balance: 0,
            currency: Currency.AED,
            openedAt: new Date(),
            ownerId: UUIDFactory.create(),
            reservedFunds: 0
        });

        await walletRepository.save(cashWallet);

        const cashWalletFromDb = await walletRepository.findByTypeOwnerAndCurrency(
            WalletType.CashWallet,
            cashWallet.ownerId,
            cashWallet.currency
        );
        expect(cashWalletFromDb).toEqual(cashWallet);

        const cashWalletOrmEntity = await walletTypeOrmRepository.findOne({ where: { id: cashWallet.id } });
        expect({ ...cashWalletOrmEntity, reservedFunds: 0 }).toEqual(cashWallet);
    });

    it('should process a wallet operation transaction', async () => {
        const cashWallet = CashWallet.map({
            id: UUIDFactory.create(),
            balance: 0,
            currency: Currency.AED,
            openedAt: new Date(),
            ownerId: UUIDFactory.create(),
            reservedFunds: 0
        });

        await walletRepository.save(cashWallet);

        const depositOperation = WalletOperation.map({
            id: UUIDFactory.create(),
            amount: 100,
            walletId: cashWallet.id,
            operationType: WalletOperationType.Deposit,
            processedAt: new Date(),
            username: 'test-user',
            profitCenterType: ProfitCenterType.TransactionPoint,
            profitCenterId: UUIDFactory.create()
        });

        const balanceUpdated = await walletRepository.processWalletOperationTransaction(
            async (saveCashWalletOperation, updateWalletBalance) => {
                await saveCashWalletOperation(depositOperation);

                const transactionalBalanceUpdateResult = await updateWalletBalance(
                    cashWallet.id,
                    cashWallet.balance,
                    depositOperation.amount
                );
                return transactionalBalanceUpdateResult;
            }
        );

        expect(balanceUpdated).toBe(true);

        const cashWalletAfterOperation = await walletRepository.findByTypeOwnerAndCurrency(
            WalletType.CashWallet,
            cashWallet.ownerId,
            cashWallet.currency
        );
        expect(cashWalletAfterOperation?.balance).toBe(depositOperation.amount);

        const cashOperationRecord = await walletOperationTypeOrmRepository.findOne({ where: { id: depositOperation.id } });
        expect(cashOperationRecord).toEqual(depositOperation);
    });

    it('should notify us of an unsuccessful balance update', async () => {
        const cashWallet = CashWallet.map({
            id: UUIDFactory.create(),
            balance: 69,
            currency: Currency.AED,
            openedAt: new Date(),
            ownerId: UUIDFactory.create(),
            reservedFunds: 0
        });

        await walletRepository.save(cashWallet);

        const depositOperation = WalletOperation.map({
            id: UUIDFactory.create(),
            amount: 100,
            walletId: cashWallet.id,
            operationType: WalletOperationType.Withdraw,
            processedAt: new Date(),
            username: 'test-user',
            profitCenterType: ProfitCenterType.TransactionPoint,
            profitCenterId: UUIDFactory.create()
        });

        const balanceUpdated = await walletRepository.processWalletOperationTransaction(
            async (saveCashWalletOperation, updateWalletBalance) => {
                await saveCashWalletOperation(depositOperation);

                // Simulate a concurrent update
                await walletTypeOrmRepository.update({ id: cashWallet.id }, { balance: 42 });

                const transactionalBalanceUpdateResult = await updateWalletBalance(
                    cashWallet.id,
                    cashWallet.balance,
                    depositOperation.amount
                );

                return transactionalBalanceUpdateResult;
            }
        );

        expect(balanceUpdated).toBe(false);
    });

    it('should rollback the transaction if an error occurs', async () => {
        const cashWallet = CashWallet.map({
            id: UUIDFactory.create(),
            balance: 69,
            currency: Currency.AED,
            openedAt: new Date(),
            ownerId: UUIDFactory.create(),
            reservedFunds: 0
        });

        await walletRepository.save(cashWallet);

        const depositOperation = WalletOperation.map({
            id: UUIDFactory.create(),
            amount: 100,
            walletId: cashWallet.id,
            operationType: WalletOperationType.Withdraw,
            processedAt: new Date(),
            username: 'test-user',
            profitCenterType: ProfitCenterType.TransactionPoint,
            profitCenterId: UUIDFactory.create()
        });

        try {
            await walletRepository.processWalletOperationTransaction(async (saveCashWalletOperation, updateWalletBalance) => {
                await saveCashWalletOperation(depositOperation);

                await updateWalletBalance(cashWallet.id, cashWallet.balance, depositOperation.amount);

                throw new Error('Test error');
            });
        } catch (ignoreError: unknown) {
            // Ignore the error
        }

        const cashWalletAfterOperation = await walletRepository.findByTypeOwnerAndCurrency(
            WalletType.CashWallet,
            cashWallet.ownerId,
            cashWallet.currency
        );
        expect(cashWalletAfterOperation?.balance).toBe(cashWallet.balance);

        const cashOperationRecord = await walletOperationTypeOrmRepository.findOne({ where: { id: depositOperation.id } });
        expect(cashOperationRecord).toBeNull();
    });

    it('should save and retrieve a cash wallet fund reservation', async () => {
        const cashWallet = CashWallet.map({
            id: UUIDFactory.create(),
            balance: 69,
            currency: Currency.AED,
            openedAt: new Date(),
            ownerId: UUIDFactory.create(),
            reservedFunds: 0
        });

        await walletRepository.save(cashWallet);

        const cashWalletFundReservation = WalletFundReservation.map({
            id: UUIDFactory.create(),
            status: ReservationStatus.Active,
            amountInWalletCurrency: 42,
            reservedAt: new Date(),
            walletId: cashWallet.id,
            username: 'test-user',
            profitCenterType: ProfitCenterType.TransactionPoint,
            profitCenterId: UUIDFactory.create()
        });

        await walletRepository.saveReservation(cashWalletFundReservation);

        const reservationFromDb = await walletRepository.findReservationById(cashWalletFundReservation.id);
        expect(reservationFromDb).toEqual(cashWalletFundReservation);

        const reservationOrmEntity = await walletFundReservationTypeOrmRepository.findOne({
            where: { id: cashWalletFundReservation.id }
        });
        expect(reservationOrmEntity).toEqual(cashWalletFundReservation);
    });

    it('should return a cash wallet with reserved funds', async () => {
        const walletBalance = 69;

        const mockCashWallet = CashWallet.map({
            id: UUIDFactory.create(),
            balance: walletBalance,
            currency: Currency.AED,
            openedAt: new Date(),
            ownerId: UUIDFactory.create(),
            reservedFunds: 0
        });

        await walletRepository.save(mockCashWallet);

        const firstReservationAmount = 10;
        const secondReservationAmount = 46;

        const walletReservations: WalletFundReservation[] = [
            WalletFundReservation.map({
                id: UUIDFactory.create(),
                status: ReservationStatus.Active,
                amountInWalletCurrency: firstReservationAmount,
                reservedAt: new Date(),
                walletId: mockCashWallet.id,
                username: 'test-user',
                profitCenterType: ProfitCenterType.TransactionPoint,
                profitCenterId: UUIDFactory.create()
            }),
            WalletFundReservation.map({
                id: UUIDFactory.create(),
                status: ReservationStatus.Active,
                amountInWalletCurrency: secondReservationAmount,
                reservedAt: new Date(),
                walletId: mockCashWallet.id,
                username: 'test-user',
                profitCenterType: ProfitCenterType.TransactionPoint,
                profitCenterId: UUIDFactory.create()
            })
        ];

        await walletFundReservationTypeOrmRepository.insert(walletReservations);

        const cashWallet = await walletRepository.findByTypeOwnerAndCurrency(
            mockCashWallet.type,
            mockCashWallet.ownerId,
            mockCashWallet.currency
        );

        const expectedReserveFunds = firstReservationAmount + secondReservationAmount;
        const expectedRedeemableFunds = walletBalance - expectedReserveFunds;

        expect(cashWallet?.reservedFunds).toBe(expectedReserveFunds);
        expect(cashWallet?.redeemableFunds).toBe(expectedRedeemableFunds);
    });

    it('should update the status of a wallet fund reservation', async () => {
        const cashWallet = CashWallet.map({
            id: UUIDFactory.create(),
            balance: 69,
            currency: Currency.AED,
            openedAt: new Date(),
            ownerId: UUIDFactory.create(),
            reservedFunds: 0
        });

        await walletRepository.save(cashWallet);

        const cashWalletFundReservation = WalletFundReservation.map({
            id: UUIDFactory.create(),
            status: ReservationStatus.Active,
            amountInWalletCurrency: 42,
            reservedAt: new Date(),
            walletId: cashWallet.id,
            username: 'test-user',
            profitCenterType: ProfitCenterType.TransactionPoint,
            profitCenterId: UUIDFactory.create()
        });

        await walletRepository.saveReservation(cashWalletFundReservation);

        const newStatus = ReservationStatus.Used;
        const newUsedAmount = 20;

        await walletRepository.updateReservationStatus(cashWalletFundReservation.id, newStatus, newUsedAmount);

        const reservationFromDb = await walletRepository.findReservationById(cashWalletFundReservation.id);
        expect(reservationFromDb?.status).toBe(newStatus);
        expect(reservationFromDb?.amountInWalletCurrency).toBe(newUsedAmount);
    });

    it('should save a free play wallet', async () => {
        const freePlayWallet = FreePlayWallet.map({
            id: UUIDFactory.create(),
            balance: 69,
            currency: Currency.AED,
            openedAt: new Date(),
            ownerId: UUIDFactory.create(),
            reservedFunds: 0
        });
        await walletRepository.save(freePlayWallet);

        const freePlayWalletFromDb = await walletRepository.findByTypeOwnerAndCurrency(
            WalletType.FreePlayWallet,
            freePlayWallet.ownerId,
            freePlayWallet.currency
        );
        expect(freePlayWalletFromDb).toEqual(freePlayWallet);
    });

    it('should retrieve a free play wallet with reserved funds', async () => {
        const walletBalance = 69;
        const mockFreePlayWallet = FreePlayWallet.map({
            id: UUIDFactory.create(),
            balance: walletBalance,
            currency: Currency.AED,
            openedAt: new Date(),
            ownerId: UUIDFactory.create(),
            reservedFunds: 0
        });

        await walletRepository.save(mockFreePlayWallet);

        const firstReservationAmount = 10;
        const secondReservationAmount = 46;
        const walletReservations: WalletFundReservation[] = [
            WalletFundReservation.map({
                id: UUIDFactory.create(),
                status: ReservationStatus.Active,
                amountInWalletCurrency: firstReservationAmount,
                reservedAt: new Date(),
                walletId: mockFreePlayWallet.id,
                username: 'test-user',
                profitCenterType: ProfitCenterType.TransactionPoint,
                profitCenterId: UUIDFactory.create()
            }),
            WalletFundReservation.map({
                id: UUIDFactory.create(),
                status: ReservationStatus.Active,
                amountInWalletCurrency: secondReservationAmount,
                reservedAt: new Date(),
                walletId: mockFreePlayWallet.id,
                username: 'test-user',
                profitCenterType: ProfitCenterType.TransactionPoint,
                profitCenterId: UUIDFactory.create()
            })
        ];

        await walletFundReservationTypeOrmRepository.insert(walletReservations);

        const freePlayWallet = await walletRepository.findByTypeOwnerAndCurrency(
            mockFreePlayWallet.type,
            mockFreePlayWallet.ownerId,
            mockFreePlayWallet.currency
        );

        const expectedReserveFunds = firstReservationAmount + secondReservationAmount;
        const expectedRedeemableFunds = walletBalance - expectedReserveFunds;

        expect(freePlayWallet?.reservedFunds).toBe(expectedReserveFunds);
        expect(freePlayWallet?.redeemableFunds).toBe(expectedRedeemableFunds);
    });

    afterAll(async () => {
        await testingModule.close();
    });
});
