import { DynamicModule, LogLevel } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import pino, { BaseLogger, LoggerOptions } from 'pino';
import { LokiOptions } from 'pino-loki';
import { PrettyOptions } from 'pino-pretty';

import { CmsLoggerConfig } from './cms-logger.config';
import { CmsLogger } from './cms-logger.service';
import { pinoLoggerInjectionToken } from './constants';
import { LoggerFormat } from './types';

export class CmsLoggerModule {
    static forRoot(app: string): DynamicModule {
        return {
            module: CmsLoggerModule,
            imports: [CmsLoggerModule.logConfigProvider()],
            providers: [
                {
                    provide: pinoLoggerInjectionToken,
                    inject: [CmsLoggerConfig],
                    useFactory: ({
                        format,
                        pinoLogLevel,
                        lokiHost,
                        lokiEndpoint,
                        lokiPassword,
                        lokiUsername
                    }: CmsLoggerConfig): BaseLogger => {
                        const pinoOptions: LoggerOptions = {
                            base: {
                                app
                            },
                            level: pinoLogLevel
                        };

                        const pinoTargets: Array<Record<string, unknown>> = [];

                        const usePinoPretty = format === LoggerFormat.Pretty;
                        if (usePinoPretty) {
                            pinoTargets.push({
                                target: 'pino-pretty',
                                options: {
                                    singleLine: true
                                } as PrettyOptions
                            });
                        } else {
                            pinoTargets.push({
                                target: 'pino/file',
                                // eslint-disable-next-line spellcheck/spell-checker
                                // 1 represents stdout/console
                                options: { destination: 1 } as LoggerOptions
                            });
                        }

                        if (lokiHost) {
                            const lokiBasicAuth =
                                lokiUsername && lokiPassword ? { username: lokiUsername, password: lokiPassword } : undefined;

                            pinoTargets.push({
                                target: 'pino-loki',
                                options: {
                                    batching: true,
                                    // The interval at which batched logs are sent in seconds
                                    interval: 5,

                                    host: lokiHost,
                                    basicAuth: lokiBasicAuth,
                                    endpoint: lokiEndpoint,
                                    propsToLabels: [
                                        'app',
                                        'requestId',
                                        'correlationId',
                                        'customerId',
                                        'requestMethod',
                                        'requestPath',
                                        'username',
                                        'statusCode',
                                        'duration',
                                        'deviceId',
                                        'machineId',
                                        'transactionPointId',
                                        'context',
                                        'ip',
                                        'query',
                                        'command',
                                        'eventId',
                                        'event',
                                        'eventHandlerName'
                                    ]
                                } as LokiOptions
                            });
                        }

                        pinoOptions.transport = {
                            // @ts-expect-error targets is readonly
                            targets: pinoTargets.map((target) => ({
                                ...target,
                                level: pinoLogLevel
                            }))
                        };

                        return pino(pinoOptions);
                    }
                },
                CmsLogger
            ],
            exports: [CmsLogger],
            global: true
        };
    }

    static logConfigProvider(): DynamicModule {
        return {
            module: CmsLoggerModule,
            providers: [
                {
                    provide: CmsLoggerConfig,
                    inject: [ConfigService],
                    useFactory: (configService: ConfigService): CmsLoggerConfig => {
                        const loggingConfig = CmsLoggerConfig.create({
                            /* eslint-disable @typescript-eslint/no-non-null-assertion */
                            format: configService.get<LoggerFormat>('LOG_FORMAT')!,
                            logLevel: configService.get<LogLevel>('LOG_LEVEL')!,
                            lokiEndpoint: configService.get<string>('LOKI_ENDPOINT'),
                            lokiHost: configService.get<string>('LOKI_HOST'),
                            lokiPassword: configService.get<string>('LOKI_PASSWORD'),
                            lokiUsername: configService.get<string>('LOKI_USERNAME')
                            /* eslint-enable @typescript-eslint/no-non-null-assertion */
                        });

                        return loggingConfig;
                    }
                }
            ],
            exports: [CmsLoggerConfig]
        };
    }
}
