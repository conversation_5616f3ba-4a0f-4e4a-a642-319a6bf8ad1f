import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import {
    BaseCommand,
    BaseQuery,
    CommandResult,
    InternalServerError,
    NewableOrStaticCreateOrMap,
    QueryResult
} from '@tronius/shared-common';
import { Channel, Message } from 'amqplib';
import EventEmitter2 from 'eventemitter2';

import {
    rabbitMqInternalEventEmitterToken,
    rabbitMqReplyQueueToken,
    rabbitMqRpcHandlerChannelToken,
    rabbitMqRpcReplyChannelToken
} from './constants';
import { MessageParsingService } from './message-parsing.service';
import { QueueNameProvider } from './queue-name.provider';
import { RabbitMqConfig } from './rabbit-mq.config';

@Injectable()
export class RabbitMqRpcClient implements OnModuleInit {
    private readonly logger = new Logger(RabbitMqRpcClient.name);

    private readonly timeoutLookup = new Map<string, NodeJS.Timeout>();

    constructor(
        @Inject(rabbitMqRpcReplyChannelToken) private readonly rabbitMqRpcReplyChannel: Channel,
        @Inject(rabbitMqReplyQueueToken) private readonly replyQueue: string,
        @Inject(rabbitMqRpcHandlerChannelToken)
        private readonly handlerChannel: Channel,

        @Inject(rabbitMqInternalEventEmitterToken)
        private readonly internalEventEmitter: EventEmitter2,
        private readonly messageParsingService: MessageParsingService,
        private readonly config: RabbitMqConfig,
        private readonly queueNameProvider: QueueNameProvider
    ) {}

    async onModuleInit(): Promise<void> {
        await this.registerReplyQueueConsumer();
    }

    async callProcedure<T extends BaseCommand | BaseQuery, R extends CommandResult | QueryResult>(
        payloadInstance: T,
        replyPayloadClassConstructor: NewableOrStaticCreateOrMap<R>
    ): Promise<R> {
        const handlerQueueName = this.queueNameProvider.getQueueName(payloadInstance.constructor.name);
        const startTime = Date.now();
        const { correlationId } = payloadInstance;

        const [messageBuffer] = this.messageParsingService.serializeMessage(payloadInstance);

        const remoteProcedureResult = await new Promise<R>((resultCallback, errorCallback) => {
            this.subscribeToReply(correlationId, handlerQueueName, replyPayloadClassConstructor, resultCallback, errorCallback);

            this.handlerChannel.sendToQueue(handlerQueueName, messageBuffer, {
                correlationId,
                replyTo: this.replyQueue
            });
        });
        const endTime = Date.now();
        const duration = endTime - startTime;

        this.logger.debug(`"RPC":"${handlerQueueName}" "duration":"${duration}ms"`);

        return remoteProcedureResult;
    }

    private subscribeToReply<R extends CommandResult | QueryResult>(
        correlationId: string,
        handlerName: string,
        replyPayloadClassConstructor: NewableOrStaticCreateOrMap<R>,
        resultCallback: (value: R) => void,
        errorCallback: (error: unknown) => void
    ): void {
        const replyMessageEventHandler = (message: Message): void => {
            try {
                this.clearRpcTimeout(correlationId);
                const response = this.messageParsingService.deserializeMessage(message, replyPayloadClassConstructor);

                if (response instanceof Error) {
                    errorCallback(response);
                    return;
                }

                resultCallback(response);
            } catch (error) {
                errorCallback(error);
            }
        };

        this.setRpcTimeout(correlationId, handlerName, errorCallback, replyMessageEventHandler);

        this.internalEventEmitter.once(correlationId, replyMessageEventHandler);
    }

    private setRpcTimeout(
        correlationId: string,
        handlerName: string,
        errorCallback: (error: unknown) => void,
        replyMessageEventHandler: (message: Message) => void
    ): void {
        const rpcTimeout = setTimeout(() => {
            const timeoutErrorMessage = `"RPC":"${handlerName}" "status":"timed out"`;
            this.logger.error(timeoutErrorMessage);

            this.timeoutLookup.delete(correlationId);
            this.internalEventEmitter.off(correlationId, replyMessageEventHandler);

            errorCallback(new InternalServerError({ message: timeoutErrorMessage }));
        }, this.config.rpcTimeoutInMs);

        this.timeoutLookup.set(correlationId, rpcTimeout);
    }

    private clearRpcTimeout(correlationId: string): void {
        const timeout = this.timeoutLookup.get(correlationId);
        if (!timeout) {
            return;
        }

        clearTimeout(timeout);
        this.timeoutLookup.delete(correlationId);
    }

    private async registerReplyQueueConsumer(): Promise<void> {
        await this.rabbitMqRpcReplyChannel.consume(
            this.replyQueue,
            (message: Message | null) => {
                if (!message) {
                    return;
                }

                const {
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    properties: { correlationId }
                } = message;

                this.internalEventEmitter.emit(correlationId as string, message);
                this.rabbitMqRpcReplyChannel.ack(message);
            },
            { noAck: false }
        );
    }
}
