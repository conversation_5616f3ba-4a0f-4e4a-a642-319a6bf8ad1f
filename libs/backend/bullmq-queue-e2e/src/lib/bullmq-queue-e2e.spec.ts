import { BullQueueProviderAdapter, BullQueuesMap, BullmqQueueModule, bullQueuesMapInjectionToken } from '@cms/bullmq-queue';
import { InjectQueueService, JobQueueModule, JobState, ProcessJob, QueueService, Worker } from '@cms/job-queue';
import { deserialize } from '@cms/serialization';
import { Injectable, Module, ModuleMetadata } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { Job, UUIDFactory, waitFor } from '@tronius/shared-common';
import { IsUUID } from 'class-validator';

class TestJob extends Job {
    @IsUUID()
    readonly randomPayload: string = UUIDFactory.create();

    static create(): TestJob {
        return deserialize(TestJob, {});
    }
}

@Injectable()
@ProcessJob(TestJob)
class TestJobWorker implements Worker<TestJob> {
    processedJobs: TestJob[] = [];

    process(job: TestJob): Promise<void> | void {
        this.processedJobs.push(job);
    }
}

@Injectable()
class TestQueueService {
    constructor(@InjectQueueService(TestJob) private readonly queueService: QueueService<TestJob>) {}

    async addJob(job: TestJob): Promise<void> {
        await this.queueService.addJob(job);
    }

    async getJobState(jobId: string): Promise<JobState | null> {
        return this.queueService.getJobStateById(jobId);
    }
}

@Module({
    providers: [TestQueueService],
    imports: [JobQueueModule.forJob(TestJob)]
})
class TestJobQueueModule {}

@Module({
    imports: [JobQueueModule.forJob(TestJob)],
    providers: [TestJobWorker]
})
class JobWorkerModule {}

describe('Bull queue e2e', () => {
    let firstModule: TestingModule;
    let secondModule: TestingModule;

    beforeAll(async () => {
        const queuePrefix = UUIDFactory.create();

        const commonImports: ModuleMetadata['imports'] = [
            ConfigModule.forRoot({ isGlobal: true }),
            JobQueueModule.forRootAsync({
                // We're using two separate modules, so the queue destruction has to be done manually
                imports: [BullmqQueueModule.forE2E(queuePrefix, false)],
                inject: [BullQueueProviderAdapter],
                useFactory: (bullQueueProvider: BullQueueProviderAdapter) => bullQueueProvider
            })
        ];

        firstModule = await Test.createTestingModule({
            imports: [...commonImports, TestJobQueueModule]
        }).compile();

        await firstModule.init();

        secondModule = await Test.createTestingModule({
            imports: [...commonImports, JobWorkerModule]
        }).compile();

        await secondModule.init();
    });

    it('should add and process a job', async () => {
        const testJob = TestJob.create();

        const testJobWorkerInstance = secondModule.get(TestJobWorker);

        const queueService = firstModule.get(TestQueueService);

        const jobStateBeforeInvocation = await queueService.getJobState(testJob.jobId);
        expect(jobStateBeforeInvocation).toBeNull();

        await queueService.addJob(testJob);

        await waitFor(3_000);

        const [processedJob] = testJobWorkerInstance.processedJobs;

        expect(processedJob).toBeInstanceOf(TestJob);
        expect(processedJob.jobId).toBe(testJob.jobId);
        expect(processedJob.randomPayload).toBe(testJob.randomPayload);

        const jobStateAfterInvocation = await queueService.getJobState(testJob.jobId);
        expect(jobStateAfterInvocation).toBe(JobState.Completed);
    });

    afterAll(async () => {
        const bullQueuesMap: BullQueuesMap = firstModule.get(bullQueuesMapInjectionToken);
        const queues = Array.from(bullQueuesMap.values());

        const obliteratePromises = queues.map(async (queue) => queue.obliterate({ force: true }));

        await Promise.all(obliteratePromises);

        await Promise.all([firstModule.close(), secondModule.close()]);
    });
});
