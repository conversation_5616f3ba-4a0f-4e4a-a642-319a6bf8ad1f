import { MigrationInterface, QueryRunner } from 'typeorm';

export class CorrectMachineHandpays1748264114147 implements MigrationInterface {
    name = 'CorrectMachineHandpays1748264114147';

    async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "handpay_operation" ADD "created_at" TIMESTAMP');
        await queryRunner.query('UPDATE "handpay_operation" SET "created_at" = "triggered_at"');
        await queryRunner.query('ALTER TABLE "handpay_operation" ALTER COLUMN "created_at" SET NOT NULL');

        await queryRunner.query('CREATE INDEX "idx_handpay_operation_created_at" ON "handpay_operation" ("created_at") ');
    }

    async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP INDEX "public"."idx_handpay_operation_created_at"');
        await queryRunner.query('ALTER TABLE "handpay_operation" DROP COLUMN "created_at"');
    }
}
