import { MigrationInterface, QueryRunner } from 'typeorm';

export class MachineFundsTransferCreatedAt1748260011830 implements MigrationInterface {
    name = 'MachineFundsTransferCreatedAt1748260011830';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add created_at column as nullable first
        await queryRunner.query(`ALTER TABLE "machine_funds_transfer_operation" ADD "created_at" TIMESTAMP`);

        // Set created_at to the current triggered_at value for all existing records
        await queryRunner.query(`UPDATE "machine_funds_transfer_operation" SET "created_at" = "triggered_at"`);

        // Now make created_at NOT NULL
        await queryRunner.query(`ALTER TABLE "machine_funds_transfer_operation" ALTER COLUMN "created_at" SET NOT NULL`);

        await queryRunner.query(`ALTER TABLE "machine_funds_transfer_operation" ALTER COLUMN "triggered_at" DROP DEFAULT`);
        await queryRunner.query(
            `CREATE INDEX "idx_machine_funds_transfer_operation_created_at" ON "machine_funds_transfer_operation" ("created_at") `
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."idx_machine_funds_transfer_operation_created_at"`);
        await queryRunner.query(`ALTER TABLE "machine_funds_transfer_operation" ALTER COLUMN "triggered_at" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "machine_funds_transfer_operation" DROP COLUMN "created_at"`);
    }
}
