import { MachineFundsTransferOperation, MachineFundsTransferOperationType } from '@cms/machine-funds-transfers';
import { dateColumnOptions } from '@tronius/backend-common';
import { TriggerType } from '@tronius/shared-common';
import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

import { MachineFundsTransferEntity } from './machine-funds-transfer.entity';
import { CasinoPropertyEntity } from '../casino/casino-property.entity';

@Entity({ name: 'machine_funds_transfer_operation', orderBy: { triggeredAt: 'DESC' } })
export class MachineFundsTransferOperationEntity implements MachineFundsTransferOperation {
    @Column({ nullable: true })
    @Index()
    propertyId?: string;
    @ManyToOne(() => CasinoPropertyEntity, {
        onDelete: 'CASCADE',
        nullable: true
    })
    @JoinColumn([
        {
            name: 'property_id',
            referencedColumnName: 'id',
            foreignKeyConstraintName: 'fk_mach_funds_tr_operation_property_id_casino_property_id'
        }
    ])
    property?: CasinoPropertyEntity;

    @PrimaryGeneratedColumn('uuid')
    id!: string;

    @Column({ type: 'varchar', enum: MachineFundsTransferOperationType })
    @Index()
    type!: MachineFundsTransferOperationType;

    @Column()
    @Index()
    triggeredById!: string;

    @Column({ type: 'varchar', enum: TriggerType })
    @Index()
    triggeredByType!: TriggerType;

    @Column(dateColumnOptions)
    @Index()
    triggeredAt!: number;

    @Column({ type: 'date' })
    @Index()
    gamingDay!: string;

    @Column({ nullable: true })
    comment?: string;

    @Column()
    @Index()
    machineFundsTransferId!: string;

    @Column(dateColumnOptions)
    @Index()
    createdAt!: number;

    @ManyToOne(() => MachineFundsTransferEntity, (ticket) => ticket.operations, {
        onDelete: 'CASCADE',
        nullable: false
    })
    @JoinColumn([
        {
            name: 'machine_funds_transfer_id',
            referencedColumnName: 'id',
            foreignKeyConstraintName: 'fk_mach_funds_tr_operation_mach_funds_tr_id_mach_funds_tr_id'
        }
    ])
    machineFundsTransfer!: MachineFundsTransferEntity;
}
