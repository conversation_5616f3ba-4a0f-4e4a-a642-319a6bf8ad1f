import { HandpayOperation, HandpayOperationType } from '@cms/machine-handpays';
import { dateColumnOptions } from '@tronius/backend-common';
import { TriggerType } from '@tronius/shared-common';
import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

import { HandpayEntity } from './handpay.entity';
import { CasinoPropertyEntity } from '../casino/casino-property.entity';

@Entity({ name: 'handpay_operation', orderBy: { triggeredAt: 'DESC' } })
export class HandpayOperationEntity implements HandpayOperation {
    @Column({ nullable: true })
    @Index()
    propertyId?: string;

    @ManyToOne(() => CasinoPropertyEntity, {
        onDelete: 'CASCADE',
        nullable: true
    })
    @JoinColumn([{ name: 'property_id', referencedColumnName: 'id' }])
    property?: CasinoPropertyEntity;

    @PrimaryGeneratedColumn('uuid')
    id!: string;

    @Column({ type: 'varchar', enum: HandpayOperationType })
    @Index()
    type!: HandpayOperationType;

    @Column()
    @Index()
    triggeredById!: string;

    @Column({ type: 'varchar', enum: TriggerType })
    @Index()
    triggeredByType!: TriggerType.Machine | TriggerType.TransactionPoint;

    @Column(dateColumnOptions)
    @Index()
    triggeredAt!: number;

    @Column(dateColumnOptions)
    @Index()
    createdAt!: number;

    @Column({ type: 'date' })
    @Index()
    gamingDay!: string;

    @Column({ type: 'varchar' })
    createdBy!: string;

    @Column({ nullable: true })
    comment?: string;

    @Column()
    @Index()
    handpayId!: string;

    @ManyToOne(() => HandpayEntity, (ticket) => ticket.operations, {
        onDelete: 'CASCADE',
        nullable: false
    })
    @JoinColumn([{ name: 'handpay_id', referencedColumnName: 'id' }])
    handpay?: HandpayEntity;
}
