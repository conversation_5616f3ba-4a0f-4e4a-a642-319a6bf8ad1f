import { MachineDailyMetricsRepositoryPort } from '@cms/machine-daily-metrics-domain';
import { MachineDailyMetrics, MachineMetricExtraDependency } from '@cms/machine-metrics';
import { Test, TestingModule } from '@nestjs/testing';
import { UUIDFactory } from '@tronius/shared-common';
import { OperationalStatus, SasMeterCode } from '@tronius/shared-domain';
import { EntityManager } from 'typeorm';

import { CmsDatabaseModule } from '../cms-database.module';
import { MachineDailyMetricsOrmEntity } from '../entities/machine-metrics/machine-daily-metrics.orm.entity';

describe('MachineDailyMetricsRepositoryAdapter (e2e)', () => {
    let testModule: TestingModule;
    let adapter: MachineDailyMetricsRepositoryPort;
    let entityManager: EntityManager;

    beforeAll(async () => {
        const testModuleBuilder = Test.createTestingModule({
            imports: [CmsDatabaseModule.forRootAsync()]
        });

        testModule = await testModuleBuilder.compile();
        await testModule.init();

        adapter = testModule.get(MachineDailyMetricsRepositoryPort);
        entityManager = testModule.get(EntityManager);
    });

    beforeEach(async () => {
        // Clear the machine_daily_metrics table before each test
        await entityManager.query('DELETE FROM machine_daily_metrics');
    });

    const createMetrics = (
        machineId: string,
        date: Date,
        operationalStatus: OperationalStatus,
        startMetersCollectedAt: Date,
        endMetersCollectedAt: Date
    ): MachineDailyMetrics => {
        // Format date as YYYY-MM-DD for gamingDay
        const [gamingDay] = date.toISOString().split('T');

        return MachineDailyMetrics.create({
            machineId,
            gamingDay,
            machineLocation: 'Test Location',
            machineOperationalStatus: operationalStatus,
            startMeters: {
                [SasMeterCode.SAS0000]: 1000,
                [SasMeterCode.SAS0001]: 800
            },
            startMetersCollectedAt,
            endMeters: {
                [SasMeterCode.SAS0000]: 1500,
                [SasMeterCode.SAS0001]: 1200
            },
            endMetersCollectedAt,
            extraDependencies: {
                [MachineMetricExtraDependency.Denomination]: 1
            },
            generatedAt: new Date()
        });
    };

    describe('list', () => {
        it('should return machine daily metrics within the specified gaming day range', async () => {
            // Arrange
            const machineId = UUIDFactory.create();

            // Create metrics for 3 days ago
            const threeDaysAgo = new Date('2025-05-19T12:00:00Z');

            // Create metrics for 2 days ago
            const twoDaysAgo = new Date('2025-05-20T12:00:00Z');

            // Create metrics for 1 day ago
            const oneDayAgo = new Date('2025-05-21T12:00:00Z');

            // Create and save metrics
            const metricsThreeDaysAgo = createMetrics(
                machineId,
                threeDaysAgo,
                OperationalStatus.Live,
                new Date(threeDaysAgo.getTime() - 10 * 60 * 1000), // 10 minutes before
                new Date(threeDaysAgo.getTime() + 10 * 60 * 1000) // 10 minutes after
            );

            const metricsTwoDaysAgo = createMetrics(
                machineId,
                twoDaysAgo,
                OperationalStatus.Live,
                new Date(twoDaysAgo.getTime() - 10 * 60 * 1000),
                new Date(twoDaysAgo.getTime() + 10 * 60 * 1000)
            );

            const metricsOneDayAgo = createMetrics(
                machineId,
                oneDayAgo,
                OperationalStatus.Live,
                new Date(oneDayAgo.getTime() - 10 * 60 * 1000),
                new Date(oneDayAgo.getTime() + 10 * 60 * 1000)
            );

            // Save metrics to database
            await entityManager.save(MachineDailyMetricsOrmEntity, [metricsThreeDaysAgo, metricsTwoDaysAgo, metricsOneDayAgo]);

            // Act - List metrics between 2 days ago and 1 day ago (should return 2 records)
            const fromGamingDay = '2025-05-20'; // Two days ago
            const toGamingDay = '2025-05-21'; // One day ago
            const result = await adapter.list({ fromGamingDay, toGamingDay });

            // Assert
            expect(result).toHaveLength(2);

            // Verify the correct metrics were returned (two days ago and one day ago)
            const resultGamingDays = result.map((metric) => metric.gamingDay).sort();
            expect(resultGamingDays).toContain('2025-05-20');
            expect(resultGamingDays).toContain('2025-05-21');
            expect(resultGamingDays).not.toContain('2025-05-19');

            // Verify the metrics have the correct machine ID
            result.forEach((metric) => {
                expect(metric.machineId).toBe(machineId);
            });
        });

        it('should return an empty array when no metrics exist in the specified gaming day range', async () => {
            // Arrange
            const machineId = UUIDFactory.create();

            // Create a metric for 5 days ago
            const fiveDaysAgo = new Date('2025-05-17T12:00:00Z');

            const metricsFiveDaysAgo = createMetrics(
                machineId,
                fiveDaysAgo,
                OperationalStatus.Live,
                new Date(fiveDaysAgo.getTime() - 10 * 60 * 1000),
                new Date(fiveDaysAgo.getTime() + 10 * 60 * 1000)
            );

            // Save metric to database
            await entityManager.save(MachineDailyMetricsOrmEntity, metricsFiveDaysAgo);

            // Act - List metrics between 3 days ago and 1 day ago (should return 0 records)
            const fromGamingDay = '2025-05-19'; // Three days ago
            const toGamingDay = '2025-05-21'; // One day ago
            const result = await adapter.list({ fromGamingDay, toGamingDay });

            // Assert
            expect(result).toHaveLength(0);
        });
    });

    describe('getLatestBeforeGamingDay', () => {
        it('should return the latest metrics before a specific gaming day based on endMetersCollectedAt and startMetersCollectedAt', async () => {
            // Arrange
            const machineId = UUIDFactory.create();
            const now = new Date('2025-05-22T12:00:00Z'); // Use a fixed date to avoid conflicts

            // Create metrics for 3 days ago
            const threeDaysAgo = new Date('2025-05-19T12:00:00Z');

            // Create metrics for 2 days ago
            const twoDaysAgo = new Date('2025-05-20T12:00:00Z');

            // Create metrics for 1 day ago
            const oneDayAgo = new Date('2025-05-21T12:00:00Z');

            // Create metrics with different endMetersCollectedAt times
            const metricsThreeDaysAgo = createMetrics(
                machineId,
                threeDaysAgo,
                OperationalStatus.Live,
                new Date(threeDaysAgo.getTime() - 60 * 60 * 1000), // 1 hour before
                new Date(threeDaysAgo.getTime() + 55 * 60 * 1000) // 55 minutes after
            );

            const metricsTwoDaysAgo = createMetrics(
                machineId,
                twoDaysAgo,
                OperationalStatus.Live,
                new Date(twoDaysAgo.getTime() - 60 * 60 * 1000), // 1 hour before
                new Date(twoDaysAgo.getTime() + 59 * 60 * 1000) // 59 minutes after
            );

            const metricsOneDayAgo = createMetrics(
                machineId,
                oneDayAgo,
                OperationalStatus.Live,
                new Date(oneDayAgo.getTime() - 60 * 60 * 1000), // 1 hour before
                new Date(oneDayAgo.getTime() + 50 * 60 * 1000) // 50 minutes after
            );

            // Insert the metrics
            await entityManager.insert(MachineDailyMetricsOrmEntity, metricsThreeDaysAgo);
            await entityManager.insert(MachineDailyMetricsOrmEntity, metricsTwoDaysAgo);
            await entityManager.insert(MachineDailyMetricsOrmEntity, metricsOneDayAgo);

            // Format now as YYYY-MM-DD for gamingDay
            const [nowGamingDay] = now.toISOString().split('T'); // '2025-05-22'

            // Act
            const result = await adapter.getLatestBeforeGamingDay(machineId, nowGamingDay);

            // Assert
            expect(result).toBeDefined();
            expect(result?.machineId).toBe(machineId);
            // The implementation returns the entry with the latest gamingDay before nowGamingDay
            expect(result?.gamingDay).toEqual(oneDayAgo.toISOString().split('T')[0]); // '2025-05-21'
            expect(result?.endMetersCollectedAt).toEqual(new Date(oneDayAgo.getTime() + 50 * 60 * 1000));
        });

        it('should return null when no metrics exist before the specified gaming day', async () => {
            // Arrange
            const machineId = UUIDFactory.create();
            const now = new Date();
            const [nowGamingDay] = now.toISOString().split('T');

            // Act
            const result = await adapter.getLatestBeforeGamingDay(machineId, nowGamingDay);

            // Assert
            expect(result).toBeNull();
        });

        it('should handle multiple machines and return the correct metrics for each', async () => {
            // Arrange
            const machineId1 = UUIDFactory.create();
            const machineId2 = UUIDFactory.create();
            const now = new Date('2025-05-22T12:00:00Z'); // Use a fixed date to avoid conflicts

            // Create metrics for 2 days ago for machine 1
            const twoDaysAgo = new Date('2025-05-20T12:00:00Z');

            // Create metrics for 1 day ago for machine 2
            const oneDayAgo = new Date('2025-05-21T12:00:00Z');

            const metricsMachine1 = createMetrics(
                machineId1,
                twoDaysAgo,
                OperationalStatus.Live,
                new Date(twoDaysAgo.getTime() - 60 * 60 * 1000),
                new Date(twoDaysAgo.getTime() + 59 * 60 * 1000)
            );

            const metricsMachine2 = createMetrics(
                machineId2,
                oneDayAgo,
                OperationalStatus.Live,
                new Date(oneDayAgo.getTime() - 60 * 60 * 1000),
                new Date(oneDayAgo.getTime() + 50 * 60 * 1000)
            );

            // Insert the metrics
            await entityManager.insert(MachineDailyMetricsOrmEntity, metricsMachine1);
            await entityManager.insert(MachineDailyMetricsOrmEntity, metricsMachine2);

            // Format now as YYYY-MM-DD for gamingDay
            const [nowGamingDay] = now.toISOString().split('T'); // '2025-05-22'

            // Act
            const resultMachine1 = await adapter.getLatestBeforeGamingDay(machineId1, nowGamingDay);
            const resultMachine2 = await adapter.getLatestBeforeGamingDay(machineId2, nowGamingDay);

            // Assert
            expect(resultMachine1).toBeDefined();
            expect(resultMachine1?.machineId).toBe(machineId1);
            expect(resultMachine1?.gamingDay).toEqual(twoDaysAgo.toISOString().split('T')[0]); // '2025-05-20'

            expect(resultMachine2).toBeDefined();
            expect(resultMachine2?.machineId).toBe(machineId2);
            expect(resultMachine2?.gamingDay).toEqual(oneDayAgo.toISOString().split('T')[0]); // '2025-05-21'
        });
    });

    describe('getByMachineIdAndGamingDay', () => {
        it('should return metrics grouped by operational status', async () => {
            // Arrange
            const machineId = UUIDFactory.create();
            const now = new Date();
            const startOfHour = new Date(now);
            startOfHour.setMinutes(0, 0, 0);

            // Create metrics for the same hour but different operational statuses
            const liveMetrics = createMetrics(
                machineId,
                startOfHour,
                OperationalStatus.Live,
                new Date(startOfHour.getTime() - 60 * 60 * 1000),
                new Date(startOfHour.getTime() + 50 * 60 * 1000)
            );

            const testingMetrics = createMetrics(
                machineId,
                startOfHour,
                OperationalStatus.Testing,
                new Date(startOfHour.getTime() - 60 * 60 * 1000),
                new Date(startOfHour.getTime() + 45 * 60 * 1000)
            );

            // Insert the metrics
            await entityManager.insert(MachineDailyMetricsOrmEntity, liveMetrics);
            await entityManager.insert(MachineDailyMetricsOrmEntity, testingMetrics);

            // Format startOfHour as YYYY-MM-DD for gamingDay
            const [gamingDay] = startOfHour.toISOString().split('T');

            // Act
            const result = await adapter.getByMachineIdAndGamingDay(machineId, gamingDay);

            // Assert
            expect(result).toBeDefined();
            expect(Object.keys(result).length).toBe(2);
            expect(result[OperationalStatus.Live]).toBeDefined();
            expect(result[OperationalStatus.Live].machineId).toBe(machineId);
            expect(result[OperationalStatus.Live].machineOperationalStatus).toBe(OperationalStatus.Live);
            expect(result[OperationalStatus.Live].gamingDay).toEqual(gamingDay);

            expect(result[OperationalStatus.Testing]).toBeDefined();
            expect(result[OperationalStatus.Testing].machineId).toBe(machineId);
            expect(result[OperationalStatus.Testing].machineOperationalStatus).toBe(OperationalStatus.Testing);
            expect(result[OperationalStatus.Testing].gamingDay).toEqual(gamingDay);
        });

        it('should return empty object when no metrics exist for the specified gaming day', async () => {
            // Arrange
            const machineId = UUIDFactory.create();
            const now = new Date();
            const [gamingDay] = now.toISOString().split('T');

            // Act
            const result = await adapter.getByMachineIdAndGamingDay(machineId, gamingDay);

            // Assert
            expect(result).toBeDefined();
            expect(Object.keys(result).length).toBe(0);
        });
    });

    describe('getByMachineIdGamingDayAndOperationalStatus', () => {
        it('should return metrics for specific machine, gaming day, and operational status', async () => {
            // Arrange
            const machineId = UUIDFactory.create();
            const now = new Date();
            const startOfHour = new Date(now);
            startOfHour.setMinutes(0, 0, 0);

            // Create metrics for the same hour but different operational statuses
            const liveMetrics = createMetrics(
                machineId,
                startOfHour,
                OperationalStatus.Live,
                new Date(startOfHour.getTime() - 60 * 60 * 1000),
                new Date(startOfHour.getTime() + 50 * 60 * 1000)
            );

            const testingMetrics = createMetrics(
                machineId,
                startOfHour,
                OperationalStatus.Testing,
                new Date(startOfHour.getTime() - 60 * 60 * 1000),
                new Date(startOfHour.getTime() + 45 * 60 * 1000)
            );

            // Insert the metrics
            await entityManager.insert(MachineDailyMetricsOrmEntity, liveMetrics);
            await entityManager.insert(MachineDailyMetricsOrmEntity, testingMetrics);

            // Format startOfHour as YYYY-MM-DD for gamingDay
            const [gamingDay] = startOfHour.toISOString().split('T');

            // Act
            const resultLive = await adapter.getByMachineIdGamingDayAndOperationalStatus(machineId, gamingDay, OperationalStatus.Live);
            const resultTesting = await adapter.getByMachineIdGamingDayAndOperationalStatus(
                machineId,
                gamingDay,
                OperationalStatus.Testing
            );

            // Assert
            expect(resultLive).toBeDefined();
            expect(resultLive?.machineId).toBe(machineId);
            expect(resultLive?.machineOperationalStatus).toBe(OperationalStatus.Live);
            expect(resultLive?.gamingDay).toEqual(gamingDay);

            expect(resultTesting).toBeDefined();
            expect(resultTesting?.machineId).toBe(machineId);
            expect(resultTesting?.machineOperationalStatus).toBe(OperationalStatus.Testing);
            expect(resultTesting?.gamingDay).toEqual(gamingDay);
        });

        it('should return null when no metrics exist for the specified machine, gaming day, and operational status', async () => {
            // Arrange
            const machineId = UUIDFactory.create();
            const now = new Date();
            const [gamingDay] = now.toISOString().split('T');

            // Act
            const result = await adapter.getByMachineIdGamingDayAndOperationalStatus(machineId, gamingDay, OperationalStatus.Live);

            // Assert
            expect(result).toBeNull();
        });
    });

    describe('upsert', () => {
        it('should insert new metrics when they do not exist', async () => {
            // Arrange
            const machineId = UUIDFactory.create();
            const now = new Date();
            const startOfHour = new Date(now);
            startOfHour.setMinutes(0, 0, 0);

            const metrics = createMetrics(
                machineId,
                startOfHour,
                OperationalStatus.Live,
                new Date(startOfHour.getTime() - 60 * 60 * 1000),
                new Date(startOfHour.getTime() + 50 * 60 * 1000)
            );

            // Act
            await adapter.upsert(metrics);

            // Format startOfHour as YYYY-MM-DD for gamingDay
            const [gamingDay] = startOfHour.toISOString().split('T');

            // Assert
            const result = await adapter.getByMachineIdGamingDayAndOperationalStatus(machineId, gamingDay, OperationalStatus.Live);
            expect(result).toBeDefined();
            expect(result?.machineId).toBe(machineId);
            expect(result?.gamingDay).toEqual(gamingDay);
            expect(result?.machineOperationalStatus).toBe(OperationalStatus.Live);
        });

        it('should update existing metrics when they already exist', async () => {
            // Arrange
            const machineId = UUIDFactory.create();
            const now = new Date();
            const startOfHour = new Date(now);
            startOfHour.setMinutes(0, 0, 0);

            // Create initial metrics
            const initialMetrics = createMetrics(
                machineId,
                startOfHour,
                OperationalStatus.Live,
                new Date(startOfHour.getTime() - 60 * 60 * 1000),
                new Date(startOfHour.getTime() + 50 * 60 * 1000)
            );

            // Insert initial metrics
            await entityManager.insert(MachineDailyMetricsOrmEntity, initialMetrics);

            // Format startOfHour as YYYY-MM-DD for gamingDay
            const [gamingDay] = startOfHour.toISOString().split('T');

            // Create updated metrics with different values
            const updatedMetrics = MachineDailyMetrics.create({
                machineId,
                gamingDay,
                machineLocation: 'Updated Location',
                machineOperationalStatus: OperationalStatus.Live,
                startMeters: {
                    [SasMeterCode.SAS0000]: 2000,
                    [SasMeterCode.SAS0001]: 1600
                },
                startMetersCollectedAt: new Date(startOfHour.getTime() - 30 * 60 * 1000),
                endMeters: {
                    [SasMeterCode.SAS0000]: 3000,
                    [SasMeterCode.SAS0001]: 2400
                },
                endMetersCollectedAt: new Date(startOfHour.getTime() + 55 * 60 * 1000),
                extraDependencies: {
                    [MachineMetricExtraDependency.Denomination]: 1
                },
                generatedAt: new Date()
            });

            // Act
            await adapter.upsert(updatedMetrics);

            // Assert
            const result = await adapter.getByMachineIdGamingDayAndOperationalStatus(machineId, gamingDay, OperationalStatus.Live);
            expect(result).toBeDefined();
            expect(result?.machineId).toBe(machineId);
            expect(result?.machineLocation).toBe('Updated Location');
            expect(result?.startMeters[SasMeterCode.SAS0000]).toBe(2000);
            expect(result?.endMeters[SasMeterCode.SAS0000]).toBe(3000);
            expect(result?.startMetersCollectedAt).toEqual(new Date(startOfHour.getTime() - 30 * 60 * 1000));
            expect(result?.endMetersCollectedAt).toEqual(new Date(startOfHour.getTime() + 55 * 60 * 1000));
            expect(result?.extraDependencies[MachineMetricExtraDependency.Denomination]).toBe(1);
        });
    });

    afterAll(async () => {
        await testModule.close();
    });
});
