import { MachineDailyMetricsRepositoryPort } from '@cms/machine-daily-metrics-domain';
import { ListDailyMetricsProps, MachineDailyMetrics } from '@cms/machine-metrics';
import { InjectRepository } from '@cms/typeorm';
import { Injectable } from '@nestjs/common';
import { OperationalStatus } from '@tronius/shared-domain';
import { Between, LessThan, Repository } from 'typeorm';

import { MachineDailyMetricsOrmEntity } from '../entities/machine-metrics/machine-daily-metrics.orm.entity';

/**
 * Repository adapter for machine daily metrics
 */
@Injectable()
export class MachineDailyMetricsRepositoryAdapter implements MachineDailyMetricsRepositoryPort {
    constructor(
        @InjectRepository(MachineDailyMetricsOrmEntity)
        private readonly repository: Repository<MachineDailyMetricsOrmEntity>
    ) {}

    /**
     * List machine daily metrics entries based on the provided query
     * @param query The query parameters
     * @returns Array of MachineDailyMetrics entries
     */
    async list(query: ListDailyMetricsProps): Promise<MachineDailyMetrics[]> {
        const { fromGamingDay, toGamingDay } = query;
        const entities = await this.repository.find({
            where: {
                gamingDay: Between(fromGamingDay, toGamingDay)
            }
        });

        return entities.map((entity) => MachineDailyMetrics.create(entity));
    }

    /**
     * Get machine daily metrics entries by machine ID and gaming day, grouped by operational status
     * @param machineId The machine ID
     * @param gamingDay The gaming day in YYYY-MM-DD format
     * @returns Object with keys as OperationalStatus and values as MachineDailyMetrics
     */
    async getByMachineIdAndGamingDay(machineId: string, gamingDay: string): Promise<Record<OperationalStatus, MachineDailyMetrics>> {
        const entities = await this.repository.find({
            where: {
                machineId,
                gamingDay
            }
        });

        const result: Record<OperationalStatus, MachineDailyMetrics> = {} as Record<OperationalStatus, MachineDailyMetrics>;
        for (const entity of entities) {
            result[entity.machineOperationalStatus] = MachineDailyMetrics.create(entity);
        }
        return result;
    }

    /**
     * Get a specific machine daily metrics entry by machine ID, gaming day, and operational status
     * @param machineId The machine ID
     * @param gamingDay The gaming day in YYYY-MM-DD format
     * @param operationalStatus The operational status
     * @returns The machine daily metrics entry or null if not found
     */
    async getByMachineIdGamingDayAndOperationalStatus(
        machineId: string,
        gamingDay: string,
        operationalStatus: OperationalStatus
    ): Promise<MachineDailyMetrics | null> {
        const entity = await this.repository.findOne({
            where: {
                machineId,
                gamingDay,
                machineOperationalStatus: operationalStatus
            }
        });

        return entity ? MachineDailyMetrics.create(entity) : null;
    }

    /**
     * Get the latest machine daily metrics entry for a machine before a specific gaming day
     * This will return the entry with the greatest endMetersCollectedAt and startMetersCollectedAt
     * @param machineId The machine ID
     * @param beforeGamingDay The gaming day before which to find metrics
     * @returns The latest machine daily metrics entry or null if not found
     */
    async getLatestBeforeGamingDay(machineId: string, beforeGamingDay: string): Promise<MachineDailyMetrics | null> {
        // First, get all metrics for the machine with gamingDay < beforeGamingDay
        const entity = await this.repository.findOne({
            where: {
                machineId,
                gamingDay: LessThan(beforeGamingDay)
            },
            order: {
                // Order by endMetersCollectedAt and startMetersCollectedAt in descending order
                // to get the latest metrics first
                endMetersCollectedAt: 'DESC',
                startMetersCollectedAt: 'DESC'
            }
        });

        if (!entity) {
            return null;
        }

        return MachineDailyMetrics.create(entity);
    }

    /**
     * Upsert a new machine daily metrics entry
     * @param metrics The machine daily metrics to create
     */
    async upsert(metrics: MachineDailyMetrics): Promise<void> {
        await this.repository.upsert(metrics, {
            conflictPaths: ['machineId', 'gamingDay', 'machineOperationalStatus'],
            skipUpdateIfNoValuesChanged: true
        });
    }
}
