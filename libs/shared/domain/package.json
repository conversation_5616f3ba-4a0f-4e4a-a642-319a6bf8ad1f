{"name": "@tronius/shared-domain", "version": "0.0.24", "type": "commonjs", "main": "./src/index.js", "typings": "./src/index.d.ts", "dependencies": {"tslib": "^2.3.0"}, "devDependencies": {"reflect-metadata": "^0.2.1", "uuid": "^10.0.0"}, "peerDependencies": {"@cms/assets": "file:../../../features/assets/shared", "@cms/casino-operator": "file:../../../features/casino-operator/shared", "@cms/customers": "file:../../../features/customers/shared", "@cms/denominations": "file:../../../features/denominations/shared", "@cms/machine-funds-transfers": "file:../../../features/machine-funds-transfers/shared", "@cms/serialization": "file:../serialization", "@cms/validation": "file:../validation", "@tronius/shared-common": "^0.0.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "lodash": "^4.17.21"}, "publishConfig": {"registry": "http://repository.internal.troniusgaming.com/repository/npm-hosted/"}}