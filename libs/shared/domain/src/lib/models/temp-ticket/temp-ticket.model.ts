// TODO
// Currently printer and customer-transactions are importing from @cms/tickets which causing a circular dependency
// In order to now slow down development, we are copying the models here
// When they(or machine-models) are decoupled in their own shared libraries this should be deleted

import { BaseModel, TriggerType } from '@tronius/shared-common';

// Import { TgTicket, TicketPolicies } from '@cms/tickets';
export const ticketNumberLength = 18; // TODO: TicketPolicies.ticketNumberLength

export enum TgTicketType {
    Cash = 'CASH',
    PromoRestricted = 'PROMO_RESTRICTED',
    PromoUnrestricted = 'PROMO_UNRESTRICTED'
}

export enum TgTicketStatus {
    Created = 'CREATED',
    Valid = 'VALID',
    Pending = 'PENDING',
    Redeemed = 'REDEEMED', // Final
    Expired = 'EXPIRED', // Can be just replaced
    Voided = 'VOIDED', // Final
    Replaced = 'REPLACED', // Final
    Flagged = 'FLAGGED'
}

export enum TgTicketOperationType {
    Create = 'CREATE',
    Print = 'PRINT',
    Lock = 'LOCK',
    Redeem = 'REDEEM',
    Expire = 'EXPIRE',
    Void = 'VOID',
    ReplaceValid = 'REPLACE_VALID',
    ReplaceExpired = 'REPLACE_EXPIRED',
    Rejected = 'REJECTED'
}

export interface TgTicketOperation {
    id: string;
    type: TgTicketOperationType;
    triggeredByType: TriggerType.Machine | TriggerType.TransactionPoint; // Machine or TransactionPoint
    triggeredById: string;
    triggeredAt: number;
    comment?: string;

    createdBy: string; // Username
    gamingDay: string;
    manual: boolean;

    ticketId: string;
    ticket?: TgTicket;
}

export interface TgTicket extends BaseModel {
    type: TgTicketType;
    amount: number;
    status: TgTicketStatus;
    expirationDay: string;

    ticketPrefix: string;
    ticketSuffix: string;

    sequenceNumber?: number;

    customerId: string | null;

    printedMachineSessionId: string | null;
    redeemedMachineSessionId: string | null;

    printedAt: Date | null;
    printedById: string | null;
    printedByType: TriggerType.Machine | TriggerType.TransactionPoint | null;
    printedGamingDay: string | null;
    resolvedAt: Date | null;
    resolvedById: string | null;
    resolvedByType: TriggerType.Machine | TriggerType.TransactionPoint | null;
    resolvedGamingDay: string | null;
    expiredAt: Date | null;

    operations?: TgTicketOperation[];
}
