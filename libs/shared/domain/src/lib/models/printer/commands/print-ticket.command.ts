import { deserialize } from '@cms/serialization';
import { IsNotEmptyString } from '@cms/validation';
import { BaseCommand } from '@tronius/shared-common';
import { IsObject, IsString, IsUUID, Length } from 'class-validator';

import { TgTicket, ticketNumberLength } from '../../temp-ticket';

export interface PrintTicketCommandProps {
    printerId: string;
    ticket: TgTicket;
    ticketNumber: string;
    workstationIdentifier: string;
}

/**
 * @description Command to print a ticket on a ticket printer. This command is handled by the cuttlefish service.
 */
export class PrintTicketCommand extends BaseCommand implements PrintTicketCommandProps {
    @IsUUID()
    readonly printerId!: string;

    @IsObject()
    readonly ticket!: TgTicket;

    @IsString()
    @Length(ticketNumberLength, ticketNumberLength)
    readonly ticketNumber!: string;

    @IsNotEmptyString()
    readonly workstationIdentifier!: string;

    protected constructor() {
        super();
    }

    static create(props: PrintTicketCommandProps): PrintTicketCommand {
        return deserialize(PrintTicketCommand, props);
    }
}
