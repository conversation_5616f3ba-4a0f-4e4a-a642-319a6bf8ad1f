import { deserialize } from '@cms/serialization';
import { IsNotEmptyString } from '@cms/validation';
import { Job } from '@tronius/shared-common';
import { IsObject, IsString, IsUUID, Length } from 'class-validator';

import { TgTicket, ticketNumberLength } from '../../temp-ticket';

export interface PrintTicketJobProps {
    printerId: string;
    ticket: TgTicket;
    ticketNumber: string;
    workstationIdentifier: string;
}

export class PrintTicketJob extends Job implements PrintTicketJobProps {
    @IsUUID()
    readonly printerId!: string;

    @IsObject()
    readonly ticket!: TgTicket;

    @IsString()
    @Length(ticketNumberLength, ticketNumberLength)
    readonly ticketNumber!: string;

    @IsNotEmptyString()
    readonly workstationIdentifier!: string;

    protected constructor() {
        super();
    }

    static create(props: PrintTicketJobProps): PrintTicketJob {
        return deserialize(PrintTicketJob, {
            jobId: props.ticket.id,
            ...props
        });
    }
}
