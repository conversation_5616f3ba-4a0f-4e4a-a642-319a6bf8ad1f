import { DenominationBreakdownValue } from '@cms/denominations';
import { deserialize } from '@cms/serialization';
import { IsNotEmptyString } from '@cms/validation';
import { CustomerTransactionEntryType, UUIDFactory } from '@tronius/shared-common';
import { Exclude, Type } from 'class-transformer';
import { IsArray, IsEnum, IsIn, IsNumber, IsOptional, IsPositive, IsString, IsUUID, Length, ValidateNested } from 'class-validator';

import { TgTicketType, ticketNumberLength } from '../temp-ticket';

export enum EntryInOutType {
    In = 'In',
    Out = 'Out'
}

export enum EntryDebitCreditType {
    Debit = 'Debit',
    Credit = 'Credit'
}

export enum CustomerTransactionStatus {
    Pending = 'Pending',
    Processed = 'Processed',
    Failed = 'Failed'
}

export const InEntryTypes = new Set([
    CustomerTransactionEntryType.CashIn,
    CustomerTransactionEntryType.RedeemTicket,
    CustomerTransactionEntryType.PayHandpay,
    CustomerTransactionEntryType.WalletWithdrawal,
    CustomerTransactionEntryType.RedeemChip
]);
export const OutEntryTypes = new Set([
    CustomerTransactionEntryType.CashOut,
    CustomerTransactionEntryType.PrintTicket,
    CustomerTransactionEntryType.WalletDeposit,
    CustomerTransactionEntryType.IssueChip
]);
export const DebitEntryTypes = new Set([
    CustomerTransactionEntryType.CashIn,
    CustomerTransactionEntryType.RedeemTicket,
    CustomerTransactionEntryType.PayHandpay,
    CustomerTransactionEntryType.WalletWithdrawal,
    CustomerTransactionEntryType.RedeemChip
]);
export const CreditEntryTypes = new Set([
    CustomerTransactionEntryType.CashOut,
    CustomerTransactionEntryType.PrintTicket,
    CustomerTransactionEntryType.WalletDeposit,
    CustomerTransactionEntryType.IssueChip
]);

interface BaseProcessCustomerTransactionEntryProps {
    readonly type: CustomerTransactionEntryType;
    readonly amount: number;
}

interface WithDenominationBreakdownProps {
    readonly denominationBreakdown?: DenominationBreakdownValue[];
}

export abstract class BaseProcessCustomerTransactionEntry implements BaseProcessCustomerTransactionEntryProps {
    @IsEnum(CustomerTransactionEntryType)
    abstract readonly type: CustomerTransactionEntryType;

    @IsNumber()
    @IsPositive()
    abstract readonly amount: number;

    @Exclude()
    get inAmount(): number {
        return InEntryTypes.has(this.type) ? this.amount : 0;
    }

    @Exclude()
    get outAmount(): number {
        return OutEntryTypes.has(this.type) ? this.amount : 0;
    }

    @Exclude()
    get debitAmount(): number {
        return DebitEntryTypes.has(this.type) ? this.amount : 0;
    }

    @Exclude()
    get creditAmount(): number {
        return CreditEntryTypes.has(this.type) ? this.amount : 0;
    }

    @Exclude()
    get inOutType(): EntryInOutType {
        return InEntryTypes.has(this.type) ? EntryInOutType.In : EntryInOutType.Out;
    }

    @Exclude()
    get debitCreditType(): EntryDebitCreditType {
        return DebitEntryTypes.has(this.type) ? EntryDebitCreditType.Debit : EntryDebitCreditType.Credit;
    }
}

export interface ProcessCustomerTransactionCashInProps extends BaseProcessCustomerTransactionEntryProps, WithDenominationBreakdownProps {
    readonly type: CustomerTransactionEntryType.CashIn;
}

export abstract class ProcessCustomerTransactionCashIn
    extends BaseProcessCustomerTransactionEntry
    implements ProcessCustomerTransactionCashInProps
{
    @IsIn([CustomerTransactionEntryType.CashIn])
    abstract override readonly type: CustomerTransactionEntryType.CashIn;

    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => DenominationBreakdownValue)
    readonly denominationBreakdown?: DenominationBreakdownValue[];

    static create(props: ProcessCustomerTransactionCashInProps): ProcessCustomerTransactionCashIn {
        return deserialize(ProcessCustomerTransactionCashIn, props);
    }
}

export interface ProcessCustomerTransactionCashOutProps extends BaseProcessCustomerTransactionEntryProps, WithDenominationBreakdownProps {
    readonly type: CustomerTransactionEntryType.CashOut;
}

export abstract class CreateCustomerTransactionCashOut
    extends BaseProcessCustomerTransactionEntry
    implements ProcessCustomerTransactionCashOutProps
{
    @IsIn([CustomerTransactionEntryType.CashOut])
    abstract override readonly type: CustomerTransactionEntryType.CashOut;

    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => DenominationBreakdownValue)
    readonly denominationBreakdown?: DenominationBreakdownValue[];

    static create(props: ProcessCustomerTransactionCashOutProps): CreateCustomerTransactionCashOut {
        return deserialize(CreateCustomerTransactionCashOut, props);
    }
}

export interface ProcessCustomerTransactionRedeemTicketProps extends BaseProcessCustomerTransactionEntryProps {
    readonly type: CustomerTransactionEntryType.RedeemTicket;
    readonly ticketNumber: string;
}

export abstract class ProcessCustomerTransactionRedeemTicket
    extends BaseProcessCustomerTransactionEntry
    implements ProcessCustomerTransactionRedeemTicketProps
{
    @IsIn([CustomerTransactionEntryType.RedeemTicket])
    abstract override readonly type: CustomerTransactionEntryType.RedeemTicket;

    @IsString()
    @Length(ticketNumberLength, ticketNumberLength)
    abstract readonly ticketNumber: string;

    static create(props: ProcessCustomerTransactionRedeemTicketProps): ProcessCustomerTransactionRedeemTicket {
        return deserialize(ProcessCustomerTransactionRedeemTicket, props);
    }
}

export interface ProcessCustomerTransactionPrintTicketProps extends BaseProcessCustomerTransactionEntryProps {
    readonly type: CustomerTransactionEntryType.PrintTicket;
    readonly ticketType: TgTicketType;
}

export abstract class ProcessCustomerTransactionPrintTicket
    extends BaseProcessCustomerTransactionEntry
    implements ProcessCustomerTransactionPrintTicketProps
{
    @IsIn([CustomerTransactionEntryType.PrintTicket])
    abstract override readonly type: CustomerTransactionEntryType.PrintTicket;

    @IsEnum(TgTicketType)
    abstract readonly ticketType: TgTicketType;

    static create(props: ProcessCustomerTransactionPrintTicketProps): ProcessCustomerTransactionPrintTicket {
        return deserialize(ProcessCustomerTransactionPrintTicket, props);
    }
}

export interface ProcessCustomerTransactionPayHandpayProps extends BaseProcessCustomerTransactionEntryProps {
    readonly type: CustomerTransactionEntryType.PayHandpay;
    readonly handpayId: string;
}

export abstract class ProcessCustomerTransactionPayHandpay
    extends BaseProcessCustomerTransactionEntry
    implements ProcessCustomerTransactionPayHandpayProps
{
    @IsIn([CustomerTransactionEntryType.PayHandpay])
    abstract override readonly type: CustomerTransactionEntryType.PayHandpay;

    @IsUUID()
    abstract readonly handpayId: string;

    static create(props: ProcessCustomerTransactionPayHandpayProps): ProcessCustomerTransactionPayHandpay {
        return deserialize(ProcessCustomerTransactionPayHandpay, props);
    }
}

export interface ProcessCustomerTransactionWalletDepositProps extends BaseProcessCustomerTransactionEntryProps {
    type: CustomerTransactionEntryType.WalletDeposit;
}

export abstract class ProcessCustomerTransactionWalletDeposit
    extends BaseProcessCustomerTransactionEntry
    implements ProcessCustomerTransactionWalletDepositProps
{
    @IsIn([CustomerTransactionEntryType.WalletDeposit])
    abstract override readonly type: CustomerTransactionEntryType.WalletDeposit;

    static create(props: ProcessCustomerTransactionWalletDepositProps): ProcessCustomerTransactionWalletDeposit {
        return deserialize(ProcessCustomerTransactionWalletDeposit, props);
    }
}

export interface ProcessCustomerTransactionWalletWithdrawalProps extends BaseProcessCustomerTransactionEntryProps {
    type: CustomerTransactionEntryType.WalletWithdrawal;
}

export abstract class ProcessCustomerTransactionWalletWithdrawal
    extends BaseProcessCustomerTransactionEntry
    implements ProcessCustomerTransactionWalletWithdrawalProps
{
    @IsIn([CustomerTransactionEntryType.WalletWithdrawal])
    abstract override readonly type: CustomerTransactionEntryType.WalletWithdrawal;

    static create(props: ProcessCustomerTransactionWalletWithdrawalProps): ProcessCustomerTransactionWalletWithdrawal {
        return deserialize(ProcessCustomerTransactionWalletWithdrawal, props);
    }
}

export interface ProcessCustomerTransactionIssueChipProps extends BaseProcessCustomerTransactionEntryProps, WithDenominationBreakdownProps {
    readonly type: CustomerTransactionEntryType.IssueChip;
}

export abstract class ProcessCustomerTransactionIssueChip
    extends BaseProcessCustomerTransactionEntry
    implements ProcessCustomerTransactionIssueChipProps
{
    @IsIn([CustomerTransactionEntryType.IssueChip])
    abstract override readonly type: CustomerTransactionEntryType.IssueChip;

    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => DenominationBreakdownValue)
    readonly denominationBreakdown?: DenominationBreakdownValue[];

    static create(props: ProcessCustomerTransactionIssueChipProps): ProcessCustomerTransactionIssueChip {
        return deserialize(ProcessCustomerTransactionIssueChip, props);
    }
}

export interface ProcessCustomerTransactionRedeemChipProps
    extends BaseProcessCustomerTransactionEntryProps,
        WithDenominationBreakdownProps {
    readonly type: CustomerTransactionEntryType.RedeemChip;
}

export abstract class ProcessCustomerTransactionRedeemChip
    extends BaseProcessCustomerTransactionEntry
    implements ProcessCustomerTransactionRedeemChipProps
{
    @IsIn([CustomerTransactionEntryType.RedeemChip])
    abstract override readonly type: CustomerTransactionEntryType.RedeemChip;

    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => DenominationBreakdownValue)
    readonly denominationBreakdown?: DenominationBreakdownValue[];

    static create(props: ProcessCustomerTransactionRedeemChipProps): ProcessCustomerTransactionRedeemChip {
        return deserialize(ProcessCustomerTransactionRedeemChip, props);
    }
}

export type ProcessCustomerTransactionEntryProps =
    | ProcessCustomerTransactionCashInProps
    | ProcessCustomerTransactionCashOutProps
    | ProcessCustomerTransactionIssueChipProps
    | ProcessCustomerTransactionPayHandpayProps
    | ProcessCustomerTransactionPrintTicketProps
    | ProcessCustomerTransactionRedeemChipProps
    | ProcessCustomerTransactionRedeemTicketProps
    | ProcessCustomerTransactionWalletDepositProps
    | ProcessCustomerTransactionWalletWithdrawalProps;

export type ProcessCustomerTransactionEntry =
    | CreateCustomerTransactionCashOut
    | ProcessCustomerTransactionCashIn
    | ProcessCustomerTransactionIssueChip
    | ProcessCustomerTransactionPayHandpay
    | ProcessCustomerTransactionPrintTicket
    | ProcessCustomerTransactionRedeemChip
    | ProcessCustomerTransactionRedeemTicket
    | ProcessCustomerTransactionWalletDeposit
    | ProcessCustomerTransactionWalletWithdrawal;

interface CustomerTransactionEntryEntity {
    id: string;
    customerTransactionId: string;
    status: CustomerTransactionStatus;
    failureReason: string | null;
}

export type CustomerTransactionCashInProps = CustomerTransactionEntryEntity & ProcessCustomerTransactionCashInProps;

export abstract class CustomerTransactionCashIn extends ProcessCustomerTransactionCashIn implements CustomerTransactionCashInProps {
    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsUUID()
    readonly customerTransactionId!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsOptional()
    @IsNotEmptyString()
    failureReason!: string | null;

    static override create(props: CustomerTransactionCashInProps): CustomerTransactionCashIn {
        return deserialize(CustomerTransactionCashIn, props);
    }
}

export type CustomerTransactionCashOutProps = CustomerTransactionEntryEntity & ProcessCustomerTransactionCashOutProps;

export abstract class CustomerTransactionCashOut extends CreateCustomerTransactionCashOut implements CustomerTransactionCashOutProps {
    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsUUID()
    readonly customerTransactionId!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsOptional()
    @IsNotEmptyString()
    failureReason!: string | null;

    static override create(props: CustomerTransactionCashOutProps): CustomerTransactionCashOut {
        return deserialize(CustomerTransactionCashOut, props);
    }
}

export type CustomerTransactionRedeemTicketProps = CustomerTransactionEntryEntity & ProcessCustomerTransactionRedeemTicketProps;

export abstract class CustomerTransactionRedeemTicket
    extends ProcessCustomerTransactionRedeemTicket
    implements CustomerTransactionRedeemTicketProps
{
    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsUUID()
    readonly customerTransactionId!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsOptional()
    @IsNotEmptyString()
    failureReason!: string | null;

    static override create(props: CustomerTransactionRedeemTicketProps): CustomerTransactionRedeemTicket {
        return deserialize(CustomerTransactionRedeemTicket, props);
    }
}

export type CustomerTransactionPrintTicketProps = CustomerTransactionEntryEntity &
    ProcessCustomerTransactionPrintTicketProps & { ticketId?: string; ticketSuffix?: string };

export abstract class CustomerTransactionPrintTicket
    extends ProcessCustomerTransactionPrintTicket
    implements CustomerTransactionPrintTicketProps
{
    @IsOptional()
    @IsUUID()
    readonly ticketId?: string;

    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsUUID()
    readonly customerTransactionId!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsOptional()
    @IsNotEmptyString()
    failureReason!: string | null;

    @IsOptional()
    @IsNotEmptyString()
    readonly ticketSuffix?: string;

    static override create(props: CustomerTransactionPrintTicketProps): CustomerTransactionPrintTicket {
        return deserialize(CustomerTransactionPrintTicket, props);
    }
}

export type CustomerTransactionPayHandpayProps = CustomerTransactionEntryEntity & ProcessCustomerTransactionPayHandpayProps;

export abstract class CustomerTransactionPayHandpay
    extends ProcessCustomerTransactionPayHandpay
    implements CustomerTransactionPayHandpayProps
{
    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsUUID()
    readonly customerTransactionId!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsOptional()
    @IsNotEmptyString()
    failureReason!: string | null;

    static override create(props: CustomerTransactionPayHandpayProps): CustomerTransactionPayHandpay {
        return deserialize(CustomerTransactionPayHandpay, props);
    }
}

export type CustomerTransactionWalletDepositProps = CustomerTransactionEntryEntity & ProcessCustomerTransactionWalletDepositProps;

export abstract class CustomerTransactionWalletDeposit
    extends ProcessCustomerTransactionWalletDeposit
    implements CustomerTransactionWalletDepositProps
{
    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsUUID()
    readonly customerTransactionId!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsOptional()
    @IsNotEmptyString()
    failureReason!: string | null;

    static override create(props: CustomerTransactionWalletDepositProps): CustomerTransactionWalletDeposit {
        return deserialize(CustomerTransactionWalletDeposit, props);
    }
}

export type CustomerTransactionWalletWithdrawalProps = CustomerTransactionEntryEntity & ProcessCustomerTransactionWalletWithdrawalProps;

export abstract class CustomerTransactionWalletWithdrawal
    extends ProcessCustomerTransactionWalletWithdrawal
    implements CustomerTransactionWalletWithdrawalProps
{
    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsUUID()
    readonly customerTransactionId!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsOptional()
    @IsNotEmptyString()
    failureReason!: string | null;

    static override create(props: CustomerTransactionWalletWithdrawalProps): CustomerTransactionWalletWithdrawal {
        return deserialize(CustomerTransactionWalletWithdrawal, props);
    }
}

export type CustomerTransactionIssueChipProps = CustomerTransactionEntryEntity & ProcessCustomerTransactionIssueChipProps;

export abstract class CustomerTransactionIssueChip
    extends ProcessCustomerTransactionIssueChip
    implements CustomerTransactionIssueChipProps
{
    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsUUID()
    readonly customerTransactionId!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsOptional()
    @IsNotEmptyString()
    failureReason!: string | null;

    static override create(props: CustomerTransactionIssueChipProps): CustomerTransactionIssueChip {
        return deserialize(CustomerTransactionIssueChip, props);
    }
}

export type CustomerTransactionRedeemChipProps = CustomerTransactionEntryEntity & ProcessCustomerTransactionRedeemChipProps;

export abstract class CustomerTransactionRedeemChip
    extends ProcessCustomerTransactionRedeemChip
    implements CustomerTransactionRedeemChipProps
{
    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsUUID()
    readonly customerTransactionId!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsOptional()
    @IsNotEmptyString()
    failureReason!: string | null;

    static override create(props: CustomerTransactionRedeemChipProps): CustomerTransactionRedeemChip {
        return deserialize(CustomerTransactionRedeemChip, props);
    }
}

export type CustomerTransactionEntryProps =
    | CustomerTransactionCashInProps
    | CustomerTransactionCashOutProps
    | CustomerTransactionIssueChipProps
    | CustomerTransactionPayHandpayProps
    | CustomerTransactionPrintTicketProps
    | CustomerTransactionRedeemChipProps
    | CustomerTransactionRedeemTicketProps
    | CustomerTransactionWalletDepositProps
    | CustomerTransactionWalletWithdrawalProps;

export type CustomerTransactionEntry =
    | CustomerTransactionCashIn
    | CustomerTransactionCashOut
    | CustomerTransactionIssueChip
    | CustomerTransactionPayHandpay
    | CustomerTransactionPrintTicket
    | CustomerTransactionRedeemChip
    | CustomerTransactionRedeemTicket
    | CustomerTransactionWalletDeposit
    | CustomerTransactionWalletWithdrawal;
