import { CustomerListItem } from '@cms/customers';
import { deserialize } from '@cms/serialization';
import { IsGamingDay, IsNullable, IsPlatformComponentUsername } from '@cms/validation';
import { CustomerTransactionEntryType, NumberUtils, UUIDFactory, ValidationError } from '@tronius/shared-common';
import { Exclude, Transform, Type } from 'class-transformer';
import {
    ArrayNotEmpty,
    IsArray,
    IsBoolean,
    IsDate,
    IsDefined,
    IsEnum,
    IsNumber,
    IsOptional,
    IsUUID,
    ValidateNested
} from 'class-validator';

import { TgTicket } from '../temp-ticket';
import {
    CreateCustomerTransactionCashOut,
    CustomerTransactionCashIn,
    CustomerTransactionCashOut,
    CustomerTransactionEntry,
    CustomerTransactionEntryProps,
    CustomerTransactionIssueChip,
    CustomerTransactionPayHandpay,
    CustomerTransactionPrintTicket,
    CustomerTransactionRedeemChip,
    CustomerTransactionRedeemTicket,
    CustomerTransactionStatus,
    CustomerTransactionWalletDeposit,
    CustomerTransactionWalletWithdrawal,
    ProcessCustomerTransactionCashIn,
    ProcessCustomerTransactionEntry,
    ProcessCustomerTransactionEntryProps,
    ProcessCustomerTransactionIssueChip,
    ProcessCustomerTransactionPayHandpay,
    ProcessCustomerTransactionPrintTicket,
    ProcessCustomerTransactionRedeemChip,
    ProcessCustomerTransactionRedeemTicket,
    ProcessCustomerTransactionWalletDeposit,
    ProcessCustomerTransactionWalletWithdrawal
} from './customer-transaction-entity.model';

export interface ProcessCustomerTransactionProps {
    customerId?: string;
    manifest: ProcessCustomerTransactionEntryProps[];
}

export abstract class ProcessCustomerTransaction implements ProcessCustomerTransactionProps {
    @IsUUID()
    @IsOptional()
    readonly customerId?: string;

    @IsArray()
    @ValidateNested({ each: true })
    @Transform(({ value: inputArray, key }): ProcessCustomerTransactionEntry[] => {
        if (!Array.isArray(inputArray)) {
            throw new ValidationError({ message: `${key} is not an array` });
        }

        // eslint-disable-next-line array-callback-return, consistent-return
        const transformedManifest = inputArray.map((object): ProcessCustomerTransactionEntry => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
            if (!object || typeof object !== 'object' || !Object.values(CustomerTransactionEntryType).includes(object.type)) {
                throw new ValidationError({ message: `${key} is invalid object` });
            }

            const entry = object as ProcessCustomerTransactionEntryProps;
            switch (entry.type) {
                case CustomerTransactionEntryType.CashIn:
                    return ProcessCustomerTransactionCashIn.create(entry);
                case CustomerTransactionEntryType.CashOut:
                    return CreateCustomerTransactionCashOut.create(entry);
                case CustomerTransactionEntryType.RedeemTicket:
                    return ProcessCustomerTransactionRedeemTicket.create(entry);
                case CustomerTransactionEntryType.PrintTicket:
                    return ProcessCustomerTransactionPrintTicket.create(entry);
                case CustomerTransactionEntryType.PayHandpay:
                    return ProcessCustomerTransactionPayHandpay.create(entry);
                case CustomerTransactionEntryType.WalletDeposit:
                    return ProcessCustomerTransactionWalletDeposit.create(entry);
                case CustomerTransactionEntryType.WalletWithdrawal:
                    return ProcessCustomerTransactionWalletWithdrawal.create(entry);
                case CustomerTransactionEntryType.IssueChip:
                    return ProcessCustomerTransactionIssueChip.create(entry);
                case CustomerTransactionEntryType.RedeemChip:
                    return ProcessCustomerTransactionRedeemChip.create(entry);
            }
        });

        const walletOperations = transformedManifest.filter(
            (entry) =>
                entry.type === CustomerTransactionEntryType.WalletDeposit || entry.type === CustomerTransactionEntryType.WalletWithdrawal
        );

        if (walletOperations.length > 1) {
            throw new ValidationError({ message: 'Multiple wallet operations per transaction are not allowed' });
        }

        return transformedManifest;
    })
    readonly manifest: ProcessCustomerTransactionEntry[] = [];

    protected constructor() {
        //
    }

    @IsNumber()
    @Exclude()
    get totalIn(): number {
        const sum = this.manifest.reduce((acc, entry) => acc + entry.inAmount, 0);
        return NumberUtils.toFloat(sum);
    }

    @IsNumber()
    @Exclude()
    get totalOut(): number {
        const sum = this.manifest.reduce((acc, entry) => acc + entry.outAmount, 0);
        return NumberUtils.toFloat(sum);
    }

    @IsNumber()
    @Exclude()
    get totalVariance(): number {
        const variance = this.totalIn - this.totalOut;
        return NumberUtils.toFloat(variance);
    }

    @IsBoolean()
    @Exclude()
    get isTotalValid(): boolean {
        return this.totalVariance === 0 && this.manifest.length > 0;
    }

    static create(props: ProcessCustomerTransactionProps): ProcessCustomerTransaction {
        return deserialize(ProcessCustomerTransaction, props);
    }
}

export interface CustomerTransactionProps extends ProcessCustomerTransactionProps {
    id: string;
    processedAt: Date;
    gamingDay: string;
    transactionPointId: string;
    username: string;
}

export class CustomerTransaction extends ProcessCustomerTransaction implements CustomerTransactionProps {
    @IsUUID()
    readonly id = UUIDFactory.create();

    @IsDate()
    @Type(() => Date)
    readonly processedAt: Date = new Date();

    @IsGamingDay()
    readonly gamingDay!: string;

    @IsUUID()
    readonly transactionPointId!: string;

    @IsPlatformComponentUsername()
    readonly username!: string;

    @IsEnum(CustomerTransactionStatus)
    status: CustomerTransactionStatus = CustomerTransactionStatus.Pending;

    @IsArray()
    @ArrayNotEmpty()
    @ValidateNested({ each: true })
    @Transform(({ value: inputArray, key }): CustomerTransactionEntry[] => {
        if (!Array.isArray(inputArray) || inputArray.length === 0) {
            throw new ValidationError({ message: `${key} is not an array` });
        }

        // eslint-disable-next-line array-callback-return, consistent-return
        return inputArray.map((object): CustomerTransactionEntry => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access
            if (!object || typeof object !== 'object' || !Object.values(CustomerTransactionEntryType).includes(object.type)) {
                throw new ValidationError({ message: `${key} is invalid object` });
            }

            const entry = object as CustomerTransactionEntryProps;
            switch (entry.type) {
                case CustomerTransactionEntryType.CashIn:
                    return CustomerTransactionCashIn.create(entry);
                case CustomerTransactionEntryType.CashOut:
                    return CustomerTransactionCashOut.create(entry);
                case CustomerTransactionEntryType.RedeemTicket:
                    return CustomerTransactionRedeemTicket.create(entry);
                case CustomerTransactionEntryType.PrintTicket:
                    return CustomerTransactionPrintTicket.create(entry);
                case CustomerTransactionEntryType.PayHandpay:
                    return CustomerTransactionPayHandpay.create(entry);
                case CustomerTransactionEntryType.WalletDeposit:
                    return CustomerTransactionWalletDeposit.create(entry);
                case CustomerTransactionEntryType.WalletWithdrawal:
                    return CustomerTransactionWalletWithdrawal.create(entry);
                case CustomerTransactionEntryType.RedeemChip:
                    return CustomerTransactionRedeemChip.create(entry);
                case CustomerTransactionEntryType.IssueChip:
                    return CustomerTransactionIssueChip.create(entry);
            }
        });
    })
    override readonly manifest: CustomerTransactionEntry[] = [];

    static override create(props: Omit<CustomerTransactionProps, 'id' | 'processedAt'>): CustomerTransaction {
        const id = UUIDFactory.create();
        return deserialize(CustomerTransaction, {
            ...props,
            id,
            manifest: props.manifest.map((entry) => ({ ...entry, customerTransactionId: id }))
        });
    }

    static map(props: CustomerTransactionProps): CustomerTransaction {
        return deserialize(CustomerTransaction, props);
    }
}

export interface CustomerTransactionGetTicketWithCustomerResponseProps {
    ticket: TgTicket;
    customerProfile: CustomerListItem | null;
}

export class CustomerTransactionGetTicketWithCustomerResponse implements CustomerTransactionGetTicketWithCustomerResponseProps {
    @IsDefined()
    readonly ticket!: TgTicket;

    @IsNullable()
    @ValidateNested()
    @Type(() => CustomerListItem)
    readonly customerProfile!: CustomerListItem | null;

    static create(props: CustomerTransactionGetTicketWithCustomerResponseProps): CustomerTransactionGetTicketWithCustomerResponse {
        return deserialize(CustomerTransactionGetTicketWithCustomerResponse, props);
    }
}
