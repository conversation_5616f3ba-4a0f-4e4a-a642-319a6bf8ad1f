import { CustomerTransactionEntryType, UUIDFactory, ValidationError } from '@tronius/shared-common';

import { TgTicketType } from '../temp-ticket';
import { ProcessCustomerTransactionEntryProps } from './customer-transaction-entity.model';
import { ProcessCustomerTransaction, ProcessCustomerTransactionProps } from './customer-transaction.model';

const validProcessCustomerTransactionProps: ProcessCustomerTransactionProps[] = [
    {
        customerId: UUIDFactory.create(),
        manifest: [
            {
                type: CustomerTransactionEntryType.CashIn,
                amount: 100
            },
            {
                type: CustomerTransactionEntryType.CashOut,
                amount: 100
            },
            {
                type: CustomerTransactionEntryType.RedeemTicket,
                amount: 100,
                ticketNumber: '123456789123456789'
            },
            {
                type: CustomerTransactionEntryType.PrintTicket,
                amount: 100,
                ticketType: TgTicketType.Cash
            },
            {
                type: CustomerTransactionEntryType.PayHandpay,
                amount: 100,
                handpayId: UUIDFactory.create()
            }
        ]
    }
];

const invalidProcessCustomerTransactionProps: ProcessCustomerTransactionProps[] = [
    {
        manifest: [
            {
                type: CustomerTransactionEntryType.CashIn,
                amount: 0
            }
        ]
    },
    {
        customerId: 'wrong-uuid',
        manifest: [
            {
                type: CustomerTransactionEntryType.CashIn,
                amount: 0
            }
        ]
    },
    {
        manifest: [
            {
                type: CustomerTransactionEntryType.PrintTicket,
                amount: 100
            } as ProcessCustomerTransactionEntryProps
        ]
    },
    {
        manifest: [
            {
                type: CustomerTransactionEntryType.RedeemTicket,
                amount: 100,
                ticketNumber: 'wrong-ticket-number'
            }
        ]
    },
    {
        manifest: [
            {
                type: CustomerTransactionEntryType.PayHandpay,
                amount: 100,
                handpayId: 'wrong-uuid'
            }
        ]
    }
];

describe('ProcessCustomerTransaction model', () => {
    test.each(validProcessCustomerTransactionProps)('should create a valid ProcessCustomerTransaction instance', (props) => {
        expect(() => ProcessCustomerTransaction.create(props)).not.toThrow();
    });

    test.each(invalidProcessCustomerTransactionProps)(
        'should throw an error when creating a ProcessCustomerTransaction instance with invalid props',
        (props) => {
            expect(() => ProcessCustomerTransaction.create(props)).toThrow(ValidationError);
        }
    );
});

describe('ProcessCustomerTransaction - totalOut, totalVariance Calculation with PrintTicket (Rounding Problem)', () => {
    it('should correctly compute totalOut, totalVariance as 1354.32 (-1354.32) for 11 PrintTicket entries of 123.12 each', () => {
        const entryValue = 123.12;

        const ticketEntries = Array.from(
            { length: 11 },
            () =>
                ({
                    type: CustomerTransactionEntryType.PrintTicket,
                    amount: entryValue,
                    ticketType: TgTicketType.Cash
                }) as ProcessCustomerTransactionEntryProps
        );

        const transaction = ProcessCustomerTransaction.create({
            manifest: ticketEntries
        });

        expect(transaction.totalIn).toBe(0);
        expect(transaction.totalOut).toBe(1354.32);
        expect(transaction.totalVariance).toBe(-1354.32);
        expect(transaction.isTotalValid).toBe(false);
    });
});
