export enum CmsAction {
    AppBackoffice = 'app:backoffice',
    // Workaround for loyalty managers default route
    AppLoyalty = 'app:loyalty',
    AppCashier = 'app:cashier',
    AppCustomers = 'app:customers',

    JackpotListLevels = 'jackpot:list-levels',
    JackpotListLevelHistory = 'jackpot:list-level-history',
    JackpotCreateLevel = 'jackpot:create-level',
    JackpotUpdateLevel = 'jackpot:update-level',
    JackpotAddExternalTriggerType = 'jackpot:add-external-trigger-type',
    JackpotToggleLevel = 'jackpot:toggle-level',
    JackpotResetLevel = 'jackpot:reset-level',
    JackpotDeleteLevel = 'jackpot:delete-level',
    JackpotSetLevelValue = 'jackpot:set-level-value',
    JackpotListClients = 'jackpot:list-clients',
    JackpotAddLevelsForClient = 'jackpot:add-levels-for-client',
    JackpotRemoveLevelsForClient = 'jackpot:remove-levels-for-client',
    JackpotSaveClientGroupIds = 'jackpot:save-client-group-ids',
    JackpotRamClear = 'jackpot:ram-clear',

    MachineFundTransferOperationsList = 'machine-fund-transfer-operations:list',

    MachineFundsTransferList = 'machine-funds-transfer:list',
    MachineFundsTransferCreateManualTransferIn = 'machine-funds-transfer:create-manual-transfer-in',
    MachineFundsTransferCancel = 'machine-funds-transfer:cancel',
    MachineFundsTransferComplete = 'machine-funds-transfer:complete',

    PrinterList = 'printer:list',
    PrinterCreate = 'printer:create',
    PrinterUpdate = 'printer:update',
    PrinterDelete = 'printer:delete',

    EventsList = 'events:list',

    MeterListRawCurrent = 'meter:list-raw-current',
    MeterListRawSnapshot = 'meter:list-raw-snapshot',

    TransactionPointList = 'transaction-point:list',
    TransactionPointCreate = 'transaction-point:create',
    TransactionPointUpdate = 'transaction-point:update',
    TransactionPointDelete = 'transaction-point:delete',
    TransactionPointOpen = 'transaction-point:open',
    TransactionPointClose = 'transaction-point:close',
    TransactionPointReopen = 'transaction-point:reopen',
    TransactionPointAssignWorkstation = 'transaction-point:assign-workstation',
    TransactionPointUnassignWorkstation = 'transaction-point:unassign-workstation',
    TransactionPointGetWorkstationTransactionPoint = 'transaction-point:get-workstation-transaction-point',

    CustomerTransactionProcess = 'customer-transaction:process',
    CustomerTransactionList = 'customer-transaction:list',

    MachineMetricsList = 'machine-metrics:list',
    MachineMetricsCalculateDaily = 'machine-metrics:calculate-daily',
    MachineMetricsDeleteLastDaily = 'machine-metrics:delete-last-daily',

    TicketOperationList = 'ticket-operation:list',

    ManufacturerList = 'manufacturer:list',
    ManufacturerCreate = 'manufacturer:create',
    ManufacturerUpdate = 'manufacturer:update',
    ManufacturerDelete = 'manufacturer:delete',

    MachineProfileList = 'machine-profile:list',
    MachineProfileCreate = 'machine-profile:create',
    MachineProfileUpdate = 'machine-profile:update',
    MachineProfileGetMedConfiguration = 'machine-profile:get-med-configuration',
    MachineProfileUpdateMedConfiguration = 'machine-profile:update-med-configuration',

    MachineList = 'machine:list',
    MachineUpdateOperationalStatus = 'machine:update-operational-status',
    MachineUpdateLockState = 'machine:update-lock-state',
    MachineRebootMed = 'machine:reboot-med',
    MachineRestartMedApp = 'machine:restart-med-app',

    MachineGameTypeCreate = 'machine-game-type:create',
    MachineGameTypeList = 'machine-game-type:list',
    MachineGameTypeUpdate = 'machine-game-type:update',

    DeviceList = 'device:list',
    DeviceCreate = 'device:create',
    DeviceUpdate = 'device:update',
    DeviceReboot = 'device:reboot',
    DeviceDelete = 'device:delete',

    TicketList = 'ticket:list',
    TicketCreateCash = 'ticket:create-cash',
    TicketCreatePromo = 'ticket:create-promo',
    TicketRedeem = 'ticket:redeem',
    TicketRedeemManual = 'ticket:redeem-manual',
    TicketVoid = 'ticket:void',
    TicketReplace = 'ticket:replace',

    HandpayList = 'handpay:list',
    HandpayCreateManualHandpay = 'handpay:create-manual-handpay',
    // Leaving out for the time being since it's not used
    HandpayAuthorize = 'handpay:authorize',
    HandpayReverse = 'handpay:reverse',
    HandpayVoid = 'handpay:void',
    HandpayPayInProperty = 'handpay:pay-in-property',
    HandpayReset = 'handpay:reset',

    HandpayOperationList = 'handpay-operation:list',

    PermissionList = 'permission:list',

    UserList = 'user:list',
    UserManage = 'user:manage',
    MedClientGetInfo = 'med-client:get-info',

    CountryList = 'country:list',
    CountryCreate = 'country:create',
    CountryUpdate = 'country:update',
    CountryDelete = 'country:delete',

    CustomerRegister = 'customer:register',
    CustomerList = 'customer:list',
    CustomerUpdate = 'customer:update',
    CustomerDelete = 'customer:delete',

    AssetTransferList = 'asset-transfer:list',
    AssetTransferCreate = 'asset-transfer:create',
    AssetTransferUpdate = 'asset-transfer:update',
    AssetTransferDelete = 'asset-transfer:delete',
    AssetTransferSend = 'asset-transfer:send',
    AssetTransferReceive = 'asset-transfer:receive',
    AssetTransferReverse = 'asset-transfer:reverse',

    AssetInventoryList = 'asset-inventory:list',
    AssetInventoryCreate = 'asset-inventory:create',
    AssetInventoryUpdate = 'asset-inventory:update',
    AssetInventoryDelete = 'asset-inventory:delete',

    FinancialEntityList = 'financial-entity:list',
    FinancialEntityCreate = 'financial-entity:create',
    FinancialEntityUpdate = 'financial-entity:update',
    FinancialEntityDelete = 'financial-entity:delete',

    DenominationList = 'denomination:list',
    DenominationCreate = 'denomination:create',
    DenominationUpdate = 'denomination:update',
    DenominationDelete = 'denomination:delete',

    InventorySnapshotList = 'inventory-snapshot:list',
    InventorySnapshotCreate = 'inventory-snapshot:create',
    InventorySnapshotUpdate = 'inventory-snapshot:update',
    InventorySnapshotDelete = 'inventory-snapshot:delete',

    CasinoPropertyList = 'casino-property:list',
    CasinoPropertyUpdateSettings = 'casino-property:update-settings',

    MachineSessionsListLive = 'machine-sessions:list-live',

    DeviceFirmware = 'device-firmware',

    MachineEarningRulesList = 'machine-earning-rules:list',
    MachineEarningRulesEdit = 'machine-earning-rules:edit',

    AuditLogList = 'audit-log:list',

    CashOperationList = 'cash-operation:list'
}

export const jackpotActions = [
    CmsAction.JackpotListLevels,
    CmsAction.JackpotListLevelHistory,
    CmsAction.JackpotCreateLevel,
    CmsAction.JackpotUpdateLevel,
    CmsAction.JackpotAddExternalTriggerType,
    CmsAction.JackpotToggleLevel,
    CmsAction.JackpotResetLevel,
    CmsAction.JackpotDeleteLevel,
    CmsAction.JackpotSetLevelValue,
    CmsAction.JackpotListClients,
    CmsAction.JackpotAddLevelsForClient,
    CmsAction.JackpotRemoveLevelsForClient,
    CmsAction.JackpotSaveClientGroupIds,
    CmsAction.JackpotRamClear
];

export const machineFundsTransferOperationActions = [CmsAction.MachineFundTransferOperationsList];

export const machineFundsTransferActions = [
    CmsAction.MachineFundsTransferList,
    CmsAction.MachineFundsTransferCreateManualTransferIn,
    CmsAction.MachineFundsTransferCancel,
    CmsAction.MachineFundsTransferComplete
];

export const printerActions = [CmsAction.PrinterList, CmsAction.PrinterCreate, CmsAction.PrinterUpdate, CmsAction.PrinterDelete];

export const eventActions = [CmsAction.EventsList];

export const meterActions = [CmsAction.MeterListRawCurrent, CmsAction.MeterListRawSnapshot];

export const transactionPointActionsWithoutGetWorkstationTransactionPoint = [
    CmsAction.TransactionPointList,
    CmsAction.TransactionPointCreate,
    CmsAction.TransactionPointUpdate,
    CmsAction.TransactionPointDelete,
    CmsAction.TransactionPointOpen,
    CmsAction.TransactionPointClose,
    CmsAction.TransactionPointReopen,
    CmsAction.TransactionPointAssignWorkstation,
    CmsAction.TransactionPointUnassignWorkstation
];

export const machineMetricsActions = [
    CmsAction.MachineMetricsList,
    CmsAction.MachineMetricsCalculateDaily,
    CmsAction.MachineMetricsDeleteLastDaily
];

export const ticketOperationActions = [CmsAction.TicketOperationList];

export const manufacturerActions = [
    CmsAction.ManufacturerList,
    CmsAction.ManufacturerCreate,
    CmsAction.ManufacturerUpdate,
    CmsAction.ManufacturerDelete
];

export const machineProfileActions = [
    CmsAction.MachineProfileList,
    CmsAction.MachineProfileCreate,
    CmsAction.MachineProfileUpdate,
    CmsAction.MachineProfileGetMedConfiguration,
    CmsAction.MachineProfileUpdateMedConfiguration
];

export const machineActions = [
    CmsAction.MachineList,
    CmsAction.MachineUpdateOperationalStatus,
    CmsAction.MachineUpdateLockState,
    CmsAction.MachineRebootMed,
    CmsAction.MachineRestartMedApp
];

export const machineGameTypeActions = [CmsAction.MachineGameTypeCreate, CmsAction.MachineGameTypeList, CmsAction.MachineGameTypeUpdate];

export const deviceActions = [
    CmsAction.DeviceList,
    CmsAction.DeviceCreate,
    CmsAction.DeviceUpdate,
    CmsAction.DeviceReboot,
    CmsAction.DeviceDelete
];

export const deviceFirmwareActions = [CmsAction.DeviceFirmware];

export const ticketActions = [
    CmsAction.TicketList,
    CmsAction.TicketCreateCash,
    CmsAction.TicketCreatePromo,
    CmsAction.TicketRedeem,
    CmsAction.TicketRedeemManual,
    CmsAction.TicketVoid,
    CmsAction.TicketReplace
];

export const handpayActions = [
    CmsAction.HandpayList,
    CmsAction.HandpayCreateManualHandpay,
    CmsAction.HandpayVoid,
    CmsAction.HandpayPayInProperty,
    CmsAction.HandpayReset,
    CmsAction.HandpayReverse,
    CmsAction.HandpayAuthorize,
    CmsAction.HandpayOperationList
];

export const userActions = [CmsAction.UserList, CmsAction.UserManage];

export const customerActions = [CmsAction.CustomerRegister, CmsAction.CustomerList, CmsAction.CustomerUpdate, CmsAction.CustomerDelete];

export const assetTransferActions = [
    CmsAction.AssetTransferList,
    CmsAction.AssetTransferCreate,
    CmsAction.AssetTransferUpdate,
    CmsAction.AssetTransferDelete,
    CmsAction.AssetTransferSend,
    CmsAction.AssetTransferReceive,
    CmsAction.AssetTransferReverse
];

export const assetInventoryActions = [
    CmsAction.AssetInventoryList,
    CmsAction.AssetInventoryCreate,
    CmsAction.AssetInventoryUpdate,
    CmsAction.AssetInventoryDelete
];

export const financialEntityActions = [
    CmsAction.FinancialEntityList,
    CmsAction.FinancialEntityCreate,
    CmsAction.FinancialEntityUpdate,
    CmsAction.FinancialEntityDelete
];

export const denominationActions = [
    CmsAction.DenominationList,
    CmsAction.DenominationCreate,
    CmsAction.DenominationUpdate,
    CmsAction.DenominationDelete
];

export const inventorySnapshotActions = [
    CmsAction.InventorySnapshotList,
    CmsAction.InventorySnapshotCreate,
    CmsAction.InventorySnapshotUpdate,
    CmsAction.InventorySnapshotDelete
];

export const casinoPropertyActions = [CmsAction.CasinoPropertyList, CmsAction.CasinoPropertyUpdateSettings];

export const customerTransactionActions = [CmsAction.CustomerTransactionProcess, CmsAction.CustomerTransactionList];

export const machineSessionActions = [CmsAction.MachineSessionsListLive];

export const machineEarningRuleActions = [CmsAction.MachineEarningRulesList, CmsAction.MachineEarningRulesEdit];

export const cashOperationActions = [CmsAction.CashOperationList];
