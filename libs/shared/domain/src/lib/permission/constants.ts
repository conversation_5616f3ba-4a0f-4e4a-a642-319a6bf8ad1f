import {
    CmsAction,
    cashOperationActions,
    casinoPropertyActions,
    customerTransactionActions,
    deviceActions,
    eventActions,
    machineActions,
    machineEarningRuleActions,
    machineGameTypeActions,
    machineMetricsActions,
    machineProfileActions,
    machineSessionActions,
    manufacturerActions,
    meterActions,
    printerActions,
    ticketOperationActions,
    transactionPointActionsWithoutGetWorkstationTransactionPoint,
    userActions
} from './cms-action.enum';
import { CmsRole } from './cms-role.enum';

const operatorSystemAdministratorActions: Set<CmsAction> = new Set([
    CmsAction.AppBackoffice,
    CmsAction.AppCustomers,
    CmsAction.CountryList,
    ...printerActions,
    ...eventActions,
    ...meterActions,
    ...transactionPointActionsWithoutGetWorkstationTransactionPoint,
    CmsAction.TransactionPointGetWorkstationTransactionPoint,
    ...machineMetricsActions,
    CmsAction.MachineList,
    CmsAction.MachineProfileList,
    CmsAction.MachineRebootMed,
    CmsAction.MachineRestartMedApp,
    ...deviceActions,
    CmsAction.TicketList,
    CmsAction.HandpayList,
    CmsAction.HandpayOperationList,
    ...userActions,
    CmsAction.MedClientGetInfo,
    ...casinoPropertyActions,
    ...machineSessionActions,
    CmsAction.AuditLogList,
    ...cashOperationActions
]);

const machineSupervisorActions: Set<CmsAction> = new Set([
    CmsAction.AppBackoffice,
    CmsAction.CountryList,
    ...eventActions,
    ...meterActions,
    CmsAction.TransactionPointGetWorkstationTransactionPoint,
    ...machineMetricsActions,
    ...ticketOperationActions,
    ...manufacturerActions,
    ...machineProfileActions,
    ...machineActions,
    ...deviceActions,
    ...printerActions,
    CmsAction.TicketList,
    CmsAction.HandpayList,
    CmsAction.HandpayReset,
    CmsAction.HandpayOperationList,
    CmsAction.CasinoPropertyList,
    ...machineSessionActions,
    ...machineGameTypeActions,
    ...cashOperationActions
]);

const cashierActions: Set<CmsAction> = new Set([
    CmsAction.AppBackoffice,
    CmsAction.AppCashier,
    CmsAction.CountryList,
    ...eventActions,
    CmsAction.TransactionPointGetWorkstationTransactionPoint,
    ...customerTransactionActions,
    CmsAction.MachineList,
    CmsAction.PrinterList,
    CmsAction.TransactionPointOpen,
    CmsAction.TransactionPointClose,
    CmsAction.TransactionPointReopen,
    CmsAction.PrinterList,
    CmsAction.TicketRedeem,
    CmsAction.HandpayList,
    CmsAction.HandpayReset,
    CmsAction.HandpayPayInProperty,
    CmsAction.HandpayOperationList,
    CmsAction.CasinoPropertyList,
    ...machineSessionActions
]);

const cageSupervisorActions: Set<CmsAction> = new Set([
    CmsAction.AppBackoffice,
    CmsAction.AppCustomers,
    CmsAction.CountryList,
    ...eventActions,
    ...transactionPointActionsWithoutGetWorkstationTransactionPoint,
    CmsAction.TransactionPointGetWorkstationTransactionPoint,
    CmsAction.MachineMetricsList,
    ...ticketOperationActions,
    CmsAction.MachineList,
    CmsAction.MachineProfileList,
    CmsAction.PrinterList,
    CmsAction.TicketList,
    CmsAction.TicketRedeem,
    CmsAction.TicketRedeemManual,
    CmsAction.TicketVoid,
    CmsAction.TicketReplace,
    CmsAction.HandpayList,
    CmsAction.HandpayCreateManualHandpay,
    CmsAction.HandpayVoid,
    CmsAction.HandpayPayInProperty,
    CmsAction.HandpayReset,
    CmsAction.HandpayReverse,
    CmsAction.HandpayOperationList,
    CmsAction.CasinoPropertyList,
    ...machineSessionActions
]);

const loyaltyManagerActions: Set<CmsAction> = new Set([
    CmsAction.CasinoPropertyList,
    CmsAction.AppLoyalty,
    CmsAction.CountryList,
    CmsAction.MachineGameTypeList,
    CmsAction.MachineGameTypeCreate,
    CmsAction.MachineGameTypeUpdate,
    CmsAction.TransactionPointGetWorkstationTransactionPoint,
    ...machineEarningRuleActions
]);

const rolePermissionsLookup: Map<CmsRole, Set<CmsAction>> = new Map();

rolePermissionsLookup.set(CmsRole.OperatorSystemAdministrator, operatorSystemAdministratorActions);
rolePermissionsLookup.set(CmsRole.MachineSupervisor, machineSupervisorActions);
rolePermissionsLookup.set(CmsRole.Cashier, cashierActions);
rolePermissionsLookup.set(CmsRole.CageSupervisor, cageSupervisorActions);
rolePermissionsLookup.set(CmsRole.LoyaltyManager, loyaltyManagerActions);

export { rolePermissionsLookup };
