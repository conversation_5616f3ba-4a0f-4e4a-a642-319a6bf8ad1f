import { Pipe, PipeTransform } from '@angular/core';
import { WinReconciliationReportType } from '@cms/machine-metrics';

@Pipe({
    name: 'winReconciliationReportType',
    standalone: true
})
export class WinReconciliationReportTypePipe implements PipeTransform {
    transform(value: string, ..._args: unknown[]): string {
        return WinReconciliationReportTypePipe.stringToDescription(value);
    }

    // eslint-disable-next-line consistent-return
    static stringToDescription(value: string): string {
        switch (value as WinReconciliationReportType) {
            case WinReconciliationReportType.ExcludePromo:
                return $localize`Excluding Promo`;
            case WinReconciliationReportType.IncludePromo:
                return $localize`Including Promo`;
            case WinReconciliationReportType.OnlyPromo:
                return $localize`Promo Only`;
        }
    }
}
