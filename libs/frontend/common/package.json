{"name": "@tronius/frontend-common", "version": "0.0.1", "sideEffects": false, "devDependencies": {"@jest/globals": "^29.7.0"}, "peerDependencies": {"@angular/animations": "^18.2.9", "@angular/common": "^18.2.9", "@angular/core": "^18.2.9", "@angular/forms": "^18.2.9", "@angular/material": "^18.2.9", "@angular/router": "^18.2.9", "@cms/casino-operator": "file:../../../features/casino-operator/shared", "@cms/client-permissions": "^0.0.1", "@cms/country": "file:../../../features/country/shared", "@cms/customer-statement": "file:../../../features/customer-statement/shared", "@cms/customers": "file:../../../features/customers/shared", "@cms/customers-api-shared": "file:../../../features/customers/api-shared", "@cms/denominations": "file:../../../features/denominations/shared", "@cms/kyc": "file:../../../features/kyc/shared", "@cms/machine-handpays": "file:../../../features/machine-handpays/shared", "@cms/machine-metrics": "file:../../../features/machine-metrics/shared", "@cms/machine-sessions": "file:../../../features/machine-sessions/shared", "@cms/serialization": "file:../../shared/serialization", "@cms/uploads": "file:../../../features/uploads/shared", "@cms/wallets": "file:../../../features/wallets/shared", "@tronius/shared-common": "file:../../shared/common", "@tronius/shared-domain": "file:../../shared/domain", "class-validator": "^0.14.1", "date-fns": "^3.3.1", "rxjs": "^7.8.0"}}