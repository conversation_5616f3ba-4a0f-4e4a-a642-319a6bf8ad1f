import { HttpClient } from '@angular/common/http';
import { DestroyRef, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { NavigationError, Router } from '@angular/router';
import { Environment } from '@tronius/frontend-common';
import { filter, firstValueFrom, from, interval, switchMap, tap } from 'rxjs';

import { ConfirmationDialogComponent, ConfirmationDialogComponentData } from '../confirmation-dialog/confirmation-dialog.component';

@Injectable({
    providedIn: 'root'
})
export class AppUpdateDetectionService {
    private dialogRef?: MatDialogRef<ConfirmationDialogComponent, boolean>;

    constructor(
        private readonly destroyRef: DestroyRef,
        private readonly httpClient: HttpClient,
        private readonly dialog: MatDialog,
        private readonly router: Router
    ) {}

    checkForAppUpdate(): void {
        this.checkForChunkLoadErrors();
        this.periodicallyCheckForEnvConfigUpdate();
    }

    private checkForChunkLoadErrors(): void {
        // Implementation based on:
        // * https://stackoverflow.com/questions/49198268/how-to-overcome-loading-chunk-failed-with-angular-lazy-loaded-modules
        // * https://medium.com/@kamrankhatti/angular-lazy-routes-loading-chunk-failed-42b16c22a377
        //
        // Better solution would be to convert app to PWA and use service worker to handle this.
        //
        // Further reading:
        // * https://senoritadeveloper.medium.com/notify-users-with-the-new-version-of-your-angular-app-56813b24d8e3
        // * https://medium.com/poka-techblog/turn-your-angular-app-into-a-pwa-in-4-easy-steps-543510a9b626
        // * https://angular.dev/ecosystem/service-workers

        this.router.events
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                filter((event) => {
                    if (!(event instanceof NavigationError)) {
                        return false;
                    }
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    const { error } = event;
                    const errorMessagePrefixes = [
                        'error loading dynamically imported module:',
                        'failed to fetch dynamically imported module:'
                    ];

                    const updateDetected =
                        error instanceof Error &&
                        error.name === 'TypeError' &&
                        !!errorMessagePrefixes.find((errorMessagePrefix) => error.message.toLowerCase().startsWith(errorMessagePrefix));

                    return updateDetected;
                })
            )
            .subscribe(() => this.showReloadConfirmationDialog());
    }

    private showReloadConfirmationDialog(): void {
        if (this.dialogRef) {
            return;
        }

        this.dialogRef = this.dialog.open<ConfirmationDialogComponent, ConfirmationDialogComponentData, boolean>(
            ConfirmationDialogComponent,
            {
                data: {
                    title: $localize`Application Update Detected`,
                    content: $localize`To continue click the button below. This will apply the update and reload the application.`,
                    confirmText: 'Update & Reload',
                    confirmIcon: 'refresh',
                    hideRejectButton: true
                },
                disableClose: true
            }
        );

        this.dialogRef
            .afterClosed()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                window.location.reload();
                this.dialogRef = undefined;
            });
    }

    private async loadVersionFromEnvConfigFile(): Promise<string | undefined> {
        const envConfigJsonPath = '/assets/env-config.json';

        const envConfig = await firstValueFrom(this.httpClient.get<Partial<Environment>>(envConfigJsonPath));

        return envConfig.imageTag;
    }

    private periodicallyCheckForEnvConfigUpdate(): void {
        const periodInMs = 60_000; // 1 minute in ms

        let version: string | undefined;

        from(this.loadVersionFromEnvConfigFile())
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                tap((latestVersion) => (version = latestVersion)),
                switchMap(() => interval(periodInMs)),
                switchMap(() => from(this.loadVersionFromEnvConfigFile())),
                filter((latestVersion) => {
                    const updateDetected = latestVersion !== version;
                    version = latestVersion;
                    return updateDetected;
                })
            )
            .subscribe(() => this.showReloadConfirmationDialog());
    }
}
